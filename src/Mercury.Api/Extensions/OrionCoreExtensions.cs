using Orion.SharedKernel.Api;
using Orion.SharedKernel.Application;
using Orion.SharedKernel.Infrastructure;
using Reports.Api;
using Reports.Application;

namespace Mercury.Api.Extensions;

public static class OrionCoreExtensions
{
    public static IServiceCollection AddOrionCore(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddOrionSharedKernelInfrastructure(configuration);

        services.AddOrionSharedKernelApplication();
        
        return services;
    }
}