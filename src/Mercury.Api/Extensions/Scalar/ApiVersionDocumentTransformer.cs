using Microsoft.AspNetCore.Mvc.ApiExplorer;
using Microsoft.AspNetCore.OpenApi;
using Microsoft.OpenApi.Models;

namespace Mercury.Api.Extensions.Scalar;

internal sealed class ApiVersionDocumentTransformer : IOpenApiDocumentTransformer
{
    private const string AppNameSettingsProperty = "AppName";
    
    public Task TransformAsync(OpenApiDocument document, OpenApiDocumentTransformerContext context, CancellationToken cancellationToken)
    {
        var apiVersionDescriptionProvider = context.ApplicationServices
            .GetService<IApiVersionDescriptionProvider>();
        
        var appName = context.ApplicationServices
            .GetService<IConfiguration>()?
            .GetSection(AppNameSettingsProperty)
            .Value;

        var currentVersion = apiVersionDescriptionProvider?.ApiVersionDescriptions
            .DefaultIfEmpty(null)
            .FirstOrDefault();

        if (currentVersion == null && appName == null)
            return Task.CompletedTask;
        
        document.Info.Version = currentVersion!.ApiVersion.ToString();
        document.Info.Title = $"{appName} {currentVersion!.GroupName.ToUpperInvariant()}";
                
        if (currentVersion.IsDeprecated) 
            document.Info.Description = $"{document.Info.Description ?? ""} (Deprecated)";

        return Task.CompletedTask;
    }
}
