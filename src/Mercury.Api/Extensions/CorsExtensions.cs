using Shared.Domain.Configuration;

namespace Mercury.Api.Extensions;

public static class CorsExtensions
{
    public static IServiceCollection AddMercuryCors(this IServiceCollection services, IConfiguration configuration)
    {
        var corsConfig = configuration.GetSection(nameof(CorsConfiguration)).Get<CorsConfiguration>();

        services.AddCors(options =>
        {
            options.AddPolicy(corsConfig?.PolicyName!, corsBuilder =>
            {
                corsBuilder
                    .WithOrigins(corsConfig?.Origins.ToArray()!)
                    .WithMethods(corsConfig?.Methods.ToArray()!)
                    .WithHeaders(corsConfig?.Headers.ToArray()!)
                    .WithExposedHeaders(corsConfig?.ExposedHeaders.ToArray()!);
            });
        });

        return services;
    }

    public static IApplicationBuilder UseMercuryCors(this IApplicationBuilder app, IConfiguration configuration)
    {
        var corsConfig = configuration.GetSection(nameof(CorsConfiguration)).Get<CorsConfiguration>();

        app.UseCors(corsConfig?.PolicyName!);

        return app;
    }
}