{"ConnectionStrings": {"PostgreSql": "{secret}"}, "AppName": "{secret}", "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "CorsConfiguration": {"PolicyName": "MercuryPolicy", "Origins": ["*"], "Methods": ["*"], "Headers": ["*"], "ExposedHeaders": ["*"]}, "JwtConfiguration": {"Secret": "{secret}", "Audience": "{secret}", "Issuer": "{secret}"}, "LegacyApiConfiguration": {"UrlBase": "{secret}", "UnloadingTicketEndpoint": "/api/UnloadingTicket/UnloadingTicket", "GetUnloadingTicketsEndpoint": "/api/v2/companies/{0}/unloadingTickets", "DeleteUnloadingTicketEndpoint": "/api/v2/companies/{0}/unloadingTickets/{1}", "CompanyId": "{secret}"}, "Quartz": {"SchedulerName": "Mercury Reports Scheduler", "MainSchedulerIntervalMinutes": 2}, "BusinessCodesConfiguration": {"AppCode": "02"}, "OrionApiConfiguration": {"UrlBase": "{secret}", "GetDomainValuesByCode": "v1/ddv/domainValues/byDomainCode/{0}", "AuthenticationEndpoint": "v1/security/authentication/login", "MaterialGroupTypeDDVCode": "{secret}", "MaterialPresentationDDVCode": "{secret}", "SyncronizationLoginUser": "{secret}'", "SyncronizationLoginPassword": "{secret}"}, "ActionsConfiguration": {"Actions": {"Security.Integration.Urbetrack.Api.Controllers.v1.AuthenticationController.Authenticate": "00", "Reports.Api.Controllers.v1.Web.CollectionsController.CreateCollections": "01", "Reports.Api.Controllers.v1.Web.RecyclingAreaController.GetAll": "02", "Reports.Api.Controllers.v1.Web.ReportsController.GetReportPreview": "03", "Reports.Api.Controllers.v1.Web.ReportsController.ExportReport": "04", "Reports.Api.Controllers.v1.Web.WeighinsController.CreateWeighing": "05"}}}