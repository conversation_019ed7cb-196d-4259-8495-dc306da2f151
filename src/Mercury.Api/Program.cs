using Mercury.Api;
using Mercury.Api.Extensions;
using Mercury.Api.Extensions.Scalar;
using Orion.Core.PermissionAuthorization.Authorization.Filters;
using Orion.SharedKernel.Api.Middlewares;

var builder = WebApplication.CreateBuilder(args);
{
    builder.Services.AddMercury(builder.Configuration);

    builder.Services.AddControllers(options => options.Filters.Add(typeof(PermissionAuthorizationFilter)));

    builder.Services.AddEndpointsApiExplorer();
    
    builder.Services.AddScalarApi();
    
    builder.AddMercuryLogger(builder.Configuration);
}

var app = builder.Build();
{
    app.UseScalarWithVersioning(app);
    
    app.UseMercuryCors(builder.Configuration);

    app.UseOrionMiddlewares();

    app.UseHttpsRedirection();

    app.UseAuthentication();

    app.UseAuthorization();

    app.MapControllers();

    app.Run();
}