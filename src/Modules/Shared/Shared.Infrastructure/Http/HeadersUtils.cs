using Microsoft.AspNetCore.Http;

namespace Shared.Infrastructure.Http;

public static class HeadersUtils
{
    private const string PageNumberHeader = "X-PageNumber";
    private const string PageSizeHeader = "X-Page-Size";
    private const string TotalRecordsHeader = "X-Total-Records";
    private const string TotalPagesHeader = "X-Total-Pages";

    /// <summary>
    /// Establece headers de respuesta para paginación.
    /// </summary>
    /// <param name="httpContext">Contexto HTTP actual</param>
    /// <param name="totalRecords">Total de registros</param>
    /// <param name="pageNumber">Número de página actual</param>
    /// <param name="pageSize">Tamaño de página</param>
    public static void SetResponseHeader(HttpContext httpContext, int totalRecords, int pageNumber, int pageSize)
    {
        if (httpContext?.Response?.Headers == null)
            return;

        var totalPages = (int)Math.Ceiling((double)totalRecords / pageSize);

        httpContext.Response.Headers.Append(TotalRecordsHeader, totalRecords.ToString());
        httpContext.Response.Headers.Append(PageNumberHeader, pageNumber.ToString());
        httpContext.Response.Headers.Append(PageSizeHeader, pageSize.ToString());
        httpContext.Response.Headers.Append(TotalPagesHeader, totalPages.ToString());
    }

    /// <summary>
    /// Establece header de respuesta para el total de registros solamente.
    /// </summary>
    /// <param name="httpContext">Contexto HTTP actual</param>
    /// <param name="totalRecords">Total de registros</param>
    public static void SetResponseHeader(HttpContext httpContext, int totalRecords)
    {
        if (httpContext?.Response?.Headers == null)
            return;

        httpContext.Response.Headers.Append(TotalRecordsHeader, totalRecords.ToString());
    }

    /// <summary>
    /// Establece un header específico en la respuesta.
    /// </summary>
    /// <param name="httpContext">Contexto HTTP actual</param>
    /// <param name="headerName">Nombre del header</param>
    /// <param name="headerValue">Valor del header</param>
    public static void SetResponseHeader(HttpContext httpContext, string headerName, string headerValue)
    {
        if (httpContext?.Response?.Headers == null || string.IsNullOrEmpty(headerName))
            return;

        httpContext.Response.Headers.Append(headerName, headerValue);
    }

    /// <summary>
    /// Obtiene parámetros de paginación desde los headers de la petición.
    /// </summary>
    /// <param name="httpContext">Contexto HTTP actual</param>
    /// <returns>Tupla con número de página y tamaño de página</returns>
    public static (int PageNumber, int PageSize) GetRequestHeaders(HttpContext httpContext)
    {
        if (httpContext?.Request?.Headers == null)
            return (1, 25);

        var pageNumber = 1;
        if (httpContext.Request.Headers.TryGetValue(PageNumberHeader, out var pageNumberValue) &&
            int.TryParse(pageNumberValue.FirstOrDefault(), out var parsedPageNumber) && parsedPageNumber > 0)
        {
            pageNumber = parsedPageNumber;
        }

        var pageSize = 25;
        if (httpContext.Request.Headers.TryGetValue(PageSizeHeader, out var pageSizeValue) &&
            int.TryParse(pageSizeValue.FirstOrDefault(), out var parsedPageSize) && parsedPageSize > 0)
        {
            pageSize = parsedPageSize;
        }

        return (pageNumber, pageSize);
    }

    /// <summary>
    /// Obtiene valor específico desde header de la petición.
    /// </summary>
    /// <param name="httpContext">Contexto HTTP actual</param>
    /// <param name="headerName">Nombre del header</param>
    /// <returns>Valor del header o null si no existe</returns>
    public static string? GetRequestHeader(HttpContext httpContext, string headerName)
    {
        if (httpContext?.Request?.Headers == null || string.IsNullOrEmpty(headerName))
            return null;

        return httpContext.Request.Headers.TryGetValue(headerName, out var headerValue)
            ? headerValue.FirstOrDefault()
            : null;
    }
}
