using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Tokens;
using Shared.Domain.Configuration;
using System.Text;


namespace Shared.Infrastructure.Extensions
{
    public static class JwtExtension 
    {
        public static IServiceCollection AddJWT(this IServiceCollection services, IConfiguration configuration)
        {
            JwtConfiguration jwtConfiguration = configuration.GetSection(nameof(JwtConfiguration)).Get<JwtConfiguration>()!;

            SymmetricSecurityKey key = new(Encoding.UTF8.GetBytes(jwtConfiguration.Secret));

            TokenValidationParameters validationParameters = new()
            {
                ValidateIssuer = true,
                ValidIssuer = jwtConfiguration.Issuer,
                ValidateAudience = true,
                ValidAudience = jwtConfiguration.Audience,
                ValidateLifetime = true,
                IssuerSigningKey = key
            };

            services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
                .AddJwtBearer(options =>
                {
                    options.RequireHttpsMetadata = false;
                    options.SaveToken = true;
                    options.IncludeErrorDetails = true;
                    options.TokenValidationParameters = validationParameters;
                });

            return services;
        }
    }
}
