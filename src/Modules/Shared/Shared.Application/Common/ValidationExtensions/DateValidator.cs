using System.Globalization;
using FluentValidation;

namespace Shared.Application.Common.ValidationExtensions;

public static class DateValidator
{
    private const string DateFormat = "yyyy-MM-dd";
    private const string TimeFormat = "HH:mm:ss";
    private const string DateTimeFormat = $"{DateFormat} {TimeFormat}";
    
    public static IRuleBuilderOptions<T, string?> IsValidDate<T>(this IRuleBuilder<T, string?> ruleBuilder)
    {
        return ruleBuilder.Must(value =>
            DateTime.TryParseExact(value, DateFormat, CultureInfo.InvariantCulture, DateTimeStyles.None, out _));
    }
    
    public static IRuleBuilderOptions<T, string?> IsValidDateWithTime<T>(this IRuleBuilder<T, string?> ruleBuilder)
    {
        return ruleBuilder.Must(value =>
            DateTime.TryParseExact(value, DateTimeFormat, CultureInfo.InvariantCulture, DateTimeStyles.None, out _));
    }

    public static IRuleBuilderOptions<T, string?> IsValidDateWithTimeOrNull<T>(this IRuleBuilder<T, string?> ruleBuilder)
    {
        return ruleBuilder.Must(value => string.IsNullOrEmpty(value)
            || DateTime.TryParseExact(value, DateTimeFormat, CultureInfo.InvariantCulture, DateTimeStyles.None, out _));
    }
}