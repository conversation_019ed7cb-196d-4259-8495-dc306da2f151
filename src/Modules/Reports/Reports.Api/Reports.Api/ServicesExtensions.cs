using Reports.Application;
using Reports.Domain.Configurations;
using Reports.Infrastructure;

namespace Reports.Api;

public static class ServicesExtensions
{
    public static IServiceCollection AddReportsModule(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddReportsOptions(configuration);
        services.AddReportsInfrastructure(configuration);
        services.AddReportsApplication();

        return services;
    }
    
    private static IServiceCollection AddReportsOptions(this IServiceCollection services, IConfiguration configuration)
    {
        services.Configure<LegacyApiConfiguration>(configuration.GetSection(nameof(LegacyApiConfiguration)));

        return services;
    }
}