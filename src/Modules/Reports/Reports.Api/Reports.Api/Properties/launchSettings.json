{"$schema": "https://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:39905", "sslPort": 44313}}, "profiles": {"Reports.Api": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": false, "launchUrl": "scalar", "applicationUrl": "https://localhost:7202;http://localhost:5013", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": false, "launchUrl": "scalar", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}}}