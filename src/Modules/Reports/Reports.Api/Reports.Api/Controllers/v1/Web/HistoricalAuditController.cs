using System.Net;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Orion.SharedKernel.Api.Controllers;
using Orion.SharedKernel.Domain.Entities.Error;
using Reports.Application.DTOs.HistoricalAudit;
using Reports.Application.Features.Web.Queries.HistoricalAudit.GetHistoricalAuditByTicket;
using Reports.Application.Features.Web.Queries.HistoricalAudit.GetHistoricalAudits;

namespace Reports.Api.Controllers.v1.Web;

/// <summary>
/// Controlador para consultar el historial de auditoría
/// </summary>
[ApiVersion("1.0")]
[Authorize]
public class HistoricalAuditController : OrionController
{
    /// <summary>
    /// Obtiene el historial de auditoría de un ticket específico.
    /// </summary>
    /// <param name="idTicket">id de ticket</param>
    [ProducesResponseType((int)HttpStatusCode.OK, Type = typeof(List<HistoricalAuditItemResponseDto>))]
    [ProducesResponseType((int)HttpStatusCode.Conflict, Type = typeof(Error))]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [HttpGet("historic/{idTicket}")]
    public async Task<IActionResult> GetHistoricalAuditByTicket(string idTicket)
    {
        var request = new GetHistoricalAuditByTicketRequest
        {
            IdTicket = idTicket
        };

        var response = await Mediator.Send(request);

        return Ok(response.Historic);
    }

    /// <summary>
    /// Obtiene múltiples registros históricos de auditoría con filtros opcionales.
    /// </summary>
    [ProducesResponseType((int)HttpStatusCode.OK, Type = typeof(List<HistoricalAuditItemResponseDto>))]
    [ProducesResponseType((int)HttpStatusCode.Conflict, Type = typeof(Error))]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [HttpGet]
    public async Task<IActionResult> GetAll([FromQuery] GetHistoricalAuditsRequest request)
    {
        var response = await Mediator.Send(request);

        return Ok(response.Historic);
    }
}
