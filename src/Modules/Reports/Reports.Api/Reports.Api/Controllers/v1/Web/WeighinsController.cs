using System.Net;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Orion.SharedKernel.Api.Controllers;
using Orion.SharedKernel.Domain.Entities;
using Orion.SharedKernel.Domain.Entities.Error;
using Reports.Application.DTOs.Weighings;
using Reports.Application.Features.Web.Commands.Weighins.CreateWeighing;
using Reports.Application.Features.Web.Queries.Weighins.GetWeighins;

namespace Reports.Api.Controllers.v1.Web;

[ApiVersion("1.0")]
[Authorize]
public class WeighinsController : OrionController
{
    /// <summary>
    /// Crea pesajes de vehiculos en base a los datos enviado por la balanza.
    /// </summary>
    /// <param name="requestDto">Datos del pesaje.</param>
    [ProducesResponseType((int)HttpStatusCode.OK, Type = typeof(List<CreateWeighingResponseDto>))]
    [ProducesResponseType((int)HttpStatusCode.Conflict, Type = typeof(Error))]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [HttpPost]
    public async Task<IActionResult> CreateWeighing([FromBody] List<CreateWeighingRequestDto> requestDto)
    {
        var request = new CreateWeighingRequest(requestDto);

        var response = await Mediator.Send<CreateWeighingResponse>(request);

        return Ok(response.Weighin);
    }

    /// <summary>
    /// Obtiene los pesajes segun los filtros ingresados de forma paginada.
    /// </summary>
    /// <param name="request"></param>
    [ProducesResponseType((int)HttpStatusCode.OK, Type = typeof(PaginatedResult<GetWeighingItemResponseDto>))]
    [ProducesResponseType((int)HttpStatusCode.Conflict, Type = typeof(Error))]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [HttpGet]
    public async Task<IActionResult> GetWeighins([FromQuery] GetWeighinsRequest request)
    {
        var response = await Mediator.Send(request);

        return Ok(response.Weighins);
    }
}