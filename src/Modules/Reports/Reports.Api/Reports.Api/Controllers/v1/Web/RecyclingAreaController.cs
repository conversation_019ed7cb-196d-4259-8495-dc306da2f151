using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Orion.SharedKernel.Api.Controllers;
using Reports.Application.DTOs.RecyclingArea;
using Reports.Application.Features.Web.Queries.RecyclingAreas.GetAllRecyclingAreas;
using System.Net;
using Orion.SharedKernel.Domain.Entities.Error;

namespace Reports.Api.Controllers.v1.Web;

[ApiVersion("1.0")]
[Authorize]
public class RecyclingAreaController : OrionController
{
    /// <summary>
    /// Obtiene la lista completa de áreas de aprovechamiento
    /// </summary>
    [ProducesResponseType((int)HttpStatusCode.OK, Type = typeof(List<GetAllRecyclingAreaResponseDto>))]
    [ProducesResponseType((int)HttpStatusCode.Conflict, Type = typeof(Error))]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [HttpGet]
    public async Task<IActionResult> GetAll()
    {
        var request = new GetAllRecyclingAreaRequest();

        var response = await Mediator.Send(request);

        return Ok(response.RecyclingAreas);
    }
}