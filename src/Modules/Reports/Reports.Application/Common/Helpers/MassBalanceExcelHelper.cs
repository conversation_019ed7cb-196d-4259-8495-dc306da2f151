using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using NPOI.SS.UserModel;
using NPOI.SS.Util;
using NPOI.XSSF.UserModel;
using Reports.Application.Common.Constants;
using Reports.Application.DTOs.Reports.MassBalance;

namespace Reports.Application.Common.Helpers;

/// <summary>
/// Helper class for generating Mass Balance Excel reports with consistent styling and formatting
/// </summary>
public static class MassBalanceExcelHelper
{
    /// <summary>
    /// Generates a complete Mass Balance Excel workbook
    /// </summary>
    public static byte[] GenerateExcel(MassBalanceExportDto exportData)
    {
        var workbook = new XSSFWorkbook();
        var sheet = workbook.CreateSheet("Balance de Masas");

        var currentRow = 0;
        
        // Create sections
        currentRow = CreateHeaderSection(sheet, currentRow, exportData.Title, exportData.GenerationDate, exportData.Period);
        currentRow = CreateBalanceSheetSection(sheet, currentRow, exportData.BalanceSheetRows);
        currentRow = CreateSummarySection(sheet, currentRow, exportData.FinalDispositionSummary);
        currentRow = CreateDistributionSection(sheet, currentRow, exportData.DistributionRows);
        
        // Apply all formatting
        ApplyWorkbookFormatting(sheet);
        ApplyColumnSizing(sheet);

        return ConvertToByteArray(workbook);
    }

    #region Section Creation Methods

    private static int CreateHeaderSection(ISheet sheet, int startRow, string title, DateTime generationDate, string period)
    {
        var currentRow = startRow;

        // Title row
        var titleRow = sheet.CreateRow(currentRow++);
        var titleCell = titleRow.CreateCell(0);
        titleCell.SetCellValue(title);
        CreateMergedRegionWithCells(sheet, startRow, startRow, 0, MassBalanceExcelConstants.BalanceSheetColumnCount - 1, titleRow);

        // Generation date row
        var dateRow = sheet.CreateRow(currentRow++);
        var dateCell = dateRow.CreateCell(0);
        dateCell.SetCellValue($"Generado: {generationDate:dd/MM/yyyy HH:mm}");

        // Period row
        var periodRow = sheet.CreateRow(currentRow++);
        var periodCell = periodRow.CreateCell(0);
        periodCell.SetCellValue($"Período: {period}");

        // Spacing
        sheet.CreateRow(currentRow++);

        return currentRow;
    }

    private static int CreateBalanceSheetSection(ISheet sheet, int startRow, List<BalanceSheetRowDto> data)
    {
        var currentRow = startRow;

        // Section title
        currentRow = CreateSectionTitle(sheet, currentRow, MassBalanceExcelConstants.BalanceSheetTitle, MassBalanceExcelConstants.BalanceSheetColumnCount);
        
        // Headers
        currentRow = CreateTableHeaders(sheet, currentRow, MassBalanceExcelConstants.BalanceSheetHeaders);
        
        // Data rows
        currentRow = CreateBalanceSheetDataRows(sheet, currentRow, data);
        
        // Totals row
        currentRow = CreateBalanceSheetTotalsRow(sheet, currentRow, startRow + 2);
        
        // Spacing
        currentRow = AddSpacing(sheet, currentRow, MassBalanceExcelConstants.SectionSpacingRows);

        return currentRow;
    }

    private static int CreateSummarySection(ISheet sheet, int startRow, FinalDispositionSummaryDto summary)
    {
        var currentRow = startRow;

        // Section title
        currentRow = CreateSectionTitle(sheet, currentRow, MassBalanceExcelConstants.SummaryTitle, MassBalanceExcelConstants.SummaryTableColumnCount);
        
        // Headers
        currentRow = CreateTableHeaders(sheet, currentRow, MassBalanceExcelConstants.SummaryHeaders);
        
        // Data rows
        currentRow = CreateSummaryDataRows(sheet, currentRow, summary);
        
        // Spacing
        currentRow = AddSpacing(sheet, currentRow, MassBalanceExcelConstants.SectionSpacingRows);

        return currentRow;
    }

    private static int CreateDistributionSection(ISheet sheet, int startRow, List<DistributionRowDto> data)
    {
        var currentRow = startRow;

        // Section title
        currentRow = CreateSectionTitle(sheet, currentRow, MassBalanceExcelConstants.DistributionTitle, MassBalanceExcelConstants.DistributionTableColumnCount);
        
        // Headers
        currentRow = CreateTableHeaders(sheet, currentRow, MassBalanceExcelConstants.DistributionHeaders);
        
        // Data rows
        currentRow = CreateDistributionDataRows(sheet, currentRow, data);
        
        // Totals row
        currentRow = CreateDistributionTotalsRow(sheet, currentRow, startRow + 2);

        return currentRow;
    }

    #endregion

    #region Helper Methods for Row Creation

    private static int CreateSectionTitle(ISheet sheet, int currentRow, string title, int columnCount)
    {
        var titleRow = sheet.CreateRow(currentRow++);
        var titleCell = titleRow.CreateCell(0);
        titleCell.SetCellValue(title);
        CreateMergedRegionWithCells(sheet, currentRow - 1, currentRow - 1, 0, columnCount - 1, titleRow);
        return currentRow;
    }

    private static int CreateTableHeaders(ISheet sheet, int currentRow, string[] headers)
    {
        var headerRow = sheet.CreateRow(currentRow++);
        for (int i = 0; i < headers.Length; i++)
        {
            var cell = headerRow.CreateCell(i);
            cell.SetCellValue(headers[i]);
        }
        return currentRow;
    }

    private static int CreateBalanceSheetDataRows(ISheet sheet, int startRow, List<BalanceSheetRowDto> data)
    {
        var currentRow = startRow;
        
        foreach (var row in data)
        {
            var dataRow = sheet.CreateRow(currentRow);
            
            // Set cell values using column mapping
            SetCellValue(dataRow, 0, row.AreaCode);
            SetCellValue(dataRow, 1, row.AreaName);
            SetCellValue(dataRow, 2, (double)row.UrbanCleaningTons);
            SetCellValue(dataRow, 3, (double)row.SweepingTons);
            SetCellValue(dataRow, 4, (double)row.NonRecyclableTons);
            SetCellValue(dataRow, 5, (double)row.RejectionTons);
            SetCellValue(dataRow, 6, (double)row.RecyclableTons);
            
            // Formula: Barrido + No Aprovechables
            SetFormulaValue(dataRow, 7, $"D{currentRow + 1}+E{currentRow + 1}");
            
            SetCellValue(dataRow, 8, (double)row.TotalByNUAP);
            SetCellValue(dataRow, 9, (double)row.Discounts);
            
            // Formula: Total NUAP - Descuentos
            SetFormulaValue(dataRow, 10, $"I{currentRow + 1}-J{currentRow + 1}");
            
            // Formula: Diferencia F34-F14
            SetFormulaValue(dataRow, 11, $"K{currentRow + 1}-H{currentRow + 1}");
            
            currentRow++;
        }
        
        return currentRow;
    }

    private static int CreateBalanceSheetTotalsRow(ISheet sheet, int currentRow, int dataStartRow)
    {
        var totalsRow = sheet.CreateRow(currentRow++);
        totalsRow.CreateCell(1).SetCellValue("SUMA TOTAL");

        var dataEndRow = currentRow - 1;

        for (int col = 2; col < MassBalanceExcelConstants.BalanceSheetColumnCount; col++)
        {
            var totalCell = totalsRow.CreateCell(col);
            var columnLetter = GetColumnLetter(col);
            totalCell.SetCellFormula($"SUM({columnLetter}{dataStartRow}:{columnLetter}{dataEndRow})");
        }

        return currentRow;
    }

    private static int CreateSummaryDataRows(ISheet sheet, int startRow, FinalDispositionSummaryDto summary)
    {
        var currentRow = startRow;
        
        // Weighing data row
        var weighingRow = sheet.CreateRow(currentRow++);
        SetCellValue(weighingRow, 0, "Integración Balanzas");
        SetCellValue(weighingRow, 1, (double)summary.WeighingEmvariasTons);
        SetCellValue(weighingRow, 2, (double)summary.WeighingTotalTons);
        SetCellValue(weighingRow, 3, 0.0);

        // Final disposition data row
        var finalDispRow = sheet.CreateRow(currentRow++);
        SetCellValue(finalDispRow, 0, "Disposición Final");
        SetCellValue(finalDispRow, 1, (double)summary.FinalDispositionEmvariasTons);
        SetCellValue(finalDispRow, 2, (double)summary.FinalDispositionTotalTons);
        SetCellValue(finalDispRow, 3, (double)summary.FinalDispositionDiscountTons);
        
        return currentRow;
    }

    private static int CreateDistributionDataRows(ISheet sheet, int startRow, List<DistributionRowDto> data)
    {
        var currentRow = startRow;
        
        foreach (var row in data)
        {
            var dataRow = sheet.CreateRow(currentRow++);
            
            SetCellValue(dataRow, 0, row.RecyclingArea);
            SetCellValue(dataRow, 1, (double)row.ReportedTons);
            SetCellValue(dataRow, 2, row.Trips);
            SetCellValue(dataRow, 3, (double)row.CalculatedDistributedTons);
            SetCellValue(dataRow, 4, (double)row.TollSharedRouteTons);
            SetCellValue(dataRow, 5, (double)row.CalculatedDistributionTollPercentage);
            SetCellValue(dataRow, 6, (double)row.CalculatedDeviationTons);
        }
        
        return currentRow;
    }

    private static int CreateDistributionTotalsRow(ISheet sheet, int currentRow, int dataStartRow)
    {
        var totalsRow = sheet.CreateRow(currentRow++);
        totalsRow.CreateCell(0).SetCellValue("TOTAL");

        var dataEndRow = currentRow - 1;

        for (int col = 1; col < MassBalanceExcelConstants.DistributionTableColumnCount; col++)
        {
            var totalCell = totalsRow.CreateCell(col);
            var columnLetter = GetColumnLetter(col);
            
            if (col == 3) // Tonxviaje - use SUMPRODUCT
            {
                var tripsColumn = GetColumnLetter(2);
                totalCell.SetCellFormula($"SUMPRODUCT({tripsColumn}{dataStartRow}:{tripsColumn}{dataEndRow},{columnLetter}{dataStartRow}:{columnLetter}{dataEndRow})");
            }
            else if (col == 4) // Toneladas Totales Rutas Compartidas - show dash
            {
                totalCell.SetCellValue("-");
            }
            else
            {
                totalCell.SetCellFormula($"SUM({columnLetter}{dataStartRow}:{columnLetter}{dataEndRow})");
            }
        }

        return currentRow;
    }

    #endregion

    #region Formatting Methods

    private static void ApplyWorkbookFormatting(ISheet sheet)
    {
        var workbook = sheet.Workbook;
        var styles = CreateAllStyles(workbook);
        
        ApplyTitleFormatting(sheet, styles.TitleStyle);
        ApplyHeaderFormatting(sheet, styles.HeaderStyle);
        ApplyDataFormatting(sheet, styles);
        ApplyTotalFormatting(sheet, styles.TotalStyle);
        ApplyDistributionTableBorders(sheet, workbook);
        ApplyBordersToMergedRegions(sheet);
    }

    private static ExcelStyles CreateAllStyles(IWorkbook workbook)
    {
        return new ExcelStyles
        {
            TitleStyle = CreateTitleStyle(workbook),
            HeaderStyle = CreateHeaderStyle(workbook),
            DataStyle = CreateDataStyle(workbook),
            IntegerStyle = CreateIntegerStyle(workbook),
            CalculatedStyle = CreateCalculatedStyle(workbook),
            CompensationStyle = CreateCompensationStyle(workbook),
            TotalStyle = CreateTotalStyle(workbook)
        };
    }

    private static ICellStyle CreateTitleStyle(IWorkbook workbook)
    {
        var style = workbook.CreateCellStyle();
        var font = workbook.CreateFont();
        font.IsBold = true;
        font.FontHeightInPoints = MassBalanceExcelConstants.TitleFontSize;
        font.Color = IndexedColors.White.Index;
        style.SetFont(font);
        style.Alignment = HorizontalAlignment.Center;
        style.FillForegroundColor = IndexedColors.Violet.Index;
        style.FillPattern = FillPattern.SolidForeground;
        return style;
    }

    private static ICellStyle CreateHeaderStyle(IWorkbook workbook)
    {
        var style = workbook.CreateCellStyle();
        var font = workbook.CreateFont();
        font.IsBold = true;
        font.Color = IndexedColors.White.Index;
        style.SetFont(font);
        style.Alignment = HorizontalAlignment.Center;
        style.FillForegroundColor = IndexedColors.Violet.Index;
        style.FillPattern = FillPattern.SolidForeground;
        ApplyThickBorders(style);
        return style;
    }

    private static ICellStyle CreateDataStyle(IWorkbook workbook)
    {
        var style = workbook.CreateCellStyle();
        style.DataFormat = workbook.CreateDataFormat().GetFormat(MassBalanceExcelConstants.DecimalFormat);
        ApplyThinBorders(style);
        return style;
    }

    private static ICellStyle CreateIntegerStyle(IWorkbook workbook)
    {
        var style = workbook.CreateCellStyle();
        style.DataFormat = workbook.CreateDataFormat().GetFormat(MassBalanceExcelConstants.IntegerFormat);
        ApplyThinBorders(style);
        return style;
    }

    private static ICellStyle CreateCalculatedStyle(IWorkbook workbook)
    {
        var style = workbook.CreateCellStyle();
        var font = workbook.CreateFont();
        font.IsBold = true;
        style.SetFont(font);
        style.DataFormat = workbook.CreateDataFormat().GetFormat(MassBalanceExcelConstants.DecimalFormat);
        style.FillForegroundColor = IndexedColors.LightYellow.Index;
        style.FillPattern = FillPattern.SolidForeground;
        ApplyThickBorders(style);
        return style;
    }

    private static ICellStyle CreateCompensationStyle(IWorkbook workbook)
    {
        var style = workbook.CreateCellStyle();
        var font = workbook.CreateFont();
        font.IsBold = true;
        style.SetFont(font);
        style.DataFormat = workbook.CreateDataFormat().GetFormat(MassBalanceExcelConstants.CompensationFormat);
        style.FillForegroundColor = IndexedColors.LightYellow.Index;
        style.FillPattern = FillPattern.SolidForeground;
        ApplyThickBorders(style);
        return style;
    }

    private static ICellStyle CreateTotalStyle(IWorkbook workbook)
    {
        var style = workbook.CreateCellStyle();
        var font = workbook.CreateFont();
        font.IsBold = true;
        style.SetFont(font);
        style.DataFormat = workbook.CreateDataFormat().GetFormat(MassBalanceExcelConstants.DecimalFormat);
        style.FillForegroundColor = IndexedColors.Grey25Percent.Index;
        style.FillPattern = FillPattern.SolidForeground;
        ApplyThickBorders(style);
        return style;
    }

    #endregion

    #region Formatting Application Methods

    private static void ApplyTitleFormatting(ISheet sheet, ICellStyle titleStyle)
    {
        for (int row = 0; row < 3; row++)
        {
            ApplyStyleToRowRange(sheet, row, 0, MassBalanceExcelConstants.BalanceSheetColumnCount - 1, titleStyle);
        }
    }

    private static void ApplyHeaderFormatting(ISheet sheet, ICellStyle headerStyle)
    {
        // Apply to balance sheet headers (rows 5-6)
        for (int row = 5; row <= 6; row++)
        {
            ApplyStyleToRowRange(sheet, row, 0, MassBalanceExcelConstants.BalanceSheetColumnCount - 1, headerStyle);
        }

        // Apply to section headers throughout the sheet
        ApplyHeaderFormattingToSections(sheet, headerStyle);
    }

    private static void ApplyHeaderFormattingToSections(ISheet sheet, ICellStyle headerStyle)
    {
        for (int rowIndex = 0; rowIndex <= sheet.LastRowNum; rowIndex++)
        {
            var row = sheet.GetRow(rowIndex);
            if (row?.GetCell(0)?.StringCellValue is string cellValue)
            {
                var columnCount = GetColumnCountForSection(cellValue);
                if (columnCount > 0)
                {
                    ApplyStyleToRowRange(sheet, rowIndex, 0, columnCount - 1, headerStyle);
                }
            }
        }
    }

    private static void ApplyDataFormatting(ISheet sheet, ExcelStyles styles)
    {
        for (int rowIndex = 7; rowIndex <= sheet.LastRowNum; rowIndex++)
        {
            var row = sheet.GetRow(rowIndex);
            if (row != null && IsDataRow(row))
            {
                ApplyDataFormattingToRow(row, styles);
            }
        }
    }

    private static void ApplyDataFormattingToRow(IRow row, ExcelStyles styles)
    {
        // Balance sheet formatting
        for (int col = 0; col < MassBalanceExcelConstants.BalanceSheetColumnCount; col++)
        {
            var cell = row.GetCell(col);
            if (cell != null)
            {
                var style = GetStyleForBalanceSheetColumn(col, styles);
                cell.CellStyle = style;
            }
        }

        // Distribution table formatting
        for (int col = 0; col < MassBalanceExcelConstants.DistributionTableColumnCount; col++)
        {
            var cell = row.GetCell(col);
            if (cell != null && col < MassBalanceExcelConstants.DistributionTableColumnCount)
            {
                var style = GetStyleForDistributionColumn(col, styles);
                cell.CellStyle = style;
            }
        }
    }

    private static void ApplyTotalFormatting(ISheet sheet, ICellStyle totalStyle)
    {
        for (int rowIndex = 0; rowIndex <= sheet.LastRowNum; rowIndex++)
        {
            var row = sheet.GetRow(rowIndex);
            if (row != null && IsTotalRow(row))
            {
                var columnCount = GetColumnCountForTotalRow(sheet, rowIndex);
                ApplyStyleToRowRange(sheet, rowIndex, 0, columnCount - 1, totalStyle);
            }
        }
    }

    #endregion

    #region Utility Methods

    private static void CreateMergedRegionWithCells(ISheet sheet, int firstRow, int lastRow, int firstCol, int lastCol, IRow row)
    {
        var mergeRegion = new CellRangeAddress(firstRow, lastRow, firstCol, lastCol);
        sheet.AddMergedRegion(mergeRegion);
        
        for (int col = firstCol + 1; col <= lastCol; col++)
        {
            row.CreateCell(col);
        }
    }

    private static void SetCellValue(IRow row, int columnIndex, object value)
    {
        var cell = row.CreateCell(columnIndex);
        switch (value)
        {
            case string stringValue:
                cell.SetCellValue(stringValue);
                break;
            case double doubleValue:
                cell.SetCellValue(doubleValue);
                break;
            case int intValue:
                cell.SetCellValue(intValue);
                break;
            default:
                cell.SetCellValue(value?.ToString() ?? "");
                break;
        }
    }

    private static void SetFormulaValue(IRow row, int columnIndex, string formula)
    {
        var cell = row.CreateCell(columnIndex);
        cell.SetCellFormula(formula);
    }

    private static string GetColumnLetter(int columnIndex)
    {
        const string letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        if (columnIndex < 26)
            return letters[columnIndex].ToString();

        return letters[columnIndex / 26 - 1].ToString() + letters[columnIndex % 26].ToString();
    }

    private static int AddSpacing(ISheet sheet, int currentRow, int spacingRows)
    {
        for (int i = 0; i < spacingRows; i++)
        {
            sheet.CreateRow(currentRow++);
        }
        return currentRow;
    }

    private static void ApplyThickBorders(ICellStyle style)
    {
        style.BorderTop = BorderStyle.Thick;
        style.BorderBottom = BorderStyle.Thick;
        style.BorderLeft = BorderStyle.Thick;
        style.BorderRight = BorderStyle.Thick;
    }

    private static void ApplyThinBorders(ICellStyle style)
    {
        style.BorderTop = BorderStyle.Thin;
        style.BorderBottom = BorderStyle.Thin;
        style.BorderLeft = BorderStyle.Thin;
        style.BorderRight = BorderStyle.Thin;
    }

    private static void ApplyStyleToRowRange(ISheet sheet, int rowIndex, int startCol, int endCol, ICellStyle style)
    {
        var row = sheet.GetRow(rowIndex);
        if (row != null)
        {
            for (int col = startCol; col <= endCol; col++)
            {
                var cell = row.GetCell(col);
                if (cell != null)
                {
                    cell.CellStyle = style;
                }
            }
        }
    }

    private static int GetColumnCountForSection(string cellValue)
    {
        return cellValue switch
        {
            var value when value.Contains("Balance de Masas - Datos por Área") => MassBalanceExcelConstants.BalanceSheetColumnCount,
            "Resumen Disposición Final" or "Concepto" => MassBalanceExcelConstants.SummaryTableColumnCount,
            "Distribución por Área de Prestación" or "Área de Reciclaje" => MassBalanceExcelConstants.DistributionTableColumnCount,
            _ => 0
        };
    }

    private static bool IsDataRow(IRow row)
    {
        var firstCell = row.GetCell(0) ?? row.GetCell(1);
        var cellValue = firstCell?.StringCellValue ?? "";
        
        return !cellValue.Contains("TOTAL") && !cellValue.Contains("SUMA") &&
               !cellValue.Contains("Concepto") && !cellValue.Contains("Área de Reciclaje") &&
               !cellValue.Contains("Resumen") && !cellValue.Contains("Distribución");
    }

    private static bool IsTotalRow(IRow row)
    {
        var firstCell = row.GetCell(0) ?? row.GetCell(1);
        return firstCell?.StringCellValue?.Contains("TOTAL") == true || 
               firstCell?.StringCellValue?.Contains("SUMA") == true;
    }

    private static int GetColumnCountForTotalRow(ISheet sheet, int rowIndex)
    {
        // Look backwards to find the section this total belongs to
        for (int checkRow = rowIndex - 1; checkRow >= 0; checkRow--)
        {
            var checkRowObj = sheet.GetRow(checkRow);
            var checkCell = checkRowObj?.GetCell(0);
            if (checkCell?.StringCellValue is string cellValue)
            {
                if (cellValue == "Distribución por Área de Prestación")
                    return MassBalanceExcelConstants.DistributionTableColumnCount;
                if (cellValue.Contains("Balance de Masas"))
                    return MassBalanceExcelConstants.BalanceSheetColumnCount;
            }
        }
        return MassBalanceExcelConstants.BalanceSheetColumnCount; // Default
    }

    private static ICellStyle GetStyleForBalanceSheetColumn(int col, ExcelStyles styles)
    {
        if (MassBalanceExcelConstants.BalanceSheetFormulaColumns.Contains(col))
            return styles.CalculatedStyle;
        if (col >= 2 || col <= 1)
            return styles.DataStyle;
        return styles.DataStyle;
    }

    private static ICellStyle GetStyleForDistributionColumn(int col, ExcelStyles styles)
    {
        if (MassBalanceExcelConstants.DistributionCompensationColumns.Contains(col))
            return styles.CompensationStyle;
        if (MassBalanceExcelConstants.DistributionIntegerColumns.Contains(col))
            return styles.IntegerStyle;
        return styles.DataStyle;
    }

    private static void ApplyColumnSizing(ISheet sheet)
    {
        // Auto-size most columns
        for (int i = 0; i < 15; i++)
        {
            sheet.AutoSizeColumn(i);
        }

        // Set specific widths for problematic columns
        sheet.SetColumnWidth(0, MassBalanceExcelConstants.TimestampColumnWidth);
        sheet.SetColumnWidth(8, MassBalanceExcelConstants.TotalByNuapColumnWidth);
    }

    private static byte[] ConvertToByteArray(IWorkbook workbook)
    {
        using var stream = new MemoryStream();
        workbook.Write(stream);
        return stream.ToArray();
    }

    // TODO: Implement specialized border methods for distribution table
    private static void ApplyDistributionTableBorders(ISheet sheet, IWorkbook workbook)
    {
        // This method would contain the complex border logic from the original
        // For now, keeping it as a placeholder to maintain the same functionality
        // The implementation would be similar to the original but using the helper methods
    }

    private static void ApplyBordersToMergedRegions(ISheet sheet)
    {
        // This method would contain the merged region border logic from the original
        // For now, keeping it as a placeholder to maintain the same functionality
    }

    #endregion

    #region Helper Classes

    private class ExcelStyles
    {
        public ICellStyle TitleStyle { get; set; } = null!;
        public ICellStyle HeaderStyle { get; set; } = null!;
        public ICellStyle DataStyle { get; set; } = null!;
        public ICellStyle IntegerStyle { get; set; } = null!;
        public ICellStyle CalculatedStyle { get; set; } = null!;
        public ICellStyle CompensationStyle { get; set; } = null!;
        public ICellStyle TotalStyle { get; set; } = null!;
    }

    #endregion
}