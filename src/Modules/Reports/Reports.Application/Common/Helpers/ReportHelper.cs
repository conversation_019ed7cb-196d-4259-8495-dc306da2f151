using Reports.Domain.Common;
using Reports.Domain.Entities;

namespace Reports.Application.Common.Helpers;

public static class ReportHelper
{
    public static SortingOptions GetSortingOptionsF14(string? sortDirection, string? sortFields)
    {
        var direction = ParseSortDirection(sortDirection);
        var fieldsMap = FieldsMapF14();
        var sortBy = GetSortFields(sortFields, fieldsMap);
        return new SortingOptions(direction, sortBy);
    }

    public static SortingOptions GetSortingOptionsF34(string? sortDirection, string? sortFields)
    {
        var direction = ParseSortDirection(sortDirection);
        var fieldsMap = FieldsMapF34();
        var sortBy = GetSortFields(sortFields, fieldsMap);
        return new SortingOptions(direction, sortBy);
    }

    public static SortingOptions GetSortingOptionsWeighingScale(string? sortDirection, string? sortFields)
    {
        var direction = ParseSortDirection(sortDirection);
        var fieldsMap = FieldsMapWeighingScale();
        var sortBy = GetSortFields(sortFields, fieldsMap);
        return new SortingOptions(direction, sortBy);
    }

    private static Dictionary<string, IEnumerable<string>> FieldsMapF14()
    {
        var fieldsMap = new Dictionary<string, IEnumerable<string>>(StringComparer.OrdinalIgnoreCase)
        {
            { "ArrivalDateTime", new[] { nameof(ReportFormat14.VehicleArrival), nameof(ReportFormat14.VehicleArrivalTime) } },
        };
        return fieldsMap;
    }

    private static Dictionary<string, IEnumerable<string>> FieldsMapF34()
    {
        return new Dictionary<string, IEnumerable<string>>(StringComparer.OrdinalIgnoreCase)
        {
            { "ArrivalDateTime", new[] { nameof(ReportFormat34.ArrivalDate), nameof(ReportFormat34.ArrivalTime) } },
        };
    }

    private static Dictionary<string, IEnumerable<string>> FieldsMapWeighingScale()
    {
        return new Dictionary<string, IEnumerable<string>>(StringComparer.OrdinalIgnoreCase)
        {
            { "EntryDate", new[] { nameof(WeighingScale.EntryDate) } },
            { "EgressDate", new[] { nameof(WeighingScale.EgressDate) } },
            { "LoadingDate", new[] { nameof(WeighingScale.LoadingDate) } },
            { "LicensePlate", new[] { nameof(WeighingScale.LicensePlate) } },
            { "NUAP", new[] { nameof(WeighingScale.NUAP) } },
            { "NIT", new[] { nameof(WeighingScale.NIT) } },
            { "ArrivingWeight", new[] { nameof(WeighingScale.ArrivingWeight) } },
            { "DepositWeight", new[] { nameof(WeighingScale.DepositWeight) } },
            { "LeavingWeight", new[] { nameof(WeighingScale.LeavingWeight) } },
        };
    }

    private static SortDirection ParseSortDirection(string? sortDirection) =>
        string.IsNullOrWhiteSpace(sortDirection)
            ? SortDirection.Descending
            : Enum.Parse<SortDirection>(sortDirection);

    private static IEnumerable<string> GetSortFields(string? sortFields, Dictionary<string, IEnumerable<string>> fieldsMap)
    {
        if (string.IsNullOrWhiteSpace(sortFields)) return fieldsMap.First().Value;

        var fields = sortFields.Split(',', StringSplitOptions.RemoveEmptyEntries).Select(field => field.Trim()).Where(fieldsMap.ContainsKey);

        return fields.SelectMany(field => fieldsMap[field]);
    }
}