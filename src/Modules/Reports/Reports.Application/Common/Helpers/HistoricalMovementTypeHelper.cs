using Reports.Domain.Constants;

namespace Reports.Application.Common.Helpers;

/// <summary>
/// Clase de utilidades para convertir enums a descripciones legibles
/// </summary>
public static class HistoricalMovementTypeHelper
{
    /// <summary>
    /// Convierte un HistoricalMovementType a su descripción en español
    /// </summary>
    /// <param name="movementType">El tipo de movimiento</param>
    /// <returns>Descripción en español del tipo de movimiento</returns>
    public static string ToDescription(HistoricalMovementType movementType)
    {
        return movementType switch
        {
            HistoricalMovementType.Created => "Creado",
            HistoricalMovementType.Modified => "Modificado",
            HistoricalMovementType.Annulled => "Anulado",
            _ => "Desconocido"
        };
    }
}
