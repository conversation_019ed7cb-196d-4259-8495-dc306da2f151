using AutoMapper;
using Orion.SharedKernel.Domain.Entities;
using Reports.Application.Common.Extensions;
using Reports.Application.Common.Helpers;
using Reports.Application.DTOs.Clients;
using Reports.Application.DTOs.Microroutes;
using Reports.Application.DTOs.RecyclingArea;
using Reports.Application.DTOs.Reports;
using Reports.Application.DTOs.Reports.MassBalance;
using Reports.Application.DTOs.Weighings;
using Reports.Application.DTOs.HistoricalAudit;
using Reports.Application.Services.Http.Legacy.Request;
using Reports.Domain.Entities;
using Reports.Domain.ValueObjects;

namespace Reports.Application.Common.MappingProfiles;

public class DomainToResponseMappingProfile : Profile
{
    private const string _defaultTimeZoneRegion = "America/Bogota";
    private const string _dateFormat = "yyyy-MM-ddTHH:mm:ss";

    readonly TimeZoneInfo sourceTimeZone = TimeZoneInfo.FindSystemTimeZoneById(_defaultTimeZoneRegion);

    public DomainToResponseMappingProfile()
    {
        #region UnloadingTicket

        CreateMap<WeighingScale, PostUnloadingTicketRequest>()
            .ForMember(dest => dest.Number,
                opt =>
                    opt.MapFrom(src =>
                        src.Id))
            .ForMember(dest => dest.LicensePlate,
                opt =>
                    opt.MapFrom(src =>
                        src.LicensePlate))
            .ForMember(dest => dest.Datetime,
                opt =>
                    opt.MapFrom(src =>
                        TimeZoneInfo.ConvertTimeToUtc(src.EntryDate, sourceTimeZone)
                            .ToString(_dateFormat)))
            .ForMember(dest => dest.GrossWeight,
                opt =>
                    opt.MapFrom(src =>
                        src.ArrivingWeight))
            .ForMember(dest => dest.TareWeight,
                opt =>
                    opt.MapFrom(src =>
                        src.LeavingWeight))
            .ForMember(dest => dest.Destination,
                opt =>
                    opt.MapFrom(src =>
                        src.DepositPlace.ToString()))
            .ForMember(dest => dest.MaterialType,
                opt =>
                    opt.MapFrom(src =>
                        src.MaterialType));

        #endregion

        #region Discounts

        CreateMap<Discount, DiscountsSpanishModel>()
            .ForMember(dest => dest.Ticket,
                opt =>
                    opt.MapFrom(src =>
                        src.Id))
            .ForMember(dest => dest.Periodo,
                opt =>
                    opt.MapFrom(src =>
                        src.Period))
            .ForMember(dest => dest.Placa_del_Vehículo,
                opt =>
                    opt.MapFrom(src =>
                        src.LicensePlate))
            .ForMember(dest => dest.Ruta,
                opt =>
                    opt.MapFrom(src =>
                        src.RouteCode))
            .ForMember(dest => dest.Tipo_de_Servicio,
                opt =>
                    opt.MapFrom(src =>
                        src.ServiceDescription))
            .ForMember(dest => dest.Toneladas,
                opt =>
                    opt.MapFrom(src =>
                        src.Tons));

        #endregion

        #region Reports

        CreateMap<ReportFormat14, F14PreviewResponseDto>()
            .ForMember(dest => dest.DestinationType,
                opt =>
                    opt.MapFrom(src =>
                        src.DestinationType.GetDescription()));

        CreateMap<ReportFormat34, F34PreviewResponseDto>()
            .ForMember(dest => dest.OriginType,
                opt =>
                    opt.MapFrom(src =>
                        src.OriginType.GetDescription()));

        CreateMap<SUIReportFormat14Extended, F14ExportResponseDto>();

        CreateMap<WeighingScale, WeighingScaleExportResponseDto>()
            .ForMember(dest => dest.NroComprobante,
                opt => opt.MapFrom(src => src.Id))
            .ForMember(dest => dest.Placa,
                opt => opt.MapFrom(src => src.LicensePlate))
            .ForMember(dest => dest.FechaEntrada,
                opt => opt.MapFrom(src => src.EntryDate))
            .ForMember(dest => dest.FechaSalida,
                opt => opt.MapFrom(src => src.EgressDate))
            .ForMember(dest => dest.PesoLlegada,
                opt => opt.MapFrom(src => src.ArrivingWeight))
            .ForMember(dest => dest.PesoSalida,
                opt => opt.MapFrom(src => src.LeavingWeight))
            .ForMember(dest => dest.PesoDepositado,
                opt => opt.MapFrom(src => src.DepositWeight))
            .ForMember(dest => dest.TipoOrigen,
                opt => opt.MapFrom(src => src.OriginType.GetDescription()))
            .ForMember(dest => dest.NombreEmpresa,
                opt => opt.Ignore())
            .ForMember(dest => dest.TipoMaterial,
                opt => opt.MapFrom(src => src.MaterialType))
            .ForMember(dest => dest.LugarDeposito,
                opt => opt.MapFrom(src => src.DepositPlace))
            .ForMember(dest => dest.FechaCarga,
                opt => opt.MapFrom(src => src.LoadingDate))
            .ForMember(dest => dest.FechaCancelacion,
                opt => opt.MapFrom(src => src.CancelDate))
            .ForMember(dest => dest.TipoCarga,
                opt => opt.MapFrom(src => src.LoadingType.GetDescription()))
            .ForMember(dest => dest.NumeroMunicipio,
                opt => opt.MapFrom(src => src.Town != null ? (src.Town.Code ?? string.Empty) : string.Empty))
            .ForMember(dest => dest.Municipio,
                opt => opt.MapFrom(src => src.Town != null ? (src.Town.Name ?? string.Empty) : string.Empty))
            .ForMember(dest => dest.Departamento,
                opt => opt.MapFrom(src => src.Town != null ? (src.Town.Department ?? string.Empty) : string.Empty))
            .ForMember(dest => dest.Provincia,
                opt => opt.MapFrom(src => src.Town != null ? (src.Town.Province ?? string.Empty) : string.Empty));

        CreateMap<ReportFormat34, F34ExportResponseDto>()
            .ForMember(dest => dest.C1_NUSD,
                opt =>
                    opt.MapFrom(src =>
                        src.NUSD))
            .ForMember(dest => dest.C2_TIPO_ORIGEN,
                opt =>
                    opt.MapFrom(src =>
                        src.OriginType))
            .ForMember(dest => dest.C3_NUSITIO_ORI,
                opt =>
                    opt.MapFrom(src =>
                        src.PlaceOriginNumber))
            .ForMember(dest => dest.C4_NOMBRE_EMPRESA,
                opt =>
                    opt.MapFrom(src =>
                        src.CompanyName))
            .ForMember(dest => dest.C5_NIT_EMPRESA,
                opt =>
                    opt.MapFrom(src =>
                        src.NIT))
            .ForMember(dest => dest.C6_COD_DANE_ORI,
                opt =>
                    opt.MapFrom(src =>
                        src.DaneCode))
            .ForMember(dest => dest.C7_PLACA,
                opt =>
                    opt.MapFrom(src =>
                        src.LicensePlate))
            .ForMember(dest => dest.C8_FECHA_INGRESO,
                opt =>
                    opt.MapFrom(src =>
                        src.ArrivalDate))
            .ForMember(dest => dest.C9_FECHA_SALIDA,
                opt =>
                    opt.MapFrom(src =>
                        src.DepartureDate))
            .ForMember(dest => dest.C10_HORA_INGRESO,
                opt =>
                    opt.MapFrom(src =>
                        src.ArrivalTime))
            .ForMember(dest => dest.C11_HORA_SALIDA,
                opt =>
                    opt.MapFrom(src =>
                        src.DepartureTime))
            .ForMember(dest => dest.C12_TONELADAS,
                opt =>
                    opt.MapFrom(src =>
                        src.Tons))
            .ForMember(dest => dest.CE_ID_TICKET,
                opt =>
                    opt.MapFrom(src =>
                        src.ServiceTicketId))
            .ForMember(dest => dest.CE_VALOR_TICKET_LEGACY,
                opt =>
                    opt.MapFrom(src =>
                        src.ServiceTicketWeight))
            .ForMember(dest => dest.CE_VALOR_TON_TICKET_LEGACY,
                opt =>
                    opt.MapFrom(src =>
                        src.ServiceTicketTonnage))
            .ForMember(dest => dest.CE_TONELADAS_RECHAZADAS,
                opt =>
                    opt.MapFrom(src =>
                        src.RejectedTonnage))
            .ForMember(dest => dest.CE_EXISTE_EN_F14,
                opt =>
                    opt.MapFrom(src =>
                        src.ExistsInReport14))
            .ForMember(dest => dest.CE_NIT_EMPRESA,
                opt =>
                    opt.MapFrom(src =>
                        src.CompanyNIT))
            .ForMember(dest => dest.CE_NUAP,
                opt =>
                    opt.MapFrom(src =>
                        src.NUAP));



        #endregion Reports

        #region RecyclingArea

        CreateMap<RecyclingArea, GetAllRecyclingAreaResponseDto>();

        #endregion RecyclingArea

        #region Clients

        CreateMap<Client, ClientsResponseDto>()
            .ForMember(dest => dest.NIT,
                opt =>
                    opt.MapFrom(src =>
                        src.Id));

        #endregion

        #region Weighins

        CreateMap<Town, GetWeighingItemTownResponseDto>()
            .ForMember(dest => dest.Code,
                opt =>
                    opt.MapFrom(src =>
                        src.Code))
            .ForMember(dest => dest.Name,
                opt =>
                    opt.MapFrom(src =>
                        src.Name))
            .ForMember(dest => dest.Department,
                opt =>
                    opt.MapFrom(src =>
                        src.Department))
            .ForMember(dest => dest.Province,
                opt =>
                    opt.MapFrom(src =>
                        src.Province));

        CreateMap<WeighingScale, GetWeighingItemResponseDto>()
            .ForMember(dest => dest.Id,
                opt =>
                    opt.MapFrom(src =>
                        src.Id))
            .ForMember(dest => dest.LicensePlate,
                opt =>
                    opt.MapFrom(src =>
                        src.LicensePlate))
            .ForMember(dest => dest.NIT,
                opt =>
                    opt.MapFrom(src =>
                        src.NIT))
            .ForMember(dest => dest.NUAP,
                opt =>
                    opt.MapFrom(src =>
                        src.NUAP))
            .ForMember(dest => dest.ArrivingWeight,
                opt =>
                    opt.MapFrom(src =>
                        src.ArrivingWeight))
            .ForMember(dest => dest.LeavingWeight,
                opt =>
                    opt.MapFrom(src =>
                        src.LeavingWeight))
            .ForMember(dest => dest.DepositWeight,
                opt =>
                    opt.MapFrom(src =>
                        src.DepositWeight))
            .ForMember(dest => dest.EntryDate,
                opt =>
                    opt.MapFrom(src => src.EntryDate.ToString(_dateFormat)))
            .ForMember(dest => dest.EgressDate,
                opt =>
                    opt.MapFrom(src => src.EgressDate.ToString(_dateFormat)))
            .ForMember(dest => dest.DepositPlace,
                opt =>
                    opt.MapFrom(src =>
                        src.DepositPlace.ToString()))
            .ForMember(dest => dest.MaterialType,
                opt =>
                    opt.MapFrom(src =>
                        src.MaterialType.ToString()))
            .ForMember(dest => dest.LoadingType,
                opt =>
                    opt.MapFrom(src =>
                        src.LoadingType.ToString()))
            .ForMember(dest => dest.OriginType,
                opt =>
                    opt.MapFrom(src =>
                        src.OriginType.ToString()));

        CreateMap<PaginatedResult<WeighingScale>, PaginatedResult<GetWeighingItemResponseDto>>()
            .ForMember(dest => dest.Results,
                opt =>
                    opt.MapFrom(src => src.Results));

        #endregion

        #region Microroutes

        CreateMap<MicroRoute, RegulatoryRouteCodesDto>()
            .ForMember(dest => dest.HygieneRouteCode,
                opt =>
                    opt.MapFrom(src =>
                        src.ExtendedRouteCode))
            .ForMember(dest => dest.RegulatoryRouteCode,
                opt =>
                    opt.MapFrom(src =>
                        src.ExternalRouteCode.ToString()));

        #endregion

        #region MassBalance

        CreateMap<Distribution, DistributionDto>()
            .ForMember(dest => dest.RecyclingArea,
                opt =>
                    opt.MapFrom(src => src.RecyclingArea))
            .ForMember(dest => dest.Trips,
                opt =>
                    opt.MapFrom(src => src.Trips))
            .ForMember(dest => dest.ReportedTons,
                opt =>
                    opt.MapFrom(src => src.ReportedTons))
            .ForMember(dest => dest.CalculatedDeviationTons,
                opt =>
                    opt.MapFrom(src => src.DeviationTons))
            .ForMember(dest => dest.CalculatedDistributedTons,
                opt =>
                    opt.MapFrom(src => src.DistributedTons))
            .ForMember(dest => dest.TollSharedRouteTons,
                opt =>
                    opt.MapFrom(src => src.TollSharedRouteTons))
            .ForMember(dest => dest.CalculatedDistributionTollPercentage,
                opt =>
                    opt.MapFrom(src => src.RoundedDistributionTollPercentage))
            .ForMember(dest => dest.CalculatedTotalTons,
                opt =>
                    opt.MapFrom(src => src.TotalTons));

        CreateMap<WeighinTonsResume, WeighinsDto>()
            .ForMember(dest => dest.EmvariasTons,
                opt =>
                    opt.MapFrom(src => src.Emvarias))
            .ForMember(dest => dest.TotalTons,
                opt =>
                    opt.MapFrom(src => src.Total));

        CreateMap<AreaGroup<WeighinTonsResume>, FinalDispositionDto>()
            .ForMember(dest => dest.PerArea,
                opt =>
                    opt.MapFrom(src => src.PerArea))
            .ForMember(dest => dest.Totals,
                opt =>
                    opt.MapFrom(src => src.Totals));

        CreateMap<ResumePerArea<WeighinTonsResume>, FinalDispositionPerAreaDto>()
            .ForMember(dest => dest.RecyclingArea,
                opt =>
                    opt.MapFrom(src => src.RecyclingArea))
            .ForMember(dest => dest.EmvariasTons,
                opt =>
                    opt.MapFrom(src => src.Resume.Emvarias))
            .ForMember(dest => dest.DiscountTons,
                opt =>
                    opt.MapFrom(src => src.Resume.Discount))
            .ForMember(dest => dest.TotalTons,
                opt =>
                    opt.MapFrom(src => src.Resume.Total));

        CreateMap<WeighinTonsResume, FinalDispositionTotalDto>()
            .ForMember(dest => dest.AllDiscountTons,
                opt =>
                    opt.MapFrom(src => src.Discount))
            .ForMember(dest => dest.AllEmvariasTons,
                opt =>
                    opt.MapFrom(src => src.Emvarias))
            .ForMember(dest => dest.TotalTons,
                opt =>
                    opt.MapFrom(src => src.Total));

        CreateMap<AreaGroup<HygieneTonsResume>, RecollectionAndTransportDto>()
            .ForMember(dest => dest.PerArea,
                opt =>
                    opt.MapFrom(src => src.PerArea))
            .ForMember(dest => dest.Totals,
                opt =>
                    opt.MapFrom(src => src.Totals));

        CreateMap<ResumePerArea<HygieneTonsResume>, RecollectionAndTransportPerAreaDto>()
            .ForMember(dest => dest.RecyclingArea,
                opt =>
                    opt.MapFrom(src => src.RecyclingArea))
            .ForMember(dest => dest.UrbanCleaningTons,
                opt =>
                    opt.MapFrom(src => src.Resume.UrbanCleaning))
            .ForMember(dest => dest.SweepingTons,
                opt =>
                    opt.MapFrom(src => src.Resume.Sweeping))
            .ForMember(dest => dest.NonRecyclableTons,
                opt =>
                    opt.MapFrom(src => src.Resume.NonRecyclable))
            .ForMember(dest => dest.RejectionTons,
                opt =>
                    opt.MapFrom(src => src.Resume.Rejection))
            .ForMember(dest => dest.RecyclableTons,
                opt =>
                    opt.MapFrom(src => src.Resume.Recyclable))
            .ForMember(dest => dest.TotalTons,
                opt =>
                    opt.MapFrom(src => src.Resume.Total));

        CreateMap<HygieneTonsResume, RecollectionAndTransportTotalDto>()
            .ForMember(dest => dest.AllUrbanCleaningTons,
                opt =>
                    opt.MapFrom(src => src.UrbanCleaning))
            .ForMember(dest => dest.AllSweepingTons,
                opt =>
                    opt.MapFrom(src => src.Sweeping))
            .ForMember(dest => dest.AllNonRecyclableTons,
                opt =>
                    opt.MapFrom(src => src.NonRecyclable))
            .ForMember(dest => dest.AllRejectionTons,
                opt =>
                    opt.MapFrom(src => src.Rejection))
            .ForMember(dest => dest.AllRecyclableTons,
                opt =>
                    opt.MapFrom(src => src.Recyclable))
            .ForMember(dest => dest.TotalTons,
                opt =>
                    opt.MapFrom(src => src.Total));

        CreateMap<MassBalance, MassBalanceResponseDto>()
            .ForMember(dest => dest.Distributions,
                opt =>
                    opt.MapFrom(src => src.Distributions))
            .ForMember(dest => dest.WeighinsDto,
                opt =>
                    opt.MapFrom(src => src.Weighins))
            .ForMember(dest => dest.FinalDispositionDto,
                opt =>
                    opt.MapFrom(src => src.FinalDisposition))
            .ForMember(dest => dest.RecollectionAndTransportDto,
                opt =>
                    opt.MapFrom(src => src.RecollectionAndTransport))
            .ForMember(dest => dest.SharedRouteTons,
                opt =>
                    opt.MapFrom(src => src.SharedRouteTons))
            .ForMember(dest => dest.IsValid,
                opt =>
                    opt.MapFrom(src => src.IsValid));

        #endregion

        #region HistoricalAudit

        CreateMap<HistoricalAudit, HistoricalAuditItemResponseDto>()
            .ForMember(dest => dest.Id,
                opt =>
                    opt.MapFrom(src => src.Id))
            .ForMember(dest => dest.EntityId,
                opt =>
                    opt.MapFrom(src => src.EntityId))
            .ForMember(dest => dest.TableName,
                opt =>
                    opt.MapFrom(src => src.TableName))
            .ForMember(dest => dest.ActionType,
                opt =>
                    opt.MapFrom(src => HistoricalMovementTypeHelper.ToDescription(src.ActionType)))
            .ForMember(dest => dest.User,
                opt =>
                    opt.MapFrom(src => src.User))
            .ForMember(dest => dest.ActionDate,
                opt =>
                    opt.MapFrom(src => src.ActionDate))
            .ForMember(dest => dest.PreviousData,
                opt =>
                    opt.MapFrom(src => src.PreviousData));

        #endregion
    }
}