using System.Reflection;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Reports.Application.Services.Authentication;
using Reports.Application.Services.HistoricalAudit;
using Reports.Application.Services.Synchronization;
using Reports.Application.Strategies;
using Reports.Domain.Services;

namespace Reports.Application;

public static class ServicesExtensions
{
    public static IServiceCollection AddReportsApplication(this IServiceCollection services)
    {
        services.AddMediatR(Assembly.GetExecutingAssembly());

        services.AddAutoMapper(Assembly.GetExecutingAssembly());

        services.AddValidatorsFromAssembly(Assembly.GetExecutingAssembly());

        services.AddScoped<PreviousDataParser>();

        services.AddSynchronizationServices();

        return services;
    }

    private static IServiceCollection AddSynchronizationServices(this IServiceCollection services)
    {
        services.AddScoped<IAuthenticatedLegacyApiService, AuthenticatedLegacyApiService>();
        services.AddScoped<SynchronizationLockManager>();

        services.AddSynchronizationStrategies();

        return services;
    }

    private static IServiceCollection AddSynchronizationStrategies(this IServiceCollection services)
    {
        services.AddScoped<SynchronizationStrategyContext>();
        
        services.AddScoped<WeighingScaleSynchronizationService>();
        
        services.AddScoped<ISynchronizationOperationStrategy, CreateUnloadingTicketStrategy>();
        services.AddScoped<ISynchronizationOperationStrategy, UpdateUnloadingTicketStrategy>();
        services.AddScoped<ISynchronizationOperationStrategy, CancelUnloadingTicketStrategy>();

        return services;
    }
}