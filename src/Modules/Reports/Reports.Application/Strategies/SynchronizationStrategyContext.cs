using Reports.Domain;
using Reports.Domain.Constants;
using Reports.Domain.Entities;
using Reports.Domain.ValueObjects;

namespace Reports.Application.Strategies;

public class SynchronizationStrategyContext
{
    private readonly IEnumerable<ISynchronizationOperationStrategy> _strategies;
    private readonly IReportsUnitOfWork _unitOfWork;
    private readonly Dictionary<SynchronizationOperationType, ISynchronizationOperationStrategy> _strategyMap;

    public SynchronizationStrategyContext(
        IEnumerable<ISynchronizationOperationStrategy> strategies,
        IReportsUnitOfWork unitOfWork)
    {
        _strategies = strategies ?? throw new ArgumentNullException(nameof(strategies));
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        
        _strategyMap = _strategies
            .ToDictionary(s =>
                s.OperationType, s => s);
        
        var contextDictionary = new Dictionary<string, object>
        {
            ["StrategyCount"] = _strategyMap.Count,
            ["StrategyTypes"] = string.Join(", ", _strategyMap.Keys),
            ["Component"] = "SynchronizationStrategyContext"
        };
        
        _unitOfWork.LogEventMessage.GenerateLogEventMessage(
            $"Contexto de estrategias de sincronización inicializado con {_strategyMap.Count} estrategias: {string.Join(", ", _strategyMap.Keys)}",
            contextDictionary);
    }
    
    public virtual async Task<BatchSyncSummary> ExecuteOperationAsync(
        SynchronizationOperationType operationType,
        IEnumerable<UrbetrackSynchronization> entries,
        RetryPolicy retryPolicy,
        CancellationToken cancellationToken = default)
    {
        var contextDictionary = new Dictionary<string, object>
        {
            ["OperationType"] = operationType.ToString(),
            ["Component"] = "SynchronizationStrategyContext",
            ["Method"] = "ExecuteOperationAsync"
        };
        
        _unitOfWork.LogEventMessage.GenerateLogEventMessage(
            $"Ejecutando operación de sincronización: {operationType}",
            contextDictionary);

        if (!_strategyMap.TryGetValue(operationType, out var strategy))
        {
            var errorMessage = $"No se encontró estrategia para el tipo de operación: {operationType}";
            var errorContext = new Dictionary<string, object>
            {
                ["OperationType"] = operationType.ToString(),
                ["AvailableStrategies"] = string.Join(", ", _strategyMap.Keys),
                ["Component"] = "SynchronizationStrategyContext",
                ["Error"] = "StrategyNotFound"
            };
            
            _unitOfWork.LogEventMessage.GenerateLogEventMessage(errorMessage, errorContext);
            throw new InvalidOperationException(errorMessage);
        }

        var entriesList = entries.ToList();
        
        if (!strategy.CanProcess(entriesList))
        {
            var errorMessage = $"La estrategia para {operationType} no puede procesar las entradas proporcionadas";
            var errorContext = new Dictionary<string, object>
            {
                ["OperationType"] = operationType.ToString(),
                ["EntryCount"] = entriesList.Count,
                ["Component"] = "SynchronizationStrategyContext",
                ["Error"] = "StrategyCannotProcess"
            };
            
            _unitOfWork.LogEventMessage.GenerateLogEventMessage(errorMessage, errorContext);
            throw new InvalidOperationException(errorMessage);
        }

        try
        {
            var executionContext = new Dictionary<string, object>
            {
                ["OperationType"] = operationType.ToString(),
                ["EntryCount"] = entriesList.Count,
                ["Component"] = "SynchronizationStrategyContext",
                ["Phase"] = "Execution"
            };
            
            _unitOfWork.LogEventMessage.GenerateLogEventMessage(
                $"Ejecutando estrategia {operationType} para {entriesList.Count} entradas",
                executionContext);

            var result = await strategy.ExecuteAsync(entriesList, retryPolicy, cancellationToken);

            var completionContext = new Dictionary<string, object>
            {
                ["OperationType"] = operationType.ToString(),
                ["TotalProcessed"] = result.TotalProcessed,
                ["SuccessCount"] = result.SuccessCount,
                ["ErrorCount"] = result.ErrorCount,
                ["FailedDueToRetryLimit"] = result.FailedDueToRetryLimit,
                ["Component"] = "SynchronizationStrategyContext",
                ["Phase"] = "Completion"
            };
            
            _unitOfWork.LogEventMessage.GenerateLogEventMessage(
                $"Operación {operationType} completada. Total: {result.TotalProcessed}, Éxitos: {result.SuccessCount}, Errores: {result.ErrorCount}, Fallidos por límite de reintentos: {result.FailedDueToRetryLimit}",
                completionContext);

            return result;
        }
        catch (Exception ex)
        {
            var exceptionContext = new Dictionary<string, object>
            {
                ["OperationType"] = operationType.ToString(),
                ["Component"] = "SynchronizationStrategyContext",
                ["Error"] = "ExecutionException",
                ["ExceptionType"] = ex.GetType().Name,
                ["ExceptionMessage"] = ex.Message
            };
            
            _unitOfWork.LogEventMessage.GenerateLogEventMessage(
                $"Error ejecutando estrategia {operationType}: {ex.Message}",
                exceptionContext);
            throw;
        }
    }
    
    public static SynchronizationOperationType GetOperationTypeFromStatus(SynchronizationStatus status)
    {
        return status switch
        {
            SynchronizationStatus.PendingCreation => SynchronizationOperationType.Create,
            SynchronizationStatus.PendingModification => SynchronizationOperationType.Update,
            SynchronizationStatus.PendingCancellation => SynchronizationOperationType.Cancel,
            _ => throw new ArgumentException($"Cannot determine operation type for status: {status}", nameof(status))
        };
    }
    
    public IEnumerable<SynchronizationOperationType> GetAvailableOperationTypes()
    {
        return _strategyMap.Keys;
    }
    
    public bool HasStrategyFor(SynchronizationOperationType operationType)
    {
        return _strategyMap.ContainsKey(operationType);
    }
}