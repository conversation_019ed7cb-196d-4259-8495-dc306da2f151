using Reports.Domain.Constants;
using Reports.Domain.Entities;
using Reports.Domain.ValueObjects;

namespace Reports.Application.Strategies;

public interface ISynchronizationOperationStrategy
{
    SynchronizationOperationType OperationType { get; }
    
    Task<BatchSyncSummary> ExecuteAsync(
        IEnumerable<UrbetrackSynchronization> entries,
        RetryPolicy retryPolicy,
        CancellationToken cancellationToken = default);

    bool CanProcess(IEnumerable<UrbetrackSynchronization> entries);
    
    SynchronizationStatus ExpectedInputStatus { get; }
}