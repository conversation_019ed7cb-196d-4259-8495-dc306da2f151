using Reports.Application.Services.Synchronization;
using Reports.Domain;
using Reports.Domain.Constants;
using Reports.Domain.Entities;
using Reports.Domain.ValueObjects;

namespace Reports.Application.Strategies;

public class UpdateUnloadingTicketStrategy : ISynchronizationOperationStrategy
{
    private readonly WeighingScaleSynchronizationService _synchronizationService;
    private readonly IReportsUnitOfWork _unitOfWork;

    public UpdateUnloadingTicketStrategy(
        WeighingScaleSynchronizationService synchronizationService,
        IReportsUnitOfWork unitOfWork)
    {
        _synchronizationService = synchronizationService ?? throw new ArgumentNullException(nameof(synchronizationService));
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public SynchronizationOperationType OperationType => SynchronizationOperationType.Update;

    public SynchronizationStatus ExpectedInputStatus => SynchronizationStatus.ProcessingModification;

    public bool CanProcess(IEnumerable<UrbetrackSynchronization> entries)
    {
        return entries.All(e => e.Status == ExpectedInputStatus);
    }

    public async Task<BatchSyncSummary> ExecuteAsync(
        IEnumerable<UrbetrackSynchronization> entries,
        RetryPolicy retryPolicy,
        CancellationToken cancellationToken = default)
    {
        var entriesList = entries.ToList();
        var results = new List<SyncOperationOutcome>();
        var startTime = DateTime.UtcNow;

        await _unitOfWork.LogEventMessage.GenerateLogEventMessage(
            $"Iniciando actualización de {entriesList.Count} tickets de descarga",
            new Dictionary<string, object>
            {
                { "ValidTicketCount", entriesList.Count },
                { "OperationType", "Update" },
                { "StartTime", startTime },
                { "ValidEntryIds", entriesList.Select(e => e.Id).ToList() }
            });

        foreach (var sync in entriesList)
        {
            try
            {
                var result = await _synchronizationService.UpdateTicketAsync(sync.WeighingScale, sync.UrbetrackInternalId, cancellationToken);
                results.Add(result);
                sync.ApplySynchronizationResult(result);

                await _unitOfWork.LogEventMessage.GenerateLogEventMessage(
                    $"Procesado WeighingScale {sync.WeighingScaleId} con resultado: {result.Status}",
                    new Dictionary<string, object>
                    {
                        { "WeighingScaleId", sync.WeighingScaleId },
                        { "SyncId", sync.Id },
                        { "ResultStatus", result.Status.ToString() },
                        { "IsSuccess", result.IsSuccess },
                        { "UrbetrackInternalId", result.UrbetrackInternalId ?? string.Empty },
                        { "PreviousUrbetrackInternalId", sync.UrbetrackInternalId ?? string.Empty }
                    });
            }
            catch (Exception ex)
            {
                await _unitOfWork.LogEventMessage.GenerateLogEventMessage(
                    $"Error procesando WeighingScale {sync.WeighingScaleId}: {ex.Message}",
                    new Dictionary<string, object>
                    {
                        { "WeighingScaleId", sync.WeighingScaleId },
                        { "SyncId", sync.Id },
                        { "ErrorMessage", ex.Message },
                        { "StackTrace", ex.StackTrace ?? string.Empty },
                        { "OperationType", "Update" },
                        { "UrbetrackInternalId", sync.UrbetrackInternalId ?? string.Empty }
                    });
                var errorResult = SyncOperationOutcome.RetryableFailure(ex.Message, SynchronizationStatus.PendingModification);
                results.Add(errorResult);
                sync.ApplySynchronizationResult(errorResult);
            }
        }

        var summary = new BatchSyncSummary();
        
        foreach (var sync in entriesList) summary.AddProcessedEntry(sync);
        
        var endTime = DateTime.UtcNow;
        var duration = endTime - startTime;
        await _unitOfWork.LogEventMessage.GenerateLogEventMessage(
            $"Se completó correctamente el lote de actualización en {duration.TotalSeconds:F2} segundos. Éxitos: {summary.SuccessCount}, Errores: {summary.ErrorCount}",
            new Dictionary<string, object>
            {
                { "SuccessCount", summary.SuccessCount },
                { "ErrorCount", summary.ErrorCount },
                { "TotalProcessed", summary.TotalProcessed },
                { "Duration", duration.TotalSeconds },
                { "OperationType", "Update" },
                { "EndTime", endTime }
            });

        return summary;
    }
}