using System.Collections.Immutable;
using System.Linq.Expressions;
using AutoMapper;
using MediatR;
using Orion.SharedKernel.Application.Common.Mapping;
using Orion.SharedKernel.Application.Common.Services;
using Orion.SharedKernel.Application.Exceptions;
using Reports.Domain;
using Reports.Domain.Constants;
using Reports.Domain.Entities;
using Reports.Domain.Events;
using Reports.Domain.ValueObjects;

namespace Reports.Application.Features.Web.Commands.Weighins.CreateWeighing;

internal sealed class CreateWeighingHandler : MappingService,
    IRequestHandler<CreateWeighingRequest, CreateWeighingResponse>
{
    private readonly IReportsUnitOfWork _unitOfWork;
    private readonly IMediator _mediator;
    private readonly ICurrentUserService _currentUserService;

    public CreateWeighingHandler(IMapper mapper, IMediator mediator, IReportsUnitOfWork reportsUnitOfWork,
        ICurrentUserService currentUserService) : base(mapper)
    {
        _unitOfWork = reportsUnitOfWork;
        _mediator = mediator;
        _currentUserService = currentUserService;
    }

    public async Task<CreateWeighingResponse> Handle(CreateWeighingRequest request, CancellationToken cancellationToken)
    {
        var weighins = Mapper.Map<List<WeighingScale>>(request.Weighins);

        var operationResult = await ClassifyWeighingOperationsAsync(weighins, cancellationToken);

        await ValidateTownCodes(weighins, cancellationToken);

        await ProcessWeighingOperationsAsync(operationResult, cancellationToken);
        
        await PublishDomainEventsAsync(operationResult, cancellationToken);
        
        return new CreateWeighingResponse(
            operationResult.WeighingsToInsert,
            operationResult.WeighingsToUpdate,
            operationResult.WeighingsToCancel);
    }
    
    private async Task<WeighingClassificationResult> ClassifyWeighingOperationsAsync(
        List<WeighingScale> weighins,
        CancellationToken cancellationToken)
    {
        var existingWeighings = await RetrieveExistingWeighingsAsync(weighins, cancellationToken);
        var classifications = ClassifyEachWeighing(weighins, existingWeighings);
        
        return BuildOperationResult(weighins, ref existingWeighings, ref classifications);
    }
    
    private async Task<IReadOnlyDictionary<long, WeighingScale>> RetrieveExistingWeighingsAsync(
        IEnumerable<WeighingScale> weighins,
        CancellationToken cancellationToken)
    {
        var weighingIds = weighins
            .Select(w => w.Id)
            .ToHashSet();
        
        if (!weighingIds.Any())
            return new Dictionary<long, WeighingScale>();

        var result = await _unitOfWork.WeighingScales.GetAllAsync(
            predicate: w => weighingIds.Contains(w.Id!),
            useCache: false,
            isPaginated: false,
            maxQueryCount: int.MaxValue,
            cancellationToken: cancellationToken);

        return result
            .Results
            .ToDictionary(w => w.Id, w => w);
    }

    private static IReadOnlyDictionary<long, WeighingOperationType> ClassifyEachWeighing(
        IEnumerable<WeighingScale> weighins,
        IReadOnlyDictionary<long, WeighingScale> existingLookup)
    {
        var classifications = new Dictionary<long, WeighingOperationType>();

        foreach (var weighing in weighins)
        {
            var existingWeighing = existingLookup.GetValueOrDefault(weighing.Id);
            
            var operationType = weighing.DetermineOperationType(existingWeighing);
            classifications[weighing.Id] = operationType;
        }

        return classifications;
    }
    
    private static WeighingClassificationResult BuildOperationResult(
        IEnumerable<WeighingScale> weighins,
        ref IReadOnlyDictionary<long, WeighingScale> existingLookup,
        ref IReadOnlyDictionary<long, WeighingOperationType> classifications)
    {
        var weighingsToInsert = new List<WeighingScale>();
        var weighingsToUpdate = new List<WeighingScale>();
        var weighingsToCancel = new List<WeighingScale>();
        var previousEntitiesForUpdate = new List<WeighingScale>();
        var previousEntitiesForCancel = new List<WeighingScale>();

        foreach (var weighing in weighins)
        {
            var operationType = classifications.GetValueOrDefault(weighing.Id, WeighingOperationType.Insert);
            var existingWeighing = existingLookup.GetValueOrDefault(weighing.Id);

            GroupWeighingByOperationType(
                weighing, existingWeighing, operationType,
                ref weighingsToInsert, ref weighingsToUpdate, ref weighingsToCancel,
                ref previousEntitiesForUpdate, ref previousEntitiesForCancel);
        }

        return new WeighingClassificationResult
        {
            WeighingsToInsert = weighingsToInsert,
            WeighingsToUpdate = weighingsToUpdate,
            WeighingsToCancel = weighingsToCancel,
            PreviousEntitiesForUpdate = previousEntitiesForUpdate,
            PreviousEntitiesForCancel = previousEntitiesForCancel
        };
    }
    
    private static void GroupWeighingByOperationType(
        WeighingScale weighing,
        WeighingScale? existingWeighing,
        WeighingOperationType operationType,
        ref List<WeighingScale> weighingsToInsert,
        ref List<WeighingScale> weighingsToUpdate,
        ref List<WeighingScale> weighingsToCancel,
        ref List<WeighingScale> previousEntitiesForUpdate,
        ref List<WeighingScale> previousEntitiesForCancel)
    {
        switch (operationType)
        {
            case WeighingOperationType.Insert:
                weighingsToInsert.Add(weighing);
                break;
                
            case WeighingOperationType.Update:
                weighingsToUpdate.Add(weighing);
                if (existingWeighing is not null)
                    previousEntitiesForUpdate.Add(existingWeighing);
                break;
                
            case WeighingOperationType.Cancel:
                weighingsToCancel.Add(weighing);
                if (existingWeighing is not null)
                    previousEntitiesForCancel.Add(existingWeighing);
                break;
        }
    }
    
    private async Task ProcessWeighingOperationsAsync(
        WeighingClassificationResult classificationResult,
        CancellationToken cancellationToken)
    {
        await _unitOfWork.BeginTransactionAsync(cancellationToken);

        await ProcessInsertOperationsAsync(classificationResult.WeighingsToInsert, cancellationToken);
        await ProcessUpdateAndCancelOperationsAsync(
            classificationResult.WeighingsToUpdate,
            classificationResult.WeighingsToCancel,
            cancellationToken);
        
        await _unitOfWork.CommitAsync(cancellationToken);
    }
    
    private async Task ProcessInsertOperationsAsync(
        IReadOnlyList<WeighingScale> weighingsToInsert,
        CancellationToken cancellationToken)
    {
        if (!weighingsToInsert.Any())
            return;

        await _unitOfWork.WeighingScales.AddRangeAsync(weighingsToInsert, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);
    }
    
    private async Task ProcessUpdateAndCancelOperationsAsync(
        IReadOnlyList<WeighingScale> weighingsToUpdate,
        IReadOnlyList<WeighingScale> weighingsToCancel,
        CancellationToken cancellationToken)
    {
        if (!weighingsToUpdate.Any() && !weighingsToCancel.Any())
            return;

        if (weighingsToUpdate.Any())
            await _unitOfWork.WeighingScales.BulkUpdateAsync(weighingsToUpdate.ToList(), cancellationToken);
            
        if (weighingsToCancel.Any())
            await _unitOfWork.WeighingScales.BulkUpdateAsync(weighingsToCancel.ToList(), cancellationToken);
    }
    
    private async Task PublishDomainEventsAsync(
        WeighingClassificationResult classificationResult,
        CancellationToken cancellationToken)
    {
        var currentUser = GetCurrentUser();
        
        await PublishCreatedEventsAsync(classificationResult.WeighingsToInsert, currentUser, cancellationToken);
        await PublishUpdatedEventsAsync(classificationResult.WeighingsToUpdate,
            classificationResult.PreviousEntitiesForUpdate, currentUser, cancellationToken);
        await PublishCancelledEventsAsync(classificationResult.WeighingsToCancel, currentUser, cancellationToken);
    }

    private async Task PublishCreatedEventsAsync(
        IReadOnlyList<WeighingScale> weighingsToInsert,
        string currentUser,
        CancellationToken cancellationToken)
    {
        if (weighingsToInsert.Any())
            await _mediator.Publish(new WeighingScaleCreatedEvent(weighingsToInsert, currentUser), cancellationToken);
    }

    private async Task PublishUpdatedEventsAsync(
        IReadOnlyList<WeighingScale> weighingsToUpdate,
        IReadOnlyList<WeighingScale> previousEntitiesForUpdate,
        string currentUser,
        CancellationToken cancellationToken)
    {
        if (weighingsToUpdate.Any())
            await _mediator.Publish(new WeighingScaleUpdatedEvent(weighingsToUpdate, previousEntitiesForUpdate, currentUser), cancellationToken);
    }

    private async Task PublishCancelledEventsAsync(
        IReadOnlyList<WeighingScale> weighingsToCancel,
        string currentUser,
        CancellationToken cancellationToken)
    {
        if (weighingsToCancel.Any())
            await _mediator.Publish(new WeighingScaleCancelledEvent(weighingsToCancel, currentUser), cancellationToken);
    }
    
    private async Task ValidateTownCodes(List<WeighingScale> weighins, CancellationToken cancellationToken)
    {
        var townCodes = weighins
            .Select(p => p.TownCode)
            .ToImmutableHashSet();

        var allTownsFound = await _unitOfWork
            .Towns
            .AllMatchesByIdAsync(townCodes, cancellationToken);

        if (!allTownsFound)
        {
            var towns = await _unitOfWork
                .Towns
                .GetAllAsync(
                    predicate: GetPredicateMatchTowns(townCodes),
                    useCache: false,
                    isPaginated: false,
                    maxQueryCount: int.MaxValue,
                    cancellationToken: cancellationToken);

            var nonFoundTownCodes = townCodes.Except(
                towns.Results.Select(p => p.Code));

            throw new OrionException(
                _unitOfWork
                    .ErrorService
                    .GenerateError(
                        new TownNotFound(nonFoundTownCodes)));
        }
    }

    private static Expression<Func<Town, bool>> GetPredicateMatchTowns(ImmutableHashSet<string> townIdsToCheck) => 
        town => townIdsToCheck.Contains(town.Code);

    private string GetCurrentUser() => _currentUserService?.GetCurrentUserName() ?? string.Empty;
}