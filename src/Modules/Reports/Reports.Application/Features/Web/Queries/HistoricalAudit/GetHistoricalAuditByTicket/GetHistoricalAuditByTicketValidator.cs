using FluentValidation;

namespace Reports.Application.Features.Web.Queries.HistoricalAudit.GetHistoricalAuditByTicket;

/// <summary>
/// Validador para la consulta de historial de auditoría por ticket
/// </summary>
public class GetHistoricalAuditByTicketValidator : AbstractValidator<GetHistoricalAuditByTicketRequest>
{
    /// <summary>
    /// Constructor del validador
    /// </summary>
    public GetHistoricalAuditByTicketValidator()
    {
        RuleFor(x => x.IdTicket)
            .NotEmpty()
            .WithMessage("El número de ticket no puede estar vacío");
    }
}
