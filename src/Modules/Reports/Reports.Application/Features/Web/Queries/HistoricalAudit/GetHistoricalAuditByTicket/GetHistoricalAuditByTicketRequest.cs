using MediatR;

namespace Reports.Application.Features.Web.Queries.HistoricalAudit.GetHistoricalAuditByTicket;

/// <summary>
/// Solicitud para obtener el historial de auditoría de un ticket específico
/// </summary>
public record GetHistoricalAuditByTicketRequest : IRequest<GetHistoricalAuditByTicketResponse>
{
    /// <summary>
    /// Número de ticket para consultar el historial
    /// </summary>
    /// <example>123456</example>
    public string IdTicket { get; set; } = string.Empty;
}
