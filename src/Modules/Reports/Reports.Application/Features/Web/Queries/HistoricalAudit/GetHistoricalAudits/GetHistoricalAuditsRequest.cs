using MediatR;

namespace Reports.Application.Features.Web.Queries.HistoricalAudit.GetHistoricalAudits;

/// <summary>
/// Solicitud para obtener el historial de auditoría con filtros
/// </summary>
public record GetHistoricalAuditsRequest : IRequest<GetHistoricalAuditsResponse>
{
    /// <summary>
    /// Lista de IDs de tickets para filtrar
    /// </summary>
    /// <example>["123456", "789012"]</example>
    public List<string>? IdsTickets { get; set; }

    /// <summary>
    /// Fecha desde para filtrar (obligatorio)
    /// </summary>
    /// <example>2024-07-01T00:00:00</example>
    public DateTime FromDate { get; set; }

    /// <summary>
    /// Fecha hasta para filtrar (obligatorio)
    /// </summary>
    /// <example>2024-07-31T23:59:59</example>
    public DateTime ToDate { get; set; }
}
