using System.Linq.Expressions;
using AutoMapper;
using MediatR;
using Orion.SharedKernel.Application.Common.Mapping;
using Orion.SharedKernel.Domain.Entities.Headers;
using Reports.Application.DTOs.HistoricalAudit;
using Reports.Application.DTOs.HistoricalAudit.PreviousData;
using Reports.Application.Services.HistoricalAudit;
using Reports.Domain;
using Reports.Domain.Constants;
using DomainHistoricalAudit = Reports.Domain.Entities.HistoricalAudit;

namespace Reports.Application.Features.Web.Queries.HistoricalAudit.GetHistoricalAudits;

internal sealed class GetHistoricalAuditsHandler : MappingService, IRequestHandler<GetHistoricalAuditsRequest, GetHistoricalAuditsResponse>
{
    private readonly IReportsUnitOfWork _unitOfWork;
    private readonly HeadersValuesProvider _headersValuesProvider;
    private readonly PreviousDataParser _previousDataParser;

    public GetHistoricalAuditsHandler(IMapper mapper, IReportsUnitOfWork unitOfWork, HeadersValuesProvider headersValuesProvider, PreviousDataParser previousDataParser) : base(mapper)
    {
        _unitOfWork = unitOfWork;
        _headersValuesProvider = headersValuesProvider;
        _previousDataParser = previousDataParser;
    }

    public async Task<GetHistoricalAuditsResponse> Handle(GetHistoricalAuditsRequest request, CancellationToken cancellationToken)
    {
        var predicate = GetPredicate(request);

        var historic = await _unitOfWork
            .HistoricalAudits
            .GetAllAsync(
                predicate: predicate,
                orderBy: h => h.OrderBy(x => x.ActionDate),
                useCache: false,
                isPaginated: false,
                maxQueryCount: int.MaxValue,
                cancellationToken: cancellationToken
            );

        _headersValuesProvider.Response.TotalRecords = historic.TotalRecords.ToString();

        var mappedResults = Mapper.Map<IEnumerable<HistoricalAuditItemResponseDto>>(historic.Results);

        foreach (var item in mappedResults)
        {
            if (item.TableName == ReportsTableNames.WeighingScale)
            {
                item.PreviousDataTyped = _previousDataParser.ParsePreviousData<WeighingScalePreviousDataDto>(item.TableName, item.PreviousData);
            }
        }
        return new GetHistoricalAuditsResponse(mappedResults);
    }

    private static Expression<Func<DomainHistoricalAudit, bool>> GetPredicate(GetHistoricalAuditsRequest request)
    {
        return h => h.TableName == ReportsTableNames.WeighingScale
                    && (request.IdsTickets == null || request.IdsTickets.Count == 0 || request.IdsTickets.Contains(h.EntityId))
                    && h.ActionDate >= request.FromDate
                    && h.ActionDate <= request.ToDate;
    }
}
