using Reports.Application.DTOs.HistoricalAudit;

namespace Reports.Application.Features.Web.Queries.HistoricalAudit.GetHistoricalAuditByTicket;

/// <summary>
/// Respuesta que contiene el historial de auditoría para un ticket específico
/// </summary>
public class GetHistoricalAuditByTicketResponse
{
    /// <summary>
    /// Lista de registros de auditoría histórica
    /// </summary>
    public List<HistoricalAuditItemResponseDto> Historic { get; set; }

    /// <summary>
    /// Constructor para inicializar la respuesta
    /// </summary>
    /// <param name="historic">Lista de registros de auditoría histórica</param>
    public GetHistoricalAuditByTicketResponse(List<HistoricalAuditItemResponseDto> historic)
    {
        Historic = historic;
    }
}
