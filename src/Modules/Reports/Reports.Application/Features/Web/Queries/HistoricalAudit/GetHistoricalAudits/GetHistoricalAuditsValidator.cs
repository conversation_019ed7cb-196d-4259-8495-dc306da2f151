using FluentValidation;

namespace Reports.Application.Features.Web.Queries.HistoricalAudit.GetHistoricalAudits;

/// <summary>
/// Validador para la consulta de historial de auditoría con filtros
/// </summary>
public class GetHistoricalAuditsValidator : AbstractValidator<GetHistoricalAuditsRequest>
{
    /// <summary>
    /// Constructor del validador
    /// </summary>
    public GetHistoricalAuditsValidator()
    {
        RuleFor(x => x.FromDate)
            .NotEmpty()
            .WithMessage("La fecha desde es obligatoria");

        RuleFor(x => x.ToDate)
            .NotEmpty()
            .WithMessage("La fecha hasta es obligatoria");

        RuleFor(x => x.FromDate)
            .LessThanOrEqualTo(x => x.ToDate)
            .WithMessage("La fecha desde debe ser menor o igual a la fecha hasta");

        RuleFor(x => x)
            .Must(x => (x.ToDate - x.FromDate).TotalDays <= 31)
            .WithMessage("La diferencia entre las fechas no puede ser mayor a 31 días");

        RuleFor(x => x.IdsTickets)
            .Must(ids => ids == null || ids.Count == 0 || ids.All(id => !string.IsNullOrWhiteSpace(id)))
            .WithMessage("Los IDs de tickets no pueden estar vacíos");
    }
}
