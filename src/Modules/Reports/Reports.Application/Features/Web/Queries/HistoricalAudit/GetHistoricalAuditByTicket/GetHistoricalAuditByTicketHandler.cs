using System.Linq.Expressions;
using AutoMapper;
using MediatR;
using Orion.SharedKernel.Application.Common.Mapping;
using Reports.Application.DTOs.HistoricalAudit;
using Reports.Application.DTOs.HistoricalAudit.PreviousData;
using Reports.Application.Services.HistoricalAudit;
using Reports.Domain;
using Reports.Domain.Constants;
using DomainHistoricalAudit = Reports.Domain.Entities.HistoricalAudit;

namespace Reports.Application.Features.Web.Queries.HistoricalAudit.GetHistoricalAuditByTicket;

internal sealed class GetHistoricalAuditByTicketHandler : MappingService, IRequestHandler<GetHistoricalAuditByTicketRequest, GetHistoricalAuditByTicketResponse>
{
    private readonly IReportsUnitOfWork _unitOfWork;
    private readonly PreviousDataParser _previousDataParser;

    public GetHistoricalAuditByTicketHandler(IMapper mapper, IReportsUnitOfWork unitOfWork, PreviousDataParser previousDataParser) : base(mapper)
    {
        _unitOfWork = unitOfWork;
        _previousDataParser = previousDataParser;
    }

    public async Task<GetHistoricalAuditByTicketResponse> Handle(GetHistoricalAuditByTicketRequest request, CancellationToken cancellationToken)
    {
        var predicate = GetPredicate(request);

        var historic = await _unitOfWork
            .HistoricalAudits
            .GetAllAsync(
                predicate: predicate,
                orderBy: h => h.OrderBy(x => x.ActionDate),
                useCache: false,
                isPaginated: false,
                maxQueryCount: int.MaxValue,
                cancellationToken: cancellationToken
            );

        var historicDtos = Mapper.Map<List<HistoricalAuditItemResponseDto>>(historic.Results);

        foreach (var item in historicDtos)
        {
            if (item.TableName == ReportsTableNames.WeighingScale)
            {
                item.PreviousDataTyped = _previousDataParser.ParsePreviousData<WeighingScalePreviousDataDto>(item.TableName, item.PreviousData);
            }
        }

        return new GetHistoricalAuditByTicketResponse(historicDtos);
    }

    private static Expression<Func<DomainHistoricalAudit, bool>> GetPredicate(GetHistoricalAuditByTicketRequest request)
    {
        return h => h.EntityId == request.IdTicket
                    && h.TableName == ReportsTableNames.WeighingScale;
    }
}
