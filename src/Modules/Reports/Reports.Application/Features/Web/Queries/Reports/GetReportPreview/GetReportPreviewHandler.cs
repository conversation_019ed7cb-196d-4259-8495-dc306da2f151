using System.Linq.Expressions;
using AutoMapper;
using MediatR;
using Orion.SharedKernel.Application.Common.Mapping;
using Orion.SharedKernel.Application.Exceptions;
using Orion.SharedKernel.Domain.Entities.Headers;
using Reports.Application.Common.Helpers;
using Reports.Application.DTOs.Reports;
using Reports.Domain;
using Reports.Domain.Common;
using Reports.Domain.Constants;
using Reports.Domain.Entities;
using Reports.Domain.ValueObjects;

namespace Reports.Application.Features.Web.Queries.Reports.GetReportPreview;

internal class GetReportPreviewHandler : MappingService, IRequestHandler<GetReportPreviewRequest, GetReportPreviewResponse>
{
    private readonly IReportsUnitOfWork _unitOfWork;
    private readonly HeadersValuesProvider _headersValuesProvider;

    public GetReportPreviewHandler(IMapper mapper,
        IReportsUnitOfWork unitOfWork,
        HeadersValuesProvider headersValuesProvider) : base(mapper)
    {
        _unitOfWork = unitOfWork;
        _headersValuesProvider = headersValuesProvider;
    }

    public async Task<GetReportPreviewResponse> Handle(GetReportPreviewRequest request, CancellationToken cancellationToken)
    {
        var filter = ReportFilter.Create(
            fromDate: request.FromDate,
            toDate: request.ToDate,
            nuap: request.NUAP,
            licensePlate: request.LicensePlate,
            weighinId: request.WeighinId,
            routeCode: request.RouteCode,
            existsInFormat14: request.ExistsInFormat14,
            reportType: request.ReportType!,
            originType: request.OriginType,
            nit: request.NIT);
        
        return filter.ReportType switch
        {
            ReportType.Format14 => await GetReportFormat14PreviewResponse(filter,
                ReportHelper.GetSortingOptionsF14(request.SortDirection, request.SortFields), cancellationToken),
            ReportType.Format34 => await GetReportFormat34PreviewResponse(filter,
                ReportHelper.GetSortingOptionsF34(request.SortDirection, request.SortFields), cancellationToken),
            _ => throw new OrionException(
                _unitOfWork.ErrorService.GenerateError(new ReportTypeIsInvalid(filter.ReportType.ToString())))
        };
    }

    private async Task<GetReportPreviewResponse> GetReportFormat14PreviewResponse(ReportFilter request, SortingOptions sortingOptions, CancellationToken cancellationToken)
    {
        var predicate = GetPredicateForReportFormat14(request);

        var reportFormat14 = await _unitOfWork.ReportsFormat14.GetFilteredReportAsync(
            predicate: predicate,
            pageNumber: _headersValuesProvider.Request.PageNumber,
            pageSize: _headersValuesProvider.Request.PageSize,
            sortingOptions: sortingOptions,
            cancellationToken: cancellationToken);
        
        var tolls = (await _unitOfWork
            .Tolls
            .GetAllAsync(
                isPaginated: false,
                cancellationToken: cancellationToken)).Results;

        var processedReport = ReportFormat14Factory
            .Create(reportFormat14.Results, tolls, request.FromDate)
            .GetReportFormat14();
        
        var reportFormat14Response = Mapper
            .Map<IReadOnlyList<F14PreviewResponseDto>>(processedReport);

        _headersValuesProvider.Response.TotalRecords = reportFormat14Response.Count.ToString();

        return new GetReportPreviewResponse(reportFormat14Response);
    }

    private async Task<GetReportPreviewResponse> GetReportFormat34PreviewResponse(ReportFilter request,  SortingOptions sortingOptions, CancellationToken cancellationToken)
    {
        var predicate = GetPredicateForReportFormat34(request);
        
        var reportFormat34 = await _unitOfWork.ReportsFormat34.GetFilteredReportAsync(
            predicate: predicate,
            pageNumber: _headersValuesProvider.Request.PageNumber,
            pageSize: _headersValuesProvider.Request.PageSize,
            sortingOptions: sortingOptions,
            cancellationToken: cancellationToken);

        var reportFormat34Response = Mapper
            .Map<IReadOnlyList<F34PreviewResponseDto>>(reportFormat34.Results);

        _headersValuesProvider.Response.TotalRecords = reportFormat34.TotalRecords.ToString();

        return new GetReportPreviewResponse(reportFormat34Response);
    }

    private static Expression<Func<ReportFormat14, bool>> GetPredicateForReportFormat14(ReportFilter request)
    {
        return report => report.VehicleArrival >= request.FromDate
                         && report.VehicleArrival < request.ToDate
                         && (request.NUAP == null
                             || report.NUAP == request.NUAP)
                         && (string.IsNullOrEmpty(request.LicensePlate)
                             || report.LicensePlate == request.LicensePlate)
                         && (request.WeighinId == null
                             || report.ServiceTicketId == request.WeighinId)
                         && (request.RouteCode == null
                             || report.ExtendedRouteCode == request.RouteCode);
    }

    private static Expression<Func<ReportFormat34, bool>> GetPredicateForReportFormat34(ReportFilter request)
    {
        return report => report.FilteredDate >= request.FromDate
                         && report.FilteredDate < request.ToDate
                         && (request.OriginType == null
                             || report.OriginType == request.OriginType)
                         && (request.NIT == null
                             || report.CompanyNIT == request.NIT)
                         && (string.IsNullOrEmpty(request.LicensePlate)
                            || report.LicensePlate == request.LicensePlate)
                         && (request.WeighinId == null
                             || report.ServiceTicketId == request.WeighinId)
                         && (request.ExistsInFormat14 == null
                             || report.ExistsInReport14 == request.ExistsInFormat14);
    }
}