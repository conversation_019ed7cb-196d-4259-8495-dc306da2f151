using System.Collections.ObjectModel;
using System.IO.Compression;
using System.Linq.Expressions;
using AutoMapper;
using MediatR;
using Microsoft.AspNetCore.Http;
using Orion.SharedKernel.Application.Common.Mapping;
using Reports.Application.Common.Extensions;
using Reports.Application.Common.Helpers;
using Reports.Domain;
using Reports.Domain.Common;
using Reports.Domain.Entities;
using Reports.Domain.ValueObjects;
using Shared.Application.Common.Services;

namespace Reports.Application.Features.Web.Queries.Reports.ExportCompressedReports;

internal class ExportCompressedReportsHandler : MappingService, IRequestHandler<ExportCompressedReportsRequest, ExportCompressedReportsResponse>
{
    private readonly IReportsUnitOfWork _unitOfWork;
    private readonly IFileExporterService _fileExporterService;
    private const string DefaultCsvDateFormat = "dd/MM/yyyy HH:mm";
    private const string SUIFormat34BaseFilenameTemplate = "SUI_DISPFIN_{0}.{1}";
    private const string SUIFormat14BaseFilenameTemplate = "SUI_RECOL_{0}_{1}_{2}.{3}";
    private const string SUICompressedFilenameTemplate = "SUI_{0}.{1}";
    private const string DefaultTimeZoneRegion = "America/Bogota";

    public ExportCompressedReportsHandler(IMapper mapper, IReportsUnitOfWork unitOfWork, IFileExporterService fileExporterService) : base(mapper)
    {
        _unitOfWork = unitOfWork;
        _fileExporterService = fileExporterService;
    }

    public async Task<ExportCompressedReportsResponse> Handle(ExportCompressedReportsRequest request, CancellationToken cancellationToken)
    {
        byte[] fileContents;
        
        var (regulatoryReport34CSV, regulatoryReport34Filename) = await GetReportFormat34(request.FromDate,
            request.ToDate,
            ReportHelper.GetSortingOptionsF34(request.SortDirection, request.SortFields),
            cancellationToken);
        
        var regulatoryReportsFormat14 = await GetReportsFormat14ByNUAP(request.FromDate,
            request.ToDate,
            ReportHelper.GetSortingOptionsF14(request.SortDirection, request.SortFields),
            cancellationToken);
        
        using (var memoryStream = new MemoryStream())
        {
            using (var archive = new ZipArchive(memoryStream, ZipArchiveMode.Create, true))
            {
                await WriteToArchiveAsync(archive, regulatoryReport34CSV, regulatoryReport34Filename, cancellationToken);

                await Task.WhenAll(regulatoryReportsFormat14.Select(async report =>
                {
                    await WriteToArchiveAsync(archive, report.Item1, report.Item2, cancellationToken);
                }));
            }

            fileContents = memoryStream.ToArray();
        }

        var compressedFilename = string.Format(SUICompressedFilenameTemplate, $"{request.FromDate:yyyyMM}", "zip");

        return new ExportCompressedReportsResponse(Results.File(fileContents: fileContents,
            contentType: "application/zip",
            fileDownloadName: compressedFilename));
    }

    private async Task<ReadOnlyCollection<Tuple<byte[], string>>> GetReportsFormat14ByNUAP(DateOnly fromDate, DateOnly toDate, SortingOptions sortingOptions , CancellationToken cancellationToken)
    {
        var predicate = GetPredicateForReportFormat14(fromDate, toDate);

        var reportData = await _unitOfWork
            .ReportsFormat14
            .GetFilteredReportAsync(
                predicate: predicate,
                isPaginated: false,
                sortingOptions: sortingOptions,
                cancellationToken: cancellationToken);

        var tolls = (await _unitOfWork
            .Tolls
            .GetAllAsync(
                isPaginated: false,
                cancellationToken: cancellationToken)).Results;

        var processedReport = ReportFormat14Factory
            .Create(reportData.Results, tolls, fromDate)
            .GetReportFormat14();

        var reportGroupedByNUAP = processedReport
            .GroupBy(x => x.NUAP)
            .Select(x =>
                new
                {
                    NUAP = x.Key,
                    RecyclingArea = x.First().RecyclingArea,
                    List = x.Select(y => y.ToSUIReportFormat14())
                }
            );

        var result = new List<Tuple<byte[], string>>();

        foreach (var report in reportGroupedByNUAP)
        {
            var csvData = _fileExporterService
                .GetCsvBytes(data: report.List, dateFormats: DefaultCsvDateFormat)
                .RemoveQuotes();

            var filename = string.Format(SUIFormat14BaseFilenameTemplate,
                report.NUAP, $"{report.RecyclingArea[..3].ToUpper()}", $"{fromDate:yyyyMM}", "csv");

            result.Add(new Tuple<byte[], string>(csvData, filename));
        }

        return result.AsReadOnly();
    }

    private async Task<(byte[], string)> GetReportFormat34(DateOnly fromDate, DateOnly toDate, SortingOptions sortingOptions, CancellationToken cancellationToken)
    {
        var predicate = GetPredicateForReportFormat34(fromDate, toDate);

        var reportData = await _unitOfWork
            .ReportsFormat34
            .GetFilteredReportAsync(
                predicate: predicate,
                isPaginated: false,
                sortingOptions: sortingOptions,
                cancellationToken: cancellationToken);

        var formattedData = reportData
            .Results
            .Select(entry => entry.ToSUIReportFormat34());

        var csvData = _fileExporterService
            .GetCsvBytes(data: formattedData, dateFormats: DefaultCsvDateFormat)
            .RemoveQuotes();

        var filename = string.Format(SUIFormat34BaseFilenameTemplate, $"{fromDate:yyyyMM}", "csv");

        return (csvData, filename);
    }

    private static Expression<Func<ReportFormat34, bool>> GetPredicateForReportFormat34(DateOnly fromDate, DateOnly toDate)
    {
        return report => report.FilteredDate >= fromDate
                         && report.FilteredDate < toDate
                         && report.Tons > 0;
    }

    private static Expression<Func<ReportFormat14, bool>> GetPredicateForReportFormat14(DateOnly fromDate, DateOnly toDate)
    {
        return report => report.VehicleArrival >= fromDate
                         && report.VehicleArrival < toDate;
    }

    private static async Task WriteToArchiveAsync(ZipArchive archive, byte[] data, string filename, CancellationToken cancellationToken)
    {
        var entry = archive.CreateEntry(filename);

        var colombiaTimeZone = TimeZoneInfo.FindSystemTimeZoneById(DefaultTimeZoneRegion);
        entry.LastWriteTime = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, colombiaTimeZone);

        await using var stream = entry.Open();

        await stream.WriteAsync(data, cancellationToken);
    }
}