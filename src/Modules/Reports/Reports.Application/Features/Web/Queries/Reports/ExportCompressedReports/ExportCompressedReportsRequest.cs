using MediatR;

namespace Reports.Application.Features.Web.Queries.Reports.ExportCompressedReports;

public record ExportCompressedReportsRequest : IRequest<ExportCompressedReportsResponse>
{
    public DateOnly FromDate { get; init; }
    public DateOnly ToDate { get; init; }
    public string? SortFields { get; init; }
    public string? SortDirection { get; init; }

    public ExportCompressedReportsRequest(DateTime fromDate, DateTime toDate, string? sortFields, string? sortDirection)
    {
        FromDate = DateOnly.FromDateTime(fromDate);
        ToDate = DateOnly.FromDateTime(toDate);
        SortFields = sortFields;
        SortDirection = sortDirection;
    }
}