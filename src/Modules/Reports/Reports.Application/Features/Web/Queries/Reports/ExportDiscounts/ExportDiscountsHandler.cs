using System.Collections.Immutable;
using System.Linq.Expressions;
using AutoMapper;
using MediatR;
using Microsoft.AspNetCore.Http;
using Orion.SharedKernel.Application.Common.Mapping;
using Orion.SharedKernel.Application.Exceptions;
using OrionConstants = Orion.SharedKernel.Domain.Constants;
using Orion.SharedKernel.Domain.Entities.Headers;
using Reports.Application.DTOs.Reports;
using Reports.Domain;
using Reports.Domain.Constants;
using Reports.Domain.Entities;
using Shared.Application.Common.Services;
using Shared.Domain.Constants;

namespace Reports.Application.Features.Web.Queries.Reports.ExportDiscounts;

internal class ExportDiscountsHandler : MappingService, IRequestHandler<ExportDiscountsRequest, ExportDiscountsResponse>
{
    private readonly IReportsUnitOfWork _unitOfWork;
    private readonly IFileExporterService _fileExporterService;
    private readonly HeadersValuesProvider _headersValuesProvider;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private const long EmvariasNIT = 890905055;

    public ExportDiscountsHandler(IMapper mapper, IReportsUnitOfWork unitOfWork, IFileExporterService fileExporterService,
        HeadersValuesProvider headersValuesProvider, IHttpContextAccessor httpContextAccessor) : base(mapper) => (_unitOfWork, _fileExporterService, _headersValuesProvider, _httpContextAccessor) = (unitOfWork, fileExporterService, headersValuesProvider, httpContextAccessor);

    public async Task<ExportDiscountsResponse> Handle(ExportDiscountsRequest request, CancellationToken cancellationToken)
    {
        var discounts = await GetDiscounts(request, cancellationToken);

        var mappedDiscounts = Mapper.Map<List<DiscountsSpanishModel>>(discounts);

        return GetExportDiscountsResponse(mappedDiscounts, request.FromDate);
    }

    private ExportDiscountsResponse GetExportDiscountsResponse(List<DiscountsSpanishModel> mappedDiscounts, DateTime filteredPeriod)
    {
        var excelHeaderTitle = $"REPORTE DE DESCUENTOS DEL PERIODO {filteredPeriod:yyyy-MM}";

        var headerValue = _headersValuesProvider.Request.ExcelFormat;

        if (string.IsNullOrEmpty(headerValue))
        {
            headerValue = _httpContextAccessor.HttpContext?.Request.Headers["X-ExcelFormat"];
        }

        if (!Enum.TryParse(headerValue, ignoreCase: true, out ExcelFormat excelFormat))
        {
            throw new OrionException(
                _unitOfWork.ErrorService.GenerateError(new ReportExportTypeIsInvalid(headerValue)));
        }

        return excelFormat switch
        {
            ExcelFormat.xls => new ExportDiscountsResponse(Results.File(
                _fileExporterService.GetExcelBytes(
                    data: mappedDiscounts, headerRowTitle: excelHeaderTitle),
                contentType: ExcelFormatContentType.Xls,
                fileDownloadName: GetFileName(nameof(ExcelFormat.xls), filteredPeriod)
            )),
            ExcelFormat.csv => new ExportDiscountsResponse(Results.File(
                _fileExporterService.GetCsvBytes(
                    data: mappedDiscounts),
                contentType: ExcelFormatContentType.Csv,
                fileDownloadName: GetFileName(nameof(ExcelFormat.csv), filteredPeriod)
            )),
            ExcelFormat.xlsx => new ExportDiscountsResponse(Results.File(
                _fileExporterService.GetExcelBytes(
                    data: mappedDiscounts, headerRowTitle: excelHeaderTitle),
                contentType: ExcelFormatContentType.Xlsx,
                fileDownloadName: GetFileName(nameof(ExcelFormat.xlsx), filteredPeriod)
            )),
            _ => throw new OrionException(
                _unitOfWork.ErrorService.GenerateError(new ReportExportTypeIsInvalid(headerValue)))
        };
    }

    private static string GetFileName(string exportFormat, DateTime filteredPeriod) => $"Descuentos {filteredPeriod:yyyyMM}.{exportFormat}";

    private async Task<IEnumerable<Discount>> GetDiscounts(ExportDiscountsRequest request,
        CancellationToken cancellationToken)
    {
        var discounts = await _unitOfWork
            .ReportsFormat34
            .GetAllAsync(isPaginated: false,
                predicate: GetPredicateForF34Discounts(request),
                useCache: false,
                cancellationToken: cancellationToken);

        var collectionByMicroroute = (await _unitOfWork
            .CollectionByMicrorouteRepository
            .GetAllAsync(isPaginated: false,
                predicate: GetPredicateForCollectionByMicroroute(request),
                useCache: false,
                cancellationToken: cancellationToken))
            .Results
            .ToImmutableList();

        return discounts
            .Results
            .Select(x =>
                new Discount(
                    id: x.ServiceTicketId,
                    date: x.DepartureDate,
                    licensePlate: x.LicensePlate,
                    tons: x.Tons,
                    collections: collectionByMicroroute));
    }

    private static Expression<Func<ReportFormat34, bool>> GetPredicateForF34Discounts(ExportDiscountsRequest request)
    {
        return report => report.FilteredDate >= DateOnly.FromDateTime(request.FromDate)
                         && report.FilteredDate < DateOnly.FromDateTime(request.ToDate)
                         && !report.ExistsInReport14
                         && report.CompanyNIT == EmvariasNIT;
    }

    private static Expression<Func<CollectionByMicroroute, bool>> GetPredicateForCollectionByMicroroute(ExportDiscountsRequest request)
    {
        return report => report.WeighingDate >= request.FromDate
                         && report.WeighingDate < request.ToDate;
    }
}