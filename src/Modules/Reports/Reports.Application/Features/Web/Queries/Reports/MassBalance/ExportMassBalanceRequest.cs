using MediatR;
using Reports.Application.DTOs.Reports.MassBalance;

namespace Reports.Application.Features.Web.Queries.Reports.MassBalance;

/// <summary>
/// Request for exporting Mass Balance report to Excel format
/// </summary>
public record ExportMassBalanceRequest : MassBalanceRequestDto, IRequest<ExportMassBalanceResponse>
{
    /// <summary>
    /// Excel format for the export (xlsx, xls, csv)
    /// </summary>
    /// <example>xlsx</example>
    public string? ExcelFormat { get; init; }
}
