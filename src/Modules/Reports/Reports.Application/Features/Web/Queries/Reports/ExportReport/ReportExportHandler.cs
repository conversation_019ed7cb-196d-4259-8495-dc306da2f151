using AutoMapper;
using MediatR;
using Microsoft.AspNetCore.Http;
using Orion.SharedKernel.Application.Common.Mapping;
using Orion.SharedKernel.Application.Exceptions;
using OrionConstants = Orion.SharedKernel.Domain.Constants;
using Orion.SharedKernel.Domain.Entities.Headers;
using Reports.Application.DTOs.Reports;
using Reports.Domain;
using Reports.Domain.Constants;
using Reports.Domain.Entities;
using System.Linq.Expressions;
using Reports.Application.Common.Helpers;
using Reports.Domain.Common;
using Reports.Domain.ValueObjects;
using Shared.Application.Common.Services;
using Shared.Domain.Constants;
using Reports.Application.Features.Web.Queries.Clients.GetAllClients;

namespace Reports.Application.Features.Web.Queries.Reports.ExportReport;

internal class ReportExportHandler : MappingService, IRequestHandler<ReportExportRequest, ReportExportResponse>
{
    private readonly IReportsUnitOfWork _unitOfWork;
    private readonly IFileExporterService _fileExporterService;
    private readonly HeadersValuesProvider _headersValuesProvider;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IMediator _mediator;
    private const string DefaultExcelDateFormat = "yyyy/MM/dd HH:mm:ss";
    private const string ReportFormat14Prefix = "SUI_RECOL_";
    private const string ReportFormat34Prefix = "SUI_DISPFIN_";
    private const string ReportWeighingScalePrefix = "REPORTE_PESOS";
    private const string ReportFormat14SuffixFilteredByNuap = "_NUAP";

    public ReportExportHandler(
        IMapper mapper,
        IReportsUnitOfWork unitOfWork,
        IFileExporterService fileExporterService,
        HeadersValuesProvider headersValuesProvider,
        IHttpContextAccessor httpContextAccessor,
        IMediator mediator
    ) : base(mapper) => (_unitOfWork, _fileExporterService, _headersValuesProvider, _httpContextAccessor, _mediator) = (unitOfWork, fileExporterService, headersValuesProvider, httpContextAccessor, mediator);

    public async Task<ReportExportResponse> Handle(ReportExportRequest request, CancellationToken cancellationToken)
    {
        var filter = ReportFilter.Create(
            fromDate: request.FromDate,
            toDate: request.ToDate,
            nuap: request.NUAP,
            licensePlate: request.LicensePlate,
            weighinId: request.WeighinId,
            routeCode: request.RouteCode,
            existsInFormat14: request.ExistsInFormat14,
            reportType: request.ReportType!,
            originType: request.OriginType,
            nit: request.NIT);

        return filter.ReportType switch
        {
            ReportType.Format14 => GenerateReport(await GetReportFormat14ExportResponse(filter,
                    ReportHelper.GetSortingOptionsF14(request.SortDirection, request.SortFields),
                    cancellationToken),
                filter),
            ReportType.Format34 => GenerateReport(
                await GetReportFormat34ExportResponse(filter,
                    ReportHelper.GetSortingOptionsF34(request.SortDirection, request.SortFields),
                    cancellationToken),
                filter),
            ReportType.WeighingScale => GenerateWeighingScaleReport(
                await GetReportWeighingScaleExportResponse(filter,
                    ReportHelper.GetSortingOptionsWeighingScale(request.SortDirection, request.SortFields),
                    cancellationToken),
                filter),
            _ => throw new OrionException(
                _unitOfWork.ErrorService.GenerateError(new ReportTypeIsInvalid(filter.ReportType.ToString())))
        };
    }

    private async Task<IReadOnlyList<F14ExportResponseDto>> GetReportFormat14ExportResponse(ReportFilter request, SortingOptions sortingOptions, CancellationToken cancellationToken)
    {
        var predicate = GetPredicateForReportFormat14(request);

        var reportFormat14 = await _unitOfWork
            .ReportsFormat14
            .GetFilteredReportAsync(
                predicate: predicate,
                isPaginated: false,
                sortingOptions: sortingOptions,
                cancellationToken: cancellationToken);

        var tolls = (await _unitOfWork
            .Tolls
            .GetAllAsync(
                isPaginated: false,
                cancellationToken: cancellationToken)).Results;

        var processedReport = ReportFormat14Factory
            .Create(reportFormat14.Results, tolls, request.FromDate)
            .GetExtendedReportFormat14();

        var reportFormat14Response = Mapper
            .Map<IReadOnlyList<F14ExportResponseDto>>(processedReport);

        return reportFormat14Response;
    }

    private async Task<IReadOnlyList<F34ExportResponseDto>> GetReportFormat34ExportResponse(ReportFilter request, SortingOptions sortingOptions, CancellationToken cancellationToken)
    {
        var predicate = GetPredicateForReportFormat34(request);

        var reportFormat34 = await _unitOfWork
            .ReportsFormat34
            .GetFilteredReportAsync(
                predicate: predicate,
                isPaginated: false,
                sortingOptions: sortingOptions,
                cancellationToken: cancellationToken);

        var reportFormat34Response = Mapper
            .Map<IReadOnlyList<F34ExportResponseDto>>(reportFormat34.Results);

        return reportFormat34Response;
    }

    private async Task<IReadOnlyList<WeighingScaleExportResponseDto>> GetReportWeighingScaleExportResponse(ReportFilter request, SortingOptions sortingOptions, CancellationToken cancellationToken)
    {
        var predicate = GetPredicateForReportWeighingScale(request);

        var reportWeighingScale = await _unitOfWork
            .WeighingScales
            .GetFilteredReportAsync(
                predicate: predicate,
                isPaginated: false,
                sortingOptions: sortingOptions,
                cancellationToken: cancellationToken);

        var clientsRequest = new GetAllClientsRequest();
        var clientsResponse = await _mediator.Send(clientsRequest, cancellationToken);

        var reportWeighingScaleResponse = Mapper
            .Map<IReadOnlyList<WeighingScaleExportResponseDto>>(reportWeighingScale.Results)
            .Select(dto => dto with
            {
                NombreEmpresa = clientsResponse.Clients.ToDictionary(c => c.NIT, c => c.FullName)
                .GetValueOrDefault(dto.NIT, "")
            })
            .ToList();

        return reportWeighingScaleResponse;
    }

    private static Expression<Func<ReportFormat14, bool>> GetPredicateForReportFormat14(ReportFilter request)
    {
        return report => report.VehicleArrival >= request.FromDate
                         && report.VehicleArrival < request.ToDate
                         && (request.NUAP == null
                             || report.NUAP == request.NUAP)
                         && (string.IsNullOrEmpty(request.LicensePlate)
                             || report.LicensePlate == request.LicensePlate)
                         && (request.WeighinId == null
                             || report.ServiceTicketId == request.WeighinId)
                         && (request.RouteCode == null
                             || report.ExtendedRouteCode == request.RouteCode);
    }

    private static Expression<Func<ReportFormat34, bool>> GetPredicateForReportFormat34(ReportFilter request)
    {
        return report => report.FilteredDate >= request.FromDate
                         && report.FilteredDate < request.ToDate
                         && (request.OriginType == null
                             || report.OriginType == request.OriginType)
                         && (request.NIT == null
                             || report.CompanyNIT == request.NIT)
                         && (string.IsNullOrEmpty(request.LicensePlate)
                             || report.LicensePlate == request.LicensePlate)
                         && (request.WeighinId == null
                             || report.ServiceTicketId == request.WeighinId)
                         && (request.ExistsInFormat14 == null
                             || report.ExistsInReport14 == request.ExistsInFormat14);
    }

    private static Expression<Func<WeighingScale, bool>> GetPredicateForReportWeighingScale(ReportFilter request)
    {
        return weighingScale => DateOnly.FromDateTime(weighingScale.EntryDate) >= request.FromDate
                                && DateOnly.FromDateTime(weighingScale.EntryDate) < request.ToDate
                                && (request.NUAP == null
                                    || weighingScale.NUAP == request.NUAP)
                                && (string.IsNullOrEmpty(request.LicensePlate)
                                    || weighingScale.LicensePlate == request.LicensePlate)
                                && (request.WeighinId == null
                                    || weighingScale.Id == request.WeighinId)
                                && (request.NIT == null
                                    || weighingScale.NIT == request.NIT);
    }
    private ReportExportResponse GenerateReport<T>(IReadOnlyList<T> reportItems, ReportFilter reportFilter) where T : class
    {
        var headerValue = _headersValuesProvider.Request.ExcelFormat;

        if (string.IsNullOrEmpty(headerValue))
        {
            headerValue = _httpContextAccessor.HttpContext?.Request.Headers["X-ExcelFormat"];
        }

        if (!Enum.TryParse(headerValue, ignoreCase: true, out ExcelFormat excelFormat))
        {
            throw new OrionException(
                _unitOfWork.ErrorService.GenerateError(new ReportExportTypeIsInvalid(headerValue)));
        }

        return excelFormat switch
        {
            ExcelFormat.xlsx => new ReportExportResponse
            {
                TotalRecords = reportItems.Count,
                Result = GenerateFile(
                    _fileExporterService.GetExcelBytes<T>(data: reportItems,
                    dateFormats: DefaultExcelDateFormat),
                    contentType: ExcelFormatContentType.Xlsx,
                    fileExtension: nameof(ExcelFormat.xlsx),
                    filter: reportFilter)
            },
            ExcelFormat.xls => new ReportExportResponse
            {
                TotalRecords = reportItems.Count,
                Result = GenerateFile(
                    _fileExporterService.GetExcelBytes<T>(data: reportItems,
                    dateFormats: DefaultExcelDateFormat),
                    contentType: ExcelFormatContentType.Xls,
                    fileExtension: nameof(ExcelFormat.xls),
                    filter: reportFilter)
            },
            ExcelFormat.csv => new ReportExportResponse
            {
                TotalRecords = reportItems.Count,
                Result = GenerateFile(
                    _fileExporterService.GetCsvBytes<T>(data: reportItems,
                    dateFormats: DefaultExcelDateFormat),
                    contentType: ExcelFormatContentType.Csv,
                    fileExtension: nameof(ExcelFormat.csv),
                    filter: reportFilter)
            },
            _ => throw new OrionException(
                _unitOfWork.ErrorService.GenerateError(new ReportExportTypeIsInvalid(headerValue)))
        };
    }

    private static IResult GenerateFile(byte[] fileContents,
        string contentType,
        string fileExtension,
        ReportFilter filter)
    {
        var filename = "";

        switch (filter.ReportType)
        {
            case ReportType.Format14 when filter.NUAP.HasValue:
                filename += ReportFormat14Prefix + $"{filter.FromDate:yyyyMM}" + ReportFormat14SuffixFilteredByNuap;
                break;
            case ReportType.Format14:
                filename += ReportFormat14Prefix + $"{filter.FromDate:yyyyMM}";
                break;
            case ReportType.Format34:
                filename += ReportFormat34Prefix + $"{filter.FromDate:yyyyMM}";
                break;
            case ReportType.WeighingScale:
                filename += ReportWeighingScalePrefix;
                break;
            default:
                filename += $"Reporte_{filter.FromDate:yyyyMMddHHmmss}";
                break;
        }

        return Results.File(fileContents: fileContents,
            contentType: contentType,
            fileDownloadName: $"{filename}.{fileExtension}");
    }

    private ReportExportResponse GenerateWeighingScaleReport(IReadOnlyList<WeighingScaleExportResponseDto> reportItems, ReportFilter reportFilter)
    {
        var headerValue = _headersValuesProvider.Request.ExcelFormat;

        if (string.IsNullOrEmpty(headerValue))
        {
            headerValue = _httpContextAccessor.HttpContext?.Request.Headers["X-ExcelFormat"];
        }

        if (!Enum.TryParse(headerValue, ignoreCase: true, out ExcelFormat excelFormat))
        {
            throw new OrionException(
                _unitOfWork.ErrorService.GenerateError(new ReportExportTypeIsInvalid(headerValue)));
        }

        var reportGenerationDate = DateTime.Now.ToString("yyyy-MM-dd HH:mm");
        var filters = BuildFilterDescription(reportFilter);
        var headerTitle = $"REPORTE DE PESOS\r\nGenerado: {reportGenerationDate}\r\nFiltros Aplicados: {filters}";

        return excelFormat switch
        {
            ExcelFormat.xlsx => new ReportExportResponse
            {
                TotalRecords = reportItems.Count,
                Result = GenerateFile(
                    _fileExporterService.GetExcelBytes(data: reportItems,
                        headerRowTitle: headerTitle,
                        dateFormats: DefaultExcelDateFormat),
                    contentType: ExcelFormatContentType.Xlsx,
                    fileExtension: nameof(ExcelFormat.xlsx),
                    filter: reportFilter)
            },
            ExcelFormat.xls => new ReportExportResponse
            {
                TotalRecords = reportItems.Count,
                Result = GenerateFile(
                    _fileExporterService.GetExcelBytes(data: reportItems,
                        headerRowTitle: headerTitle,
                        dateFormats: DefaultExcelDateFormat),
                    contentType: ExcelFormatContentType.Xls,
                    fileExtension: nameof(ExcelFormat.xls),
                    filter: reportFilter)
            },
            ExcelFormat.csv => new ReportExportResponse
            {
                TotalRecords = reportItems.Count,
                Result = GenerateFile(
                    _fileExporterService.GetCsvBytes(data: reportItems,
                        dateFormats: DefaultExcelDateFormat),
                    contentType: ExcelFormatContentType.Csv,
                    fileExtension: nameof(ExcelFormat.csv),
                    filter: reportFilter)
            },
            _ => throw new OrionException(
                _unitOfWork.ErrorService.GenerateError(new ReportExportTypeIsInvalid(headerValue)))
        };
    }

    private static string BuildFilterDescription(ReportFilter filter)
    {
        var filterParts = new List<string>();

        filterParts.Add($"Fecha desde: {filter.FromDate:yyyy-MM-dd}");
        filterParts.Add($"Fecha hasta: {filter.ToDate:yyyy-MM-dd}");

        if (!string.IsNullOrEmpty(filter.LicensePlate))
            filterParts.Add($"Placa: {filter.LicensePlate}");

        if (filter.WeighinId.HasValue)
            filterParts.Add($"ID Pesaje: {filter.WeighinId}");

        if (filter.NIT.HasValue)
            filterParts.Add($"NIT: {filter.NIT}");

        if (filter.OriginType.HasValue)
            filterParts.Add($"Tipo Origen: {filter.OriginType}");

        return string.Join(", ", filterParts);
    }
}