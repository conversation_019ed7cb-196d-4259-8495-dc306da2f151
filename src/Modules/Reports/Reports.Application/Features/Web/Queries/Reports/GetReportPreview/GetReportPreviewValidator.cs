using FluentValidation;
using Reports.Domain.Common;
using Reports.Domain.Constants;
using Shared.Application.Common.ValidationExtensions;

namespace Reports.Application.Features.Web.Queries.Reports.GetReportPreview;

internal class GetReportPreviewValidator : AbstractValidator<GetReportPreviewRequest>
{
    public GetReportPreviewValidator()
    {
        RuleFor(p => p.ReportType)
            .NotEmpty()
            .WithMessage("El tipo de reporte no puede estar vacío")
            .IsValidEnum(typeof(ReportType))
            .WithMessage("El tipo de reporte no es válido");
        
        RuleFor(p => p.FromDate)
            .Must(p => p != default)
            .WithMessage("Debe ingresar una fecha de entrada válida.");
        
        RuleFor(p => p.ToDate)
            .Must(p => p != default)
            .WithMessage("Debe ingresar una fecha de salida válida.");
        
        RuleFor(p => p.FromDate)
            .LessThanOrEqualTo(p => p.ToDate)
            .WithMessage("La fecha de entrada no puede ser mayor a la fecha de salida");
        
        RuleFor(p => p.FromDate)
            .Must((p, _) =>  p.FromDate.Month == p.ToDate.Month
                             && p.FromDate.Year == p.ToDate.Year)
            .WithMessage("Las fechas deben ser del mismo mes y año");
        
        RuleFor(p => p.FromDate)
            .Must((p, _) => p.FromDate.CompareTo(DateTime.Now) <= 0)
            .WithMessage("La fecha de entrada no puede ser mayor a la fecha actual");
        
        RuleFor(p => p.ToDate)
            .Must((p, _) => p.ToDate.CompareTo(DateTime.Now) <= 0)
            .WithMessage("La fecha de salida no puede ser mayor a la fecha actual");
        
        RuleFor(p => p.LicensePlate)
            .MinimumLength(5)
            .WithMessage("La placa del vehiculo no puede tener menos de 5 caracteres")
            .When(p => !string.IsNullOrEmpty(p.LicensePlate));

        RuleFor(p => p.WeighinId)
            .GreaterThan(0)
            .WithMessage("El Id del ticket debe ser mayor a 0")
            .When(p => p.WeighinId is not null);
        
        When(p => nameof(ReportType.Format14).Equals(p.ReportType), () =>
        {
            RuleFor(p => p.NUAP)
                .GreaterThan(0)
                .WithMessage("El NUAP debe ser mayor a 0")
                .When(p => p.NUAP is not null);
        });
        
        When(p => nameof(ReportType.Format34).Equals(p.ReportType), () =>
        {
            RuleFor(p => p.OriginType)
                .IsValidEnum(typeof(OriginType))
                .WithMessage("El tipo de origen no es válido")
                .When(p => !string.IsNullOrEmpty(p.OriginType));
            
            RuleFor(p => p.NIT)
                .GreaterThan(0)
                .WithMessage("El NIT debe ser mayor a 0")
                .When(p => p.NIT is not null);
        });
        
        RuleFor(p => p.SortDirection)
            .IsValidEnum(typeof(SortDirection))
            .WithMessage("El tipo de ordenamiento no es válido")
            .When(p => !string.IsNullOrEmpty(p.SortDirection));
    }
}