using MediatR;
using Reports.Application.DTOs.Reports;

namespace Reports.Application.Features.Web.Queries.Reports.ExportDiscounts;

public record ExportDiscountsRequest : IRequest<ExportDiscountsResponse>
{
    /// <summary>
    /// Fecha desde la cual se filtrarán los datos.
    /// </summary>
    /// <remarks>Formato: YYYY-MM-DD</remarks>
    /// <example>2023-07-14</example>
    public DateTime FromDate { get; init; }

    /// <summary>
    /// Fecha hasta la cual se filtrarán los datos.
    /// </summary>
    /// <remarks>Formato: YYYY-MM-DD</remarks>
    /// <example>2023-07-14</example>
    public DateTime ToDate { get; init; }
}