using FluentValidation;

namespace Reports.Application.Features.Web.Queries.Reports.MassBalance;

public class GetMassBalanceValidator : AbstractValidator<GetMassBalanceRequest>
{
    public GetMassBalanceValidator()
    {
        RuleFor(p => p.FromDate)
            .Must(p => p != default)
            .WithMessage("Debe ingresar una fecha de inicio válida.");
        
        RuleFor(p => p.ToDate)
            .Must(p => p != default)
            .WithMessage("Debe ingresar una fecha de fin válida.");
        
        RuleFor(p => p.FromDate)
            .LessThanOrEqualTo(p => p.ToDate)
            .WithMessage("La fecha de inicio no puede ser mayor a la fecha de fin");
        
        RuleFor(p => p.FromDate)
            .Must((p, _) => p.FromDate.Month == p.ToDate.AddMonths(-1).Month
                             && p.ToDate.Day == 1 && p.FromDate.Day == 1)
            .WithMessage("Las fechas deben ser desde el primer día del mes, hasta el primer día del mes siguiente");
    }
}