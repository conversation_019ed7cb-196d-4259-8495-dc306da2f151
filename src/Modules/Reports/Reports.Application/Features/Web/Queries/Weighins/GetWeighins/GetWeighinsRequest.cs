using MediatR;

namespace Reports.Application.Features.Web.Queries.Weighins.GetWeighins;

public record GetWeighinsRequest : IRequest<GetWeighinsResponse>
{
    /// <summary>
    /// Fecha desde
    /// </summary>
    /// <example>2024-01-04 12:30:51</example>
    public DateTime? FromDate { get; set; }

    /// <summary>
    /// <PERSON><PERSON> hasta
    /// </summary>
    /// <example>2024-01-05 12:30:51</example>
    public DateTime? ToDate { get; set; }

    /// <summary>
    /// Placa del vehículo
    /// </summary>
    /// <example>ABC123</example>
    public string? LicensePlate { get; set; }

    /// <summary>
    /// Número de identificación tributaria
    /// </summary>
    /// <example>*********</example>
    public long? NIT { get; set; }

    /// <summary>
    /// Número de comprobante
    /// </summary>
    /// <example>1234</example>
    public string? TicketNumber { get; set; }

    /// <summary>
    /// Nombre de la empresa/compañía
    /// </summary>
    /// <example>EMPRESA DE ASEO LTDA</example>
    public string? CompanyName { get; set; }
}