using FluentValidation;

namespace Reports.Application.Features.Web.Queries.Weighins.GetWeighins;

public class GetWeighinsValidator : AbstractValidator<GetWeighinsRequest>
{
    public GetWeighinsValidator()
    {
        RuleFor(p => p.FromDate)
            .NotEmpty()
            .WithMessage("La fecha desde no puede estar vacía");

        RuleFor(p => p.ToDate)
            .NotEmpty()
            .WithMessage("La fecha hasta no puede estar vacía");
        
        When(p => p.ToDate != null && p.FromDate != null, () =>
        {
            RuleFor(p => p.ToDate)
                .Must((request, toDate) => toDate.Value.Month == request.FromDate.Value.Month
                    && toDate.Value.Year == request.FromDate.Value.Year)
                .WithMessage("El mes y año de la fecha desde la que se filtra debe ser igual al mes y año de la fecha hasta la que se filtra");
        });
        
        RuleFor(x => x.LicensePlate)
            .MinimumLength(5)
            .When(x => x.LicensePlate != null)
            .WithMessage("La placa del vehículo no puede tener menos de 5 caracteres.");

        RuleFor(x => x.NIT)
            .GreaterThanOrEqualTo(0)
            .When(x => x.NIT != null)
            .WithMessage("El NIT no puede ser menor o igual a 0");
    }
}