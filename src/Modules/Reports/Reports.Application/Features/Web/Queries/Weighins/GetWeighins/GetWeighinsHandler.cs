using System.Linq.Expressions;
using AutoMapper;
using MediatR;
using Microsoft.AspNetCore.Http;
using Orion.SharedKernel.Application.Common.Mapping;
using Orion.SharedKernel.Domain.Entities;
using Reports.Application.DTOs.Weighings;
using Reports.Domain;
using Reports.Domain.Entities;
using Shared.Infrastructure.Http;

namespace Reports.Application.Features.Web.Queries.Weighins.GetWeighins;

internal sealed class GetWeighinsHandler : MappingService, IRequestHandler<GetWeighinsRequest, GetWeighinsResponse>
{
    private readonly IReportsUnitOfWork _unitOfWork;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public GetWeighinsHandler(IMapper mapper, IReportsUnitOfWork classificationUnitOfWork, IHttpContextAccessor httpContextAccessor) : base(mapper)
    {
        _unitOfWork = classificationUnitOfWork;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<GetWeighinsResponse> Handle(GetWeighinsRequest request, CancellationToken cancellationToken)
    {
        var predicate = GetPredicate(request);

        var httpContext = _httpContextAccessor.HttpContext;
        var (pageNumber, pageSize) = httpContext != null
            ? HeadersUtils.GetRequestHeaders(httpContext)
            : (1, 25);

        var weighins = await _unitOfWork
            .WeighingScales
            .GetAllAsync(
                includes: w => w.Town,
                predicate: predicate,
                useCache: false,
                pageNumber: pageNumber,
                pageSize: pageSize,
                maxQueryCount: int.MaxValue,
                cancellationToken: cancellationToken
            );

        var clientsDict = (await _unitOfWork.Clients.GetAllAsync(isPaginated: false, cancellationToken: cancellationToken))
            .Results.ToDictionary(c => c.Id, c => c);

        foreach (var weighing in weighins.Results)
        {
            if (string.IsNullOrEmpty(weighing.CompanyName) && clientsDict.TryGetValue(weighing.NIT, out var client))
            {
                weighing.CompanyName = client.FullName;
            }
        }

        if (!string.IsNullOrEmpty(request.CompanyName))
        {
            weighins.Results = weighins.Results
                .Where(w => w.CompanyName.Contains(request.CompanyName, StringComparison.OrdinalIgnoreCase))
                .ToList();
        }

        if (httpContext != null)
        {
            HeadersUtils.SetResponseHeader(httpContext, weighins.TotalRecords, pageNumber, pageSize);
        }

        var weighinsDtos = Mapper.Map<PaginatedResult<GetWeighingItemResponseDto>>(weighins);

        return new GetWeighinsResponse(weighinsDtos);
    }
    private static Expression<Func<WeighingScale, bool>> GetPredicate(GetWeighinsRequest request)
    {
        long? ticketNumber = null;
        if (!string.IsNullOrEmpty(request.TicketNumber) && long.TryParse(request.TicketNumber, out var parsedId))
        {
            ticketNumber = parsedId;
        }

        return w =>
            w.EntryDate >= request.FromDate
            && w.EntryDate <= request.ToDate
            && (string.IsNullOrEmpty(request.LicensePlate)
                || w.LicensePlate == request.LicensePlate)
            && (request.NIT == null
                || w.NIT == request.NIT)
            && (ticketNumber == null
                || w.Id == ticketNumber);
    }
}