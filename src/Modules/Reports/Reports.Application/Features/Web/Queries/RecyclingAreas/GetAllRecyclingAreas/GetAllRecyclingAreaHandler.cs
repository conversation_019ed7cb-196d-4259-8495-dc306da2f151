using AutoMapper;
using MediatR;
using Orion.SharedKernel.Application.Common.Mapping;
using Orion.SharedKernel.Domain.Entities.Headers;
using Reports.Application.DTOs.RecyclingArea;
using Reports.Domain;

namespace Reports.Application.Features.Web.Queries.RecyclingAreas.GetAllRecyclingAreas;

internal class GetAllRecyclingAreaHandler : MappingService, IRequestHandler<GetAllRecyclingAreaRequest, GetAllRecyclingAreaResponse>
{
    private readonly IReportsUnitOfWork _unitOfWork;
    private readonly HeadersValuesProvider _headersValuesProvider;

    public GetAllRecyclingAreaHandler(IMapper mapper, IReportsUnitOfWork unitOfWork, HeadersValuesProvider headersValuesProvider) : base(mapper)
    {
        _unitOfWork = unitOfWork;
        _headersValuesProvider = headersValuesProvider;
    }

    public async Task<GetAllRecyclingAreaResponse> Handle(GetAllRecyclingAreaRequest request, CancellationToken cancellationToken)
    {
        var recyclingAreas = await _unitOfWork
            .RecyclingAreaRepository
            .GetAllAsync(
                isPaginated: false,
                cancellationToken: cancellationToken
            );
        
        _headersValuesProvider.Response.TotalRecords = recyclingAreas.TotalRecords.ToString();
        
        return new GetAllRecyclingAreaResponse(Mapper.Map<IEnumerable<GetAllRecyclingAreaResponseDto>>(recyclingAreas.Results));
    }
}