using MediatR;
using Reports.Domain;
using Reports.Domain.Constants;
using Reports.Domain.Entities;
using Reports.Domain.Events;

namespace Reports.Application.Features.Web.Events.Weighins.Synchronization;

public class WeighingScaleCancelledSynchronizationHandler : INotificationHandler<WeighingScaleCancelledEvent>
{
    private readonly IReportsUnitOfWork _unitOfWork;

    public WeighingScaleCancelledSynchronizationHandler(IReportsUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    public async Task Handle(WeighingScaleCancelledEvent notification, CancellationToken cancellationToken)
    {
        var weighingScaleIds = notification.WeighingScales.Select(ws => ws.Id).ToList();

        var existingSyncRecords = await _unitOfWork.UrbetrackSynchronizations
            .GetByWeighingScaleIdsAsync(weighingScaleIds, cancellationToken);

        var synchronizationsToCreate = new List<UrbetrackSynchronization>();
        var synchronizationsToUpdate = new List<UrbetrackSynchronization>();

        foreach (var weighingId in notification.WeighingScales.Select(ws => ws.Id))
        {
            var hasPreviousSyncronization = existingSyncRecords.TryGetValue(weighingId, out var existingSync);

            if (hasPreviousSyncronization)
            {
                existingSync!.Status = existingSync?.Status switch
                {
                    SynchronizationStatus.PendingCreation => SynchronizationStatus.Cancelled,
                    SynchronizationStatus.PendingModification => SynchronizationStatus.PendingCancellation,
                    SynchronizationStatus.Synchronized => SynchronizationStatus.PendingCancellation,
                    _ => existingSync?.Status ?? SynchronizationStatus.Invalid
                };

                if (existingSync!.Status == SynchronizationStatus.PendingCancellation)
                {
                    synchronizationsToUpdate.Add(existingSync!);
                }
                continue;
            }

            var newSync = new UrbetrackSynchronization
            {
                WeighingScaleId = weighingId,
                Status = SynchronizationStatus.PendingCancellation,
                UrbetrackInternalId = null,
                RetryCount = 0,
                LastSynchronization = null
            };

            synchronizationsToCreate.Add(newSync);
        }

        if (synchronizationsToUpdate.Any() || synchronizationsToCreate.Any())
            await _unitOfWork.UrbetrackSynchronizations.UpsertRangeAsync(
                synchronizationsToCreate
                    .Concat(synchronizationsToUpdate),
                cancellationToken);

        await _unitOfWork.SaveChangesAsync(cancellationToken);
    }
}