using MediatR;
using Reports.Domain;
using Reports.Domain.Constants;
using Reports.Domain.Entities;
using Reports.Domain.Events;

namespace Reports.Application.Features.Web.Events.Weighins.Audit;

public class WeighingScaleUpdatedEventHandler : INotificationHandler<WeighingScaleUpdatedEvent>
{
    private readonly IReportsUnitOfWork _unitOfWork;

    public WeighingScaleUpdatedEventHandler(IReportsUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    public async Task Handle(WeighingScaleUpdatedEvent notification, CancellationToken cancellationToken)
    {
        var auditRecords = HistoricalAudit
            .ForWeighingScales(notification.CurrentWeighingScales)
            .WithAction(HistoricalMovementType.Modified)
            .ByUser(notification.User)
            .WithPreviousData(notification.PreviousWeighingScales)
            .Build();

        await _unitOfWork.BeginTransactionAsync(cancellationToken);

        await _unitOfWork.HistoricalAudits.BulkInsertAsync(auditRecords, cancellationToken);
        
        await _unitOfWork.CommitAsync(cancellationToken);
    }
}
