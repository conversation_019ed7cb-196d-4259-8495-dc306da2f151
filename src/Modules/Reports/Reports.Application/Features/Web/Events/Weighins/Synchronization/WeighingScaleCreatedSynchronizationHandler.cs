using MediatR;
using Reports.Domain;
using Reports.Domain.Constants;
using Reports.Domain.Entities;
using Reports.Domain.Events;

namespace Reports.Application.Features.Web.Events.Weighins.Synchronization;

public class WeighingScaleCreatedSynchronizationHandler : INotificationHandler<WeighingScaleCreatedEvent>
{
    private readonly IReportsUnitOfWork _unitOfWork;

    public WeighingScaleCreatedSynchronizationHandler(IReportsUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    public async Task Handle(WeighingScaleCreatedEvent notification, CancellationToken cancellationToken)
    {
        var weighingScaleIds = notification.WeighingScales
            .Select(ws => ws.Id)
            .ToList();
        
        var existingSyncRecords = await _unitOfWork.UrbetrackSynchronizations
            .GetByWeighingScaleIdsAsync(weighingScaleIds, cancellationToken);

        var synchronizationsToCreate = new List<UrbetrackSynchronization>();

        foreach (var weighingScale in notification.WeighingScales)
        {
            if (existingSyncRecords.ContainsKey(weighingScale.Id))
                continue;
            
            var status = weighingScale.CancelDate.HasValue 
                ? SynchronizationStatus.Invalid 
                : SynchronizationStatus.PendingCreation;

            if (status == SynchronizationStatus.Invalid)
            {
                await _unitOfWork.LogEventMessage.GenerateLogEventMessage(
                    $"Cannot generate an unloading ticket for WeighingScale with ID {weighingScale.Id} as it has a cancellation date.",
                    new Dictionary<string, object>
                    {
                        { "WeighingScaleId", weighingScale.Id },
                        { "CancellationDate", weighingScale.CancelDate! }
                    });
            }

            var synchronization = new UrbetrackSynchronization
            {
                WeighingScaleId = weighingScale.Id,
                Status = status,
                RetryCount = 0,
                LastSynchronization = null,
                UrbetrackInternalId = null
            };

            synchronizationsToCreate.Add(synchronization);
        }

        if (synchronizationsToCreate.Any())
        {
            await _unitOfWork
                .UrbetrackSynchronizations
                .AddRangeAsync(synchronizationsToCreate, cancellationToken);

            await _unitOfWork.SaveChangesAsync(cancellationToken);
        }
    }
}