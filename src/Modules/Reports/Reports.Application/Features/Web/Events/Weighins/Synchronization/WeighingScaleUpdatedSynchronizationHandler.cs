using MediatR;
using Reports.Domain;
using Reports.Domain.Constants;
using Reports.Domain.Entities;
using Reports.Domain.Events;

namespace Reports.Application.Features.Web.Events.Weighins.Synchronization;

public class WeighingScaleUpdatedSynchronizationHandler : INotificationHandler<WeighingScaleUpdatedEvent>
{
    private readonly IReportsUnitOfWork _unitOfWork;

    public WeighingScaleUpdatedSynchronizationHandler(IReportsUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    public async Task Handle(WeighingScaleUpdatedEvent notification, CancellationToken cancellationToken)
    {
        var weighingScaleIds = notification.CurrentWeighingScales.Select(ws => ws.Id).ToList();

        var existingSyncRecords = await _unitOfWork.UrbetrackSynchronizations
            .GetByWeighingScaleIdsAsync(weighingScaleIds, cancellationToken);

        var synchronizationsToCreate = new List<UrbetrackSynchronization>();
        var synchronizationsToUpdate = new List<UrbetrackSynchronization>();

        foreach (var weighingId in notification.CurrentWeighingScales.Select(ws => ws.Id))
        {
            var hasPreviousSyncronization = existingSyncRecords.TryGetValue(weighingId, out var existingSync);

            if (hasPreviousSyncronization)
            {
                existingSync!.Status = existingSync?.Status switch
                {
                    SynchronizationStatus.Synchronized => SynchronizationStatus.PendingModification,
                    SynchronizationStatus.Cancelled => SynchronizationStatus.PendingCreation,
                    _ => existingSync?.Status ?? SynchronizationStatus.Invalid
                };

                if (existingSync!.Status is not (SynchronizationStatus.PendingCreation or SynchronizationStatus.PendingModification))
                    continue;
                
                synchronizationsToUpdate.Add(existingSync!);
                continue;
            }
            
            var newSync = new UrbetrackSynchronization
            {
                WeighingScaleId = weighingId,
                Status = SynchronizationStatus.PendingModification
            };

            synchronizationsToCreate.Add(newSync);
        }

        if (synchronizationsToUpdate.Count > 0 || synchronizationsToCreate.Count > 0)
            await _unitOfWork.UrbetrackSynchronizations.UpsertRangeAsync(
                synchronizationsToCreate
                    .Concat(synchronizationsToUpdate),
                cancellationToken);
        
        await _unitOfWork.SaveChangesAsync(cancellationToken);
    }
}