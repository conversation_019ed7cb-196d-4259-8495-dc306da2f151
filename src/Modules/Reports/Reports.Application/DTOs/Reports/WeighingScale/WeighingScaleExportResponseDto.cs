namespace Reports.Application.DTOs.Reports;

/// <summary>
/// DTO para la exportación de datos de báscula de pesaje.
/// </summary>
public record WeighingScaleExportResponseDto
{
    /// <summary>
    /// Número de comprobante - Identificador único del pesaje.
    /// </summary>
    public long NroComprobante { get; init; }
    
    /// <summary>
    /// Placa del vehículo.
    /// </summary>
    public required string Placa { get; init; }
    
    /// <summary>
    /// Fecha de entrada del vehículo.
    /// </summary>
    public DateTime FechaEntrada { get; init; }
    
    /// <summary>
    /// Fecha de salida del vehículo.
    /// </summary>
    public DateTime FechaSalida { get; init; }
    
    /// <summary>
    /// Peso de llegada del vehículo.
    /// </summary>
    public int PesoLlegada { get; init; }
    
    /// <summary>
    /// Peso de salida del vehículo.
    /// </summary>
    public int PesoSalida { get; init; }
    
    /// <summary>
    /// Peso depositado.
    /// </summary>
    public int PesoDepositado { get; init; }
    
    /// <summary>
    /// Número único del área de aprovechamiento.
    /// </summary>
    public long NUAP { get; init; }
    
    /// <summary>
    /// Tipo de origen.
    /// </summary>
    public required string TipoOrigen { get; init; }
    
    /// <summary>
    /// Número de identificación tributaria.
    /// </summary>
    public long NIT { get; init; }
    
    /// <summary>
    /// Nombre de la empresa.
    /// </summary>
    public required string NombreEmpresa { get; init; }
    
    /// <summary>
    /// Tipo de material.
    /// </summary>
    public required string TipoMaterial { get; init; }
    
    /// <summary>
    /// Lugar de depósito.
    /// </summary>
    public long LugarDeposito { get; init; }
    
    /// <summary>
    /// Fecha de carga.
    /// </summary>
    public DateTime FechaCarga { get; init; }
    
    /// <summary>
    /// Fecha de cancelación (si aplica).
    /// </summary>
    public DateTime? FechaCancelacion { get; init; }
    
    /// <summary>
    /// Tipo de carga.
    /// </summary>
    public required string TipoCarga { get; init; }
    
    /// <summary>
    /// Número del municipio (código del municipio).
    /// </summary>
    public required string NumeroMunicipio { get; init; }
    
    /// <summary>
    /// Nombre del municipio.
    /// </summary>
    public required string Municipio { get; init; }
    
    /// <summary>
    /// Departamento del municipio.
    /// </summary>
    public required string Departamento { get; init; }
    
    /// <summary>
    /// Provincia del municipio.
    /// </summary>
    public required string Provincia { get; init; }
}
