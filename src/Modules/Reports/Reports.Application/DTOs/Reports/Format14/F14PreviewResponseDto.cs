namespace Reports.Application.DTOs.Reports;

public class F14PreviewResponseDto : ReportPreviewReponseDto
{
    /// <summary>
    /// Numero de sitio de disposición.
    /// </summary>
    /// <example>124105088</example>
    public long NUAP { get; private set; }
    
    /// <summary>
    /// Tipo de destino.
    /// </summary>
    /// <example>Sitio de disposición final</example>
    public string DestinationType { get; private set; }
    
    /// <summary>
    /// Codigo de destino.
    /// </summary>
    /// <example>720105237</example>
    public string DestinationCode { get; private set; }
    
    /// <summary>
    /// Placa del vehículo.
    /// </summary>
    /// <example>GKV295</example>
    public string LicensePlate { get; private set; }
    
    /// <summary>
    /// Fecha de entrada.
    /// </summary>
    /// <example>2023-07-14</example>
    public DateOnly VehicleArrival { get; private set; }
    
    /// <summary>
    /// Hora de entrada.
    /// </summary>
    /// <example>11:18:00</example>
    public TimeOnly VehicleArrivalTime { get; private set; }
    
    /// <summary>
    /// Código de micro ruta.
    /// </summary>
    /// <example>168387</example>
    public long MicrorouteId { get; private set; }
    
    /// <summary>
    /// Cantidad en toneladas de residuos sólidos urbanos.
    /// </summary>
    /// <example>10</example>
    public double UrbanCleaningTons { get; set; }
    
    /// <summary>
    /// Cantidad en toneladas de barrido.
    /// </summary>
    /// <example>20</example>
    public double SweepingTons { get; set; }
    
    /// <summary>
    /// Cantidad en toneladas de residuos no aprovechables.
    /// </summary>
    /// <example>30</example>
    public double NonRecyclableTons { get; set; }
    
    /// <summary>
    /// Cantidad en toneladas de residuos rechazados.
    /// </summary>
    /// <example>30</example>
    public double RejectedTons { get; set; }
    
    /// <summary>
    /// Cantidad de residuos aprovechables.
    /// </summary>
    /// <example>40</example>
    public double RecyclableTons { get; set; }
    
    /// <summary>
    /// Tipo de unidad de medición.
    /// </summary>
    /// <example>1</example>
    public int MeasuringUnit { get; set; }
    
    /// <summary>
    /// Coste de peajes.
    /// </summary>
    /// <example>50</example>
    public double Toll { get; set; }
    
    /// <summary>
    /// Número de ticket.
    /// </summary>
    /// <example>61248551</example>
    public long? ServiceTicketId { get; set; }
    
    /// <summary>
    /// Nombre de área de aprovechamiento.
    /// </summary>
    /// <example>Copacabana</example>
    public string RecyclingArea { get; set; }
    
    /// <summary>
    /// Codigo de ruta largo.
    /// </summary>
    /// <example>0219701</example>
    public string ExtendedRouteCode { get; set; }
    
    /// <summary>
    /// Cantidad de toneladas totales.
    /// </summary>
    /// <example>50</example>
    public double TotalTons { get; set; }
    
    /// <summary>
    /// Bandera que indica si el registro tiene relación de compensación, excepto para las adiciones.
    /// </summary>
    /// <example>true</example>
    public bool? CompensationRelation { get; set; }
    
    /// <summary>
    /// Codigo de ticket de relación de compensación en caso de tenerlo.
    /// </summary>
    public long? CompensationRelationTicketId { get; set; }
}