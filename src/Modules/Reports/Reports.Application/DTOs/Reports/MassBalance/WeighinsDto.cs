using System.Text.Json.Serialization;

namespace Reports.Application.DTOs.Reports.MassBalance;

public record WeighinsDto
{
    /// <summary>
    /// Toneladas exclusivamente de la empresa Emvarias
    /// de la integracion con balanzas
    /// </summary>
    /// <example>20.5</example>
    [JsonPropertyName("emvarias_tons")]
    public decimal EmvariasTons { get; init; }

    /// <summary>
    /// Total de toneladas de la integracion con balanzas
    /// </summary>
    /// <example>50.0</example>
    [JsonPropertyName("total_tons")]
    public decimal TotalTons { get; init; }
}