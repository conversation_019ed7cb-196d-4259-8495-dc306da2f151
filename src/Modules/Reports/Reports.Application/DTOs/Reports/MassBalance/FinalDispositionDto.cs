using System.Text.Json.Serialization;

namespace Reports.Application.DTOs.Reports.MassBalance;

public record FinalDispositionDto
{
    /// <summary>
    /// Listado por areas de prestación de servicio de la disposición final.
    /// </summary>
    [JsonPropertyName("per_area")]
    public List<FinalDispositionPerAreaDto> PerArea { get; init; } = null!;

    /// <summary>
    /// Totales de la disposición final.
    /// </summary>
    [JsonPropertyName("totals")]
    public FinalDispositionTotalDto Totals { get; init; } = null!;
}