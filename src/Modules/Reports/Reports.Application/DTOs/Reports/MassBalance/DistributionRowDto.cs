using System.Text.Json.Serialization;

namespace Reports.Application.DTOs.Reports.MassBalance;

/// <summary>
/// DTO representing a single row in the Distribution table (page 2 of the report)
/// </summary>
public record DistributionRowDto
{
    /// <summary>
    /// Name of the recycling area
    /// </summary>
    /// <example>Medellín</example>
    [JsonPropertyName("recycling_area")]
    public string RecyclingArea { get; init; } = null!;

    /// <summary>
    /// Total tons reported for the period
    /// </summary>
    /// <example>1250.450</example>
    [JsonPropertyName("reported_tons")]
    public decimal ReportedTons { get; init; }

    /// <summary>
    /// Number of trips reported for the period
    /// </summary>
    /// <example>125</example>
    [JsonPropertyName("trips")]
    public int Trips { get; init; }

    /// <summary>
    /// Calculated distributed tons used in SUI reports
    /// </summary>
    /// <example>1245.320</example>
    [JsonPropertyName("calculated_distributed_tons")]
    public decimal CalculatedDistributedTons { get; init; }

    /// <summary>
    /// Calculated total tons including deviation
    /// </summary>
    /// <example>1250.450</example>
    [JsonPropertyName("calculated_total_tons")]
    public decimal CalculatedTotalTons { get; init; }

    /// <summary>
    /// Calculated deviation tons from distribution
    /// </summary>
    /// <example>5.130</example>
    [JsonPropertyName("calculated_deviation_tons")]
    public decimal CalculatedDeviationTons { get; init; }

    /// <summary>
    /// Shared route tons used for toll percentage calculation
    /// </summary>
    /// <example>203.630</example>
    [JsonPropertyName("toll_shared_route_tons")]
    public decimal TollSharedRouteTons { get; init; }

    /// <summary>
    /// Calculated distribution toll percentage
    /// </summary>
    /// <example>0.125</example>
    [JsonPropertyName("calculated_distribution_toll_percentage")]
    public decimal CalculatedDistributionTollPercentage { get; init; }

    /// <summary>
    /// Compensation amount calculated based on percentage and tons
    /// </summary>
    /// <example>25.450</example>
    [JsonPropertyName("compensation")]
    public decimal Compensation { get; init; }
}
