using System.Text.Json.Serialization;

namespace Reports.Application.DTOs.Reports.MassBalance;

public record FinalDispositionTotalDto
{
    /// <summary>
    /// Sumatoria total de las toneladas de descuento
    /// </summary>
    /// <example>20.5</example>
    [JsonPropertyName("all_discount_tons")]
    public decimal AllDiscountTons { get; init; }

    /// <summary>
    /// Sumatoria total de las toneladas exclusivamente de la empresa Emvarias
    /// del reporte de disposición final
    /// </summary>
    /// <example>20.5</example>
    [JsonPropertyName("all_emvarias_tons")]
    public decimal AllEmvariasTons { get; init; }

    /// <summary>
    /// Sumatoria total de toneladas del reporte de disposición final
    /// </summary>
    /// <example>50.0</example>
    [JsonPropertyName("total_tons")]
    public decimal TotalTons { get; init; }
}