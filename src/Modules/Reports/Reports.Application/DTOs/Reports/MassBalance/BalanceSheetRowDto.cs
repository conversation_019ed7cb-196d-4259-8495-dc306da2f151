using System.Text.Json.Serialization;

namespace Reports.Application.DTOs.Reports.MassBalance;

/// <summary>
/// DTO representing a single row in the Mass Balance sheet combining F14 and F34 data
/// </summary>
public record BalanceSheetRowDto
{
    /// <summary>
    /// Area code for the service area
    /// </summary>
    /// <example>440405001</example>
    [JsonPropertyName("area_code")]
    public string AreaCode { get; init; } = null!;

    /// <summary>
    /// Name of the service area
    /// </summary>
    /// <example>Medellín</example>
    [JsonPropertyName("area_name")]
    public string AreaName { get; init; } = null!;

    // F14 Columns (Recollection & Transport)
    
    /// <summary>
    /// Urban cleaning tons from F14 report
    /// </summary>
    /// <example>125.450</example>
    [JsonPropertyName("urban_cleaning_tons")]
    public decimal UrbanCleaningTons { get; init; }

    /// <summary>
    /// Sweeping tons from F14 report
    /// </summary>
    /// <example>89.320</example>
    [JsonPropertyName("sweeping_tons")]
    public decimal SweepingTons { get; init; }

    /// <summary>
    /// Non-recyclable tons from F14 report
    /// </summary>
    /// <example>234.780</example>
    [JsonPropertyName("non_recyclable_tons")]
    public decimal NonRecyclableTons { get; init; }

    /// <summary>
    /// Rejection tons from F14 report
    /// </summary>
    /// <example>12.150</example>
    [JsonPropertyName("rejection_tons")]
    public decimal RejectionTons { get; init; }

    /// <summary>
    /// Recyclable tons from F14 report
    /// </summary>
    /// <example>45.670</example>
    [JsonPropertyName("recyclable_tons")]
    public decimal RecyclableTons { get; init; }

    // F34 Columns (Final Disposition)
    
    /// <summary>
    /// Total tons by NUAP from F34 report
    /// </summary>
    /// <example>350.250</example>
    [JsonPropertyName("total_by_nuap")]
    public decimal TotalByNUAP { get; init; }

    /// <summary>
    /// Discount tons from F34 report
    /// </summary>
    /// <example>15.750</example>
    [JsonPropertyName("discounts")]
    public decimal Discounts { get; init; }

    // Note: Calculated fields (Total F14, Total F34, Difference) will be generated as Excel formulas
    // These are not included as properties since they will be calculated in Excel
}
