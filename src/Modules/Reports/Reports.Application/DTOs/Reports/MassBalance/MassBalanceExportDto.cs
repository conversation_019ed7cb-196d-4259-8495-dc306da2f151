using System.Text.Json.Serialization;

namespace Reports.Application.DTOs.Reports.MassBalance;

/// <summary>
/// DTO for Mass Balance Excel export containing all data needed for Excel generation
/// </summary>
public record MassBalanceExportDto
{
    /// <summary>
    /// Title for the Excel report header
    /// </summary>
    /// <example>Balance de Masas</example>
    [JsonPropertyName("title")]
    public string Title { get; init; } = "Balance de Masas";

    /// <summary>
    /// Date when the report was generated
    /// </summary>
    /// <example>2025-03-15T10:30:00</example>
    [JsonPropertyName("generation_date")]
    public DateTime GenerationDate { get; init; }

    /// <summary>
    /// Period covered by the report in YYYY/MM format
    /// </summary>
    /// <example>2025/03</example>
    [JsonPropertyName("period")]
    public string Period { get; init; } = null!;

    /// <summary>
    /// Combined balance sheet data rows for the main table
    /// </summary>
    [JsonPropertyName("balance_sheet_rows")]
    public List<BalanceSheetRowDto> BalanceSheetRows { get; init; } = null!;

    /// <summary>
    /// Final disposition summary data for the small summary table
    /// </summary>
    [JsonPropertyName("final_disposition_summary")]
    public FinalDispositionSummaryDto FinalDispositionSummary { get; init; } = null!;

    /// <summary>
    /// Distribution data for the distribution table on page 2
    /// </summary>
    [JsonPropertyName("distribution_rows")]
    public List<DistributionRowDto> DistributionRows { get; init; } = null!;
}
