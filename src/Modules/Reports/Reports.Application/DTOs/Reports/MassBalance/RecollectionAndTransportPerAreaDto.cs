using System.Text.Json.Serialization;

namespace Reports.Application.DTOs.Reports.MassBalance;

public record RecollectionAndTransportPerAreaDto
{
    /// <summary>
    /// Nombre del área de prestación de servicio.
    /// </summary>
    /// <example>Medellín</example>
    [JsonPropertyName("recycling_area")]
    public string RecyclingArea { get; init; } = null!;

    /// <summary>
    /// Toneladas de limpieza urbana del reporte
    /// de recolección y transporte
    /// </summary>
    /// <example>20.5</example>
    [JsonPropertyName("urban_cleaning_tons")]
    public decimal UrbanCleaningTons { get; init; }

    /// <summary>
    /// Toneladas de barrido del reporte
    /// de recolección y transporte
    /// </summary>
    /// <example>20.5</example>
    [JsonPropertyName("sweeping_tons")]
    public decimal SweepingTons { get; init; }

    /// <summary>
    /// Toneladas no aprovechables del reporte
    /// de recolección y transporte
    /// </summary>
    /// <example>20.5</example>
    [JsonPropertyName("non_recyclable_tons")]
    public decimal NonRecyclableTons { get; init; }

    /// <summary>
    /// Toneladas de rechazo del reporte
    /// de recolección y transporte
    /// </summary>
    /// <example>20.5</example>
    [JsonPropertyName("rejection_tons")]
    public decimal RejectionTons { get; init; }

    /// <summary>
    /// Toneladas aprovechables del reporte
    /// de recolección y transporte
    /// </summary>
    /// <example>20.5</example>
    [JsonPropertyName("recyclable_tons")]
    public decimal RecyclableTons { get; init; }

    /// <summary>
    /// Total de toneladas del reporte
    /// de recolección y transporte resultado de la suma
    /// de las toneladas de barrido y toneladas de no aprovechables
    /// </summary>
    /// <example>50.0</example>
    [JsonPropertyName("total_tons")]
    public decimal TotalTons { get; init; }
}