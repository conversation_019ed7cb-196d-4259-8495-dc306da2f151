using System.Text.Json.Serialization;

namespace Reports.Application.DTOs.Reports.MassBalance;

/// <summary>
/// DTO for the Final Disposition Summary table that appears at the bottom of page 1
/// </summary>
public record FinalDispositionSummaryDto
{
    /// <summary>
    /// Total tons from weighing scales integration (Emvarias only)
    /// </summary>
    /// <example>1250.450</example>
    [JsonPropertyName("weighing_emvarias_tons")]
    public decimal WeighingEmvariasTons { get; init; }

    /// <summary>
    /// Total tons from weighing scales integration (all companies)
    /// </summary>
    /// <example>1850.750</example>
    [JsonPropertyName("weighing_total_tons")]
    public decimal WeighingTotalTons { get; init; }

    /// <summary>
    /// Total Emvarias tons from final disposition report
    /// </summary>
    /// <example>1245.320</example>
    [JsonPropertyName("final_disposition_emvarias_tons")]
    public decimal FinalDispositionEmvariasTons { get; init; }

    /// <summary>
    /// Total tons from final disposition report
    /// </summary>
    /// <example>1840.890</example>
    [JsonPropertyName("final_disposition_total_tons")]
    public decimal FinalDispositionTotalTons { get; init; }

    /// <summary>
    /// Total discount tons from final disposition report
    /// </summary>
    /// <example>45.250</example>
    [JsonPropertyName("final_disposition_discount_tons")]
    public decimal FinalDispositionDiscountTons { get; init; }
}
