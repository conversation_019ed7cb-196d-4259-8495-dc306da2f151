using System.Text.Json.Serialization;

namespace Reports.Application.DTOs.Reports.MassBalance;

public record DistributionDto
{
    /// <summary>
    /// Nombre del área de prestación de servicio.
    /// </summary>
    /// <example>Medellín</example>
    [JsonPropertyName("recycling_area")]
    public string RecyclingArea { get; init; } = null!;

    /// <summary>
    /// Toneladas totales reportadas del periodo.
    /// </summary>
    /// <example>20.5</example>
    [JsonPropertyName("reported_tons")]
    public decimal ReportedTons { get; init; }

    /// <summary>
    /// Cantidad de viajes reportados del periodo.
    /// </summary>
    /// <example>1</example>
    [JsonPropertyName("trips")]
    public int Trips { get; init; }

    /// <summary>
    /// Toneladas distribuidas utilizadas en los reportes SUI.
    /// </summary>
    /// <example>20.5</example>
    [JsonPropertyName("calculated_distributed_tons")]
    public decimal CalculatedDistributedTons { get; init; }

    /// <summary>
    /// Toneladas totales calculadas, incluyendo la desviación.
    /// </summary>
    /// <example>20.5</example>
    [JsonPropertyName("calculated_total_tons")]
    public decimal CalculatedTotalTons { get; init; }

    /// <summary>
    /// Toneladas de desviación resultante de la distribución.
    /// </summary>
    /// <example>20.5</example>
    [JsonPropertyName("calculated_deviation_tons")]
    public decimal CalculatedDeviationTons { get; init; }
    
    /// <summary>
    /// Toneladas de rutas compartidas usadas para el calculo del porcentaje de peaje.
    /// </summary>
    /// <example>203.63</example>
    [JsonPropertyName("toll_shared_route_tons")]
    public decimal TollSharedRouteTons { get; init; }
    
    /// <summary>
    /// Porcentaje correspondiente al peaje vehicular de la distribución.
    /// </summary>
    /// <example>0.5</example>
    [JsonPropertyName("calculated_distribution_toll_percentage")]
    public decimal CalculatedDistributionTollPercentage { get; init; }
}