namespace Reports.Application.DTOs.Reports;

public class F34PreviewResponseDto : ReportPreviewReponseDto
{
    /// <summary>
    /// Número de sitio de disposición final correspondiente a Emvarias.
    /// </summary>
    /// <example>*********</example>
    public long NUSD { get; set; }
    
    /// <summary>
    /// Tipo de origen.
    /// </summary>
    /// <example>NUAP</example>
    public string OriginType { get; set; }
    
    /// <summary>
    /// Número único de sitio de origen.
    /// </summary>
    /// <example>*********</example>
    public string PlaceOriginNumber { get; set; }
    
    /// <summary>
    /// Nombre de la compañía.
    /// </summary>
    /// <example>Shinra Corporation</example>
    public string CompanyName { get; set; }
    
    /// <summary>
    /// Número de identificación tributaria.
    /// </summary>
    /// <example>*********</example>
    public string NIT { get; set; }
    
    /// <summary>
    /// Código del municipio de donde provienen los residuos.
    /// </summary>
    /// <example>05001</example>
    public string DaneCode { get; set; }
    
    /// <summary>
    /// Placa del vehículo.
    /// </summary>
    /// <example>GKV295</example>
    public string LicensePlate { get; set; }
    
    /// <summary>
    /// Fecha de ingreso.
    /// </summary>
    /// <example>2023-07-14</example>
    public DateOnly ArrivalDate { get; set; }
    
    /// <summary>
    /// Hora de ingreso.
    /// </summary>
    /// <example>11:18:00</example>
    public TimeOnly ArrivalTime { get; set; }
    
    /// <summary>
    /// Fecha de salida.
    /// </summary>
    /// <example>2023-07-14</example>
    public DateOnly DepartureDate { get; set; }
    
    /// <summary>
    /// Hora de salida.
    /// </summary>
    /// <example>12:02:36</example>
    public TimeOnly DepartureTime { get; set; }
    
    /// <summary>
    /// Toneladas de residuos.
    /// </summary>
    /// <example>20.5</example>
    public double Tons { get; set; }
    
    /// <summary>
    /// Número de ticket.
    /// </summary>
    /// <example>61248551</example>
    public long ServiceTicketId { get; set; }
    
    /// <summary>
    /// Peso registrado en ticket de servicio.
    /// </summary>
    /// <example>4100</example>
    public decimal ServiceTicketWeight { get; set; }
    
    /// <summary>
    /// Tonelage registrado en ticket de servicio.
    /// </summary>
    /// <example>4.10</example>
    public decimal ServiceTicketTonnage { get; set; }
    
    /// <summary>
    /// Tonelaje de rechazos calculado.
    /// </summary>
    /// <example>20</example>
    public decimal RejectedTonnage { get; set; }
    
    /// <summary>
    /// Indica si el registro existe en el reporte 14.
    /// </summary>
    /// <example>true</example>
    public bool ExistsInReport14 { get; set; }
    
    /// <summary>
    /// Número de identificación tributaria de la empresa.
    /// </summary>
    /// <example>*********</example>
    public long CompanyNIT { get; set; }
    
    /// <summary>
    /// Número de sitio aprovechamiento en caso de corresponder.
    /// </summary>
    /// <example>*********</example>
    public long? NUAP { get; set; }
}