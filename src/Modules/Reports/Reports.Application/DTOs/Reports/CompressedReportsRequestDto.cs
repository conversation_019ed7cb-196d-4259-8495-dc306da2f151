namespace Reports.Application.DTOs.Reports;

public record CompressedReportsRequestDto
{
    /// <summary>
    /// <PERSON>cha de entrada a filtrar desde.
    /// </summary>
    /// <remarks>Formato: YYYY-MM-DD</remarks>
    /// <example>2023-07-14</example>
    public DateTime FromDate { get; init; }

    /// <summary>
    /// Fecha de entrada a filtrar hasta.
    /// </summary>
    /// <remarks>Formato: YYYY-MM-DD</remarks>
    /// <example>2023-07-14</example>
    public DateTime ToDate { get; init; }

    /// <summary>
    /// Campos de ordenamiento.
    /// </summary>
    /// <remarks> ArrivalDateTime </remarks>
    /// <example>ArrivalDateTime</example>
    public string? SortFields { get; init; }

    /// <summary>
    /// Tipo de ordenamiento.
    /// </summary>
    /// <remarks> Ascending, Descending</remarks>
    /// <example>Ascending</example>
    public string? SortDirection { get; init; }
}