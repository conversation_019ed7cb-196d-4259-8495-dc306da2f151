using System.Text.Json.Serialization;
using Reports.Application.DTOs.HistoricalAudit.PreviousData;

namespace Reports.Application.DTOs.HistoricalAudit;

/// <summary>
/// DTO para representar un registro de auditoría histórica
/// </summary>
public class HistoricalAuditItemResponseDto
{
    /// <summary>
    /// ID único del registro histórico
    /// </summary>
    /// <example>1</example>
    [JsonPropertyName("id")]
    public int Id { get; set; }

    /// <summary>
    /// ID de la entidad
    /// </summary>
    /// <example>123456</example>
    [JsonPropertyName("entityId")]
    public string EntityId { get; set; } = string.Empty;

    /// <summary>
    /// Nombre de la tabla
    /// </summary>
    /// <example>Reporting-Emvarias_03-<PERSON><PERSON>aj<PERSON>_de_Balanza</example>
    [JsonPropertyName("tableName")]
    public string TableName { get; set; } = string.Empty;

    /// <summary>
    /// Tipo de acción: Creado, Modificado, Anulado
    /// </summary>
    /// <example>Creado</example>
    [JsonPropertyName("actionType")]
    public string ActionType { get; set; } = string.Empty;

    /// <summary>
    /// Usuario que realizó la acción
    /// </summary>
    /// <example><EMAIL></example>
    [JsonPropertyName("user")]
    public string User { get; set; } = string.Empty;

    /// <summary>
    /// Fecha y hora de la acción
    /// </summary>
    /// <example>2024-07-15T14:30:00</example>
    [JsonPropertyName("actionDate")]
    public DateTime ActionDate { get; set; }

    /// <summary>
    /// Datos previos en formato JSON (solo para modificaciones)
    /// </summary>
    /// <example>{"LicensePlate": "ABC123", "NIT": 123456789}</example>
    [JsonIgnore]
    public string? PreviousData { get; set; }

    /// <summary>
    /// Datos previos tipados según el tipo de tabla (solo para modificaciones)
    /// </summary>
    [JsonPropertyName("previousData")]
    public object? PreviousDataTyped { get; set; }
}
