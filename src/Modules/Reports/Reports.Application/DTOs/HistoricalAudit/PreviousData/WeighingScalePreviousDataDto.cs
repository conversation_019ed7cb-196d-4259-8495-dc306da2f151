using System.Text.Json.Serialization;
using Reports.Application.DTOs.Weighings;
using Reports.Domain.Constants;

namespace Reports.Application.DTOs.HistoricalAudit.PreviousData;

/// <summary>
/// DTO para los datos previos de pesaje de balanza
/// </summary>
public class WeighingScalePreviousDataDto : GetWeighingItemResponseDto, IBasePreviousDataDto
{
    /// <summary>
    /// Tipo de tabla
    /// </summary>
    [JsonPropertyName("tableType")]
    public string TableType => ReportsTableNames.WeighingScale;
}
