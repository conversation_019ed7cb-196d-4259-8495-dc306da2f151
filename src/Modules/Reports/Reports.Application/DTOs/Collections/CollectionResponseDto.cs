using System.Text.Json.Serialization;

namespace Reports.Application.DTOs.Collections;

public class CollectionResponseDto
{
    /// <summary>
    /// Identificador de la recolección vehicular registrada.
    /// </summary>
    /// <remarks>Formato: NUAP-Número de Matrícula-Fecha de Recolección</remarks>
    /// <example>124105088-GKV295-20230714130702</example>
    [JsonPropertyName("IdRecoleccionVehicular")]
    public string VehicleRetrievalId { get; set; }
    
    /// <summary>
    /// Identificador de la recolección por micro ruta registrada.
    /// </summary>
    /// <remarks>Formato: NUAP-Número de Matrícula-Fecha de Recolección</remarks>
    /// <example>124105088-GKV295-20230714130702</example>
    [JsonPropertyName("IdRecoleccionMicroRuta")]
    public string MicroRouteCollectionId { get; set; }
}