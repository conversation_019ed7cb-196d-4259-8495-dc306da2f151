using System.Text.Json.Serialization;

namespace Reports.Application.DTOs.Microroutes;

public record RegulatoryRouteCodesDto
{
    /// <summary>
    /// Identificador de la ruta almacenada en higiene.
    /// </summary>
    /// <example>0518219</example>
    [JsonPropertyName("hygieneRouteCode")]
    public string HygieneRouteCode { get; set; }
    
    /// <summary>
    /// Identificador de la ruta provista y usada por la superintendencia.
    /// </summary>
    /// <example>117412</example>
    [JsonPropertyName("regulatoryRouteCode")]
    public string RegulatoryRouteCode { get; set; }
}