using System.Text.Json.Serialization;

namespace Reports.Application.DTOs.Weighings;

public class CreateWeighingItemResponseDto
{
    /// <summary>
    /// Codigo de la operación realizada.
    /// <para>1001 - Alta de pesaje.</para>
    /// <para>1002 - Modificación de pesaje.</para>
    /// <para>1003 - Anulación de pesaje.</para>
    /// </summary>
    /// <example>1001</example>
    /// <example>1002</example>
    /// <example>1003</example>
    [JsonPropertyName("Tipo_Operacion")]
    public int Status { get; set; } 

    /// <summary>
    /// Nro. del comprobante afectado.
    /// </summary>
    /// <example>91220094</example>
    [JsonPropertyName("NroComprobante")]
    public long Id { get; set; }
}