namespace Reports.Application.DTOs.Weighings;

public class GetWeighingItemResponseDto
{
    public string Id { get; set; }
    public string LicensePlate { get; set; }
    public long NIT { get; set; }
    public double ArrivingWeight { get; set; }
    public double LeavingWeight { get; set; }
    public double DepositWeight { get; set; }
    public string CompanyName { get; set; }
    public DateTime EntryDate { get; set; }
    public DateTime EgressDate { get; set; }
    public string MaterialType { get; set; }
    public long DepositPlace { get; set; }
    public string OriginType { get; set; }
    public long NUAP { get; set; }
    public DateTime LoadingDate { get; set; }
    public DateTime? CancelDate { get; set; }
    public string LoadingType { get; set; }
    public GetWeighingItemTownResponseDto Town { get; set; }
}