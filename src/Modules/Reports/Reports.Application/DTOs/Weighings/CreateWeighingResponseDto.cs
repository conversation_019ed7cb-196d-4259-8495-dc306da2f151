using System.Text.Json.Serialization;

namespace Reports.Application.DTOs.Weighings;

public class CreateWeighingResponseDto
{
    /// <summary>
    /// Lista de pesajes insertados, modificados o anulados.
    /// </summary>
    /// <example></example>
    [JsonPropertyName("Pesajes")]
    public List<CreateWeighingItemResponseDto> Items { get; set; }
    
    /// <summary>
    /// Total de pesajes manipulados.
    /// </summary>
    /// <example>2</example>
    [JsonPropertyName("Total")]
    public int Total { get; set; }
}