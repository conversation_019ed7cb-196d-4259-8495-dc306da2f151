using Reports.Application.Services.Http.Legacy.Request;
using Reports.Application.Services.Http.Legacy.Response;

namespace Reports.Application.Services.Http.Legacy;

public interface ILegacyApiService
{
    Task SendUnloadingTicketAsync(PostUnloadingTicketRequest request);
    
    Task DeleteUnloadingTicketAsync(DeleteUnloadingTicketRequest request);

    Task<IList<GetUnloadingTicketsResponseItem>> GetUnloadingTicketsAsync(GetUnloadingTicketsRequest request);
}