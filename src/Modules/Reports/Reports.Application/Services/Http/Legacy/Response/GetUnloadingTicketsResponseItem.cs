using Newtonsoft.Json;
using Orion.SharedKernel.Domain.Entities.Http;

namespace Reports.Application.Services.Http.Legacy.Response;

public class GetUnloadingTicketsResponseItem : SerializableModel
{
    [JsonProperty("id")]
    public int InternalUrbetrackId { get; set; }
    
    [JsonProperty("companyId")]
    public int CompanyId { get; set; }
    
    [JsonProperty("receptionDate")]
    public string ReceptionDate { get; set; } = null!;
    
    [JsonProperty("weighingDate")]
    public string WeighingDate { get; set; } = null!;
    
    [JsonProperty("licensePlate")]
    public string LicensePlate { get; set; } = null!;
    
    [JsonProperty("ticketNumber")]
    public string TicketNumber { get; set; } = null!;
    
    [JsonProperty("grossWeight")]
    public decimal GrossWeight { get; set; }
    
    [JsonProperty("tareWeight")]
    public decimal TareWeight { get; set; }
    
    [JsonProperty("comment")]
    public string? Comment { get; set; }
    
    [JsonProperty("userId")]
    public int UserId { get; set; }
    
    [JsonProperty("scaleId")]
    public int ScaleId { get; set; }
    
    [JsonProperty("productId")]
    public int ProductId { get; set; }
}