using Orion.SharedKernel.Domain.Entities.Http;

namespace Reports.Application.Services.Http.Legacy.Request;

public class DeleteUnloadingTicketRequest : CommonRequest<DeleteUnloadingTicketRequest>
{
    [Route(position: 0)]
    public int CompanyId { get; set; }
    
    [Route(position: 1)]
    public int Id { get; set; }
    
    public DeleteUnloadingTicketRequest Create(int companyId, int urbetrackInternalId)
    {
        CompanyId = companyId;
        Id = urbetrackInternalId;
        return this;
    }
}