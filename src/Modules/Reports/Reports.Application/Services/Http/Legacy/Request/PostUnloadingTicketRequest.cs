using Newtonsoft.Json;

namespace Reports.Application.Services.Http.Legacy.Request;

public class PostUnloadingTicketRequest : CommonRequest<PostUnloadingTicketRequest>
{
    private const string DgLimDistrictParameterCode = "Emv";
    
    [JsonProperty("number")]
    public string Number { get; set; }
    
    [JsonProperty("license_plate")]
    public string LicensePlate { get; set; }
    
    [JsonProperty("internal_number")]
    public string? InternalNumber { get; set; } = null;
    
    [JsonProperty("company")]
    public string Company { get; set; } = DgLimDistrictParameterCode;
    
    [JsonProperty("datetime")]
    public string Datetime { get; set; }
    
    [JsonProperty("gross_weight")]
    public double GrossWeight { get; set; }
    
    [JsonProperty("tare_weight")]
    public double TareWeight { get; set; }
    
    [JsonProperty("destination")]
    public string Destination { get; set; }
    
    [JsonProperty("material_type")]
    public string MaterialType { get; set; }
    
    [JsonProperty("comment")]
    public string? Comment { get; set; } = null;
}