using Orion.SharedKernel.Domain.Entities.Http;

namespace Reports.Application.Services.Http.Legacy.Request;

public class GetUnloadingTicketsRequest : CommonRequest<GetUnloadingTicketsRequest>
{
    [Query("ticketNumber")]
    public string TicketNumber { get; set; } = null!;
    
    [Route(position: 0)]
    public int CompanyId { get; set; }
    
    public GetUnloadingTicketsRequest Create(string weighingScaleId, int companyId)
    {
        TicketNumber = weighingScaleId;
        CompanyId = companyId;
        return this;
    }
}