using Reports.Application.Services.Http.Legacy.Request;
using Reports.Application.Services.Http.Legacy.Response;

namespace Reports.Application.Services.Authentication;

public interface IAuthenticatedLegacyApiService
{
    Task SendUnloadingTicketAsync(PostUnloadingTicketRequest request, CancellationToken cancellationToken = default);

    Task<IList<GetUnloadingTicketsResponseItem>> GetUnloadingTicketsAsync(
        GetUnloadingTicketsRequest request, 
        CancellationToken cancellationToken = default);

    Task DeleteUnloadingTicketAsync(DeleteUnloadingTicketRequest request, CancellationToken cancellationToken = default);
}
