using Common.Application.Services;
using Microsoft.Extensions.Logging;
using Orion.SharedKernel.Application.Common.Services;
using Reports.Application.Services.Http.Legacy;
using Reports.Application.Services.Http.Legacy.Request;
using Reports.Application.Services.Http.Legacy.Response;
using Reports.Domain.Exceptions;

namespace Reports.Application.Services.Authentication;

public class AuthenticatedLegacyApiService : IAuthenticatedLegacyApiService
{
    private readonly ILegacyApiService _legacyApiService;
    private readonly IJobAuthenticationService _authenticationService;
    private readonly ILogger<AuthenticatedLegacyApiService> _logger;
    private string? _cachedToken;

    public AuthenticatedLegacyApiService(
        ILegacyApiService legacyApiService,
        IJobAuthenticationService authenticationService,
        ILogger<AuthenticatedLegacyApiService> logger)
    {
        _legacyApiService = legacyApiService;
        _authenticationService = authenticationService;
        _logger = logger;
    }

    public async Task SendUnloadingTicketAsync(PostUnloadingTicketRequest request, CancellationToken cancellationToken = default)
    {
        var token = await EnsureAuthenticatedAsync();
        request.SetAuthorization(token);
        await _legacyApiService.SendUnloadingTicketAsync(request);
    }

    public async Task<IList<GetUnloadingTicketsResponseItem>> GetUnloadingTicketsAsync(
        GetUnloadingTicketsRequest request, 
        CancellationToken cancellationToken = default)
    {
        var token = await EnsureAuthenticatedAsync();
        request.SetAuthorization(token);
        return await _legacyApiService.GetUnloadingTicketsAsync(request);
    }

    public async Task DeleteUnloadingTicketAsync(DeleteUnloadingTicketRequest request, CancellationToken cancellationToken = default)
    {
        var token = await EnsureAuthenticatedAsync();
        request.SetAuthorization(token);
        await _legacyApiService.DeleteUnloadingTicketAsync(request);
    }

    private async Task<string> EnsureAuthenticatedAsync()
    {
        if (string.IsNullOrEmpty(_cachedToken))
        {
            try
            {
                _logger.LogDebug("Authenticating for legacy API access");
                var authResponse = await _authenticationService.AuthenticateAsync();
                _cachedToken = authResponse.LegacyToken;
                _logger.LogInformation("Successfully authenticated for legacy API access");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to authenticate for legacy API access");
                throw new AuthenticationFailedException("Failed to obtain legacy token", ex);
            }
        }

        if (string.IsNullOrEmpty(_cachedToken))
        {
            throw new AuthenticationFailedException("No legacy token obtained after authentication");
        }

        return _cachedToken;
    }
}
