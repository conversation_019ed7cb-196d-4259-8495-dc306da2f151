using System.Text.Json;
using System.Text.Json.Serialization;
using Orion.SharedKernel.Application.Exceptions;
using Reports.Application.DTOs.HistoricalAudit.PreviousData;
using Reports.Application.Features.Web;
using Reports.Domain;
using Reports.Domain.Constants;
using Reports.Domain.Entities;

namespace Reports.Application.Services.HistoricalAudit;

/// <summary>
/// Servicio para parsear datos previos de auditoría histórica
/// </summary>
public class PreviousDataParser
{
    private readonly IReportsUnitOfWork _unitOfWork;
    private const string LegacyWeighingScaleTableName = "Pesaje_de_Balanza";

    /// <summary>
    /// Constructor para inicializar el servicio de parseo de datos previos
    /// </summary>
    /// <param name="unitOfWork">Unit of work para acceso a servicios</param>
    public PreviousDataParser(IReportsUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    /// <summary>
    /// Parsea los datos previos según el tipo de tabla preservando el tipo específico
    /// </summary>
    /// <typeparam name="T">Tipo específico del DTO que implementa IBasePreviousDataDto</typeparam>
    /// <param name="tableName">Nombre de la tabla</param>
    /// <param name="previousDataJson">JSON de datos previos</param>
    /// <returns>DTO tipado específico</returns>
    /// <exception cref="OrionException">Se lanza cuando hay un error al parsear los datos JSON</exception>
    public T? ParsePreviousData<T>(string tableName, string? previousDataJson) where T : class, IBasePreviousDataDto
    {
        if (string.IsNullOrWhiteSpace(previousDataJson))
            return null;

        try
        {
            var result = tableName switch
            {
                LegacyWeighingScaleTableName => ParseWeighingScaleData(previousDataJson),
                _ when tableName == ReportsTableNames.WeighingScale => ParseWeighingScaleData(previousDataJson),
                _ => null
            };

            return result as T;
        }
        catch (Exception ex)
        {
            throw new OrionException(_unitOfWork.ErrorService.GenerateError(new PreviousDataParsingFailed(tableName, previousDataJson, ex.Message)));
        }
    }

    /// <summary>
    /// Parsea datos previos de pesaje de balanza
    /// </summary>
    /// <param name="jsonData">Datos JSON a parsear</param>
    /// <returns>DTO de datos previos de pesaje de balanza</returns>
    /// <exception cref="JsonException">Se lanza cuando hay un error al deserializar el JSON</exception>
    private WeighingScalePreviousDataDto? ParseWeighingScaleData(string jsonData)
    {
        var options = new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            NumberHandling = JsonNumberHandling.AllowReadingFromString,
            DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
        };

        var weighingScale = JsonSerializer.Deserialize<WeighingScale>(jsonData, options);

        var weighingData = new WeighingScalePreviousDataDto()
        {
            Id = weighingScale.Id.ToString(),
            OriginType = weighingScale.OriginType.ToString(),
            ArrivingWeight = weighingScale.ArrivingWeight,
            CancelDate = weighingScale.CancelDate,
            DepositPlace = weighingScale.DepositPlace,
            EgressDate = weighingScale.EgressDate,
            EntryDate = weighingScale.EntryDate,
            LeavingWeight = weighingScale.LeavingWeight,
            LicensePlate = weighingScale.LicensePlate,
            LoadingDate = weighingScale.LoadingDate,
            LoadingType = weighingScale.LoadingType.ToString(),
            MaterialType = weighingScale.MaterialType.ToString(),
            NIT = weighingScale.NIT,
            NUAP = weighingScale.NUAP,
            Town = new()
            {
                Code = weighingScale.TownCode,
            }
        };
        return weighingData;
    }
}
