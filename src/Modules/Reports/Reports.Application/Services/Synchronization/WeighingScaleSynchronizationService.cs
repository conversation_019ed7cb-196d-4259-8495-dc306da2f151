using Reports.Domain;
using Reports.Domain.Constants;
using Reports.Domain.Entities;
using Reports.Domain.Services;
using Reports.Domain.ValueObjects;

namespace Reports.Application.Services.Synchronization;

public class WeighingScaleSynchronizationService
{
    private readonly IExternalTicketingSystem _externalTicketingSystem;
    private readonly IReportsUnitOfWork _unitOfWork;

    public WeighingScaleSynchronizationService(
        IExternalTicketingSystem externalTicketingSystem,
        IReportsUnitOfWork unitOfWork)
    {
        _externalTicketingSystem = externalTicketingSystem ?? throw new ArgumentNullException(nameof(externalTicketingSystem));
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public async Task<SyncOperationOutcome> CreateTicketAsync(WeighingScale weighingScale, CancellationToken cancellationToken = default)
    {
        if (weighingScale is null)
            throw new ArgumentNullException(nameof(weighingScale));

        var validationResult = weighingScale.ValidateForUpsertSynchronization();
        if (!validationResult.IsValid)
        {
            return SyncOperationOutcome.CreationFailure(validationResult.ErrorMessage ?? "Validation failed");
        }

        try
        {
            var existingInternalId = await _externalTicketingSystem.GetExistingTicketInternalIdAsync(weighingScale.Id, cancellationToken);
            if (!string.IsNullOrEmpty(existingInternalId))
            {
                await _unitOfWork.LogEventMessage.GenerateLogEventMessage(
                    $"El ticket ya existe para WeighingScale {weighingScale.Id} con ID interno {existingInternalId}",
                    new Dictionary<string, object>
                    {
                        { "WeighingScaleId", weighingScale.Id },
                        { "ExistingInternalId", existingInternalId },
                        { "Operation", "CreateTicket" },
                        { "Result", "AlreadyExists" }
                    });
                return SyncOperationOutcome.AlreadyExists(existingInternalId);
            }

            await _externalTicketingSystem.CreateTicketAsync(weighingScale, cancellationToken);
            await _unitOfWork.LogEventMessage.GenerateLogEventMessage(
                $"Solicitud de creación de ticket enviada para WeighingScale {weighingScale.Id}",
                new Dictionary<string, object>
                {
                    { "WeighingScaleId", weighingScale.Id },
                    { "Operation", "CreateTicket" },
                    { "Step", "RequestSent" }
                });

            var newInternalId = await _externalTicketingSystem.GetExistingTicketInternalIdAsync(weighingScale.Id, cancellationToken);
            if (!string.IsNullOrEmpty(newInternalId))
            {
                await _unitOfWork.LogEventMessage.GenerateLogEventMessage(
                    $"Ticket creado exitosamente para WeighingScale {weighingScale.Id} con ID interno {newInternalId}",
                    new Dictionary<string, object>
                    {
                        { "WeighingScaleId", weighingScale.Id },
                        { "NewInternalId", newInternalId },
                        { "Operation", "CreateTicket" },
                        { "Result", "Success" }
                    });
                return SyncOperationOutcome.Success(newInternalId);
            }

            await _unitOfWork.LogEventMessage.GenerateLogEventMessage(
                $"Ticket creado para WeighingScale {weighingScale.Id} pero no se pudo recuperar el ID interno",
                new Dictionary<string, object>
                {
                    { "WeighingScaleId", weighingScale.Id },
                    { "Operation", "CreateTicket" },
                    { "Result", "PartialSuccess" },
                    { "Issue", "InternalIdNotRetrieved" }
                });
            return SyncOperationOutcome.RetryableFailure("Ticket created but InternalId not retrieved", SynchronizationStatus.PendingCreation);
        }
        catch (Exception ex)
        {
            await _unitOfWork.LogEventMessage.GenerateLogEventMessage(
                $"Error creando ticket para WeighingScale {weighingScale.Id}: {ex.Message}",
                new Dictionary<string, object>
                {
                    { "WeighingScaleId", weighingScale.Id },
                    { "Operation", "CreateTicket" },
                    { "ErrorMessage", ex.Message },
                    { "StackTrace", ex.StackTrace ?? string.Empty },
                    { "Result", "RetryableFailure" }
                });
            return SyncOperationOutcome.RetryableFailure(ex.Message, SynchronizationStatus.PendingCreation);
        }
    }

    public async Task<SyncOperationOutcome> UpdateTicketAsync(WeighingScale weighingScale, string urbetrackInternalId, CancellationToken cancellationToken = default)
    {
        if (weighingScale is null)
            throw new ArgumentNullException(nameof(weighingScale));

        var validationResult = weighingScale.ValidateForUpsertSynchronization();
        if (!validationResult.IsValid)
            return SyncOperationOutcome.UpdateFailure(validationResult.ErrorMessage ?? "Validation failed");

        try
        {
            var existingInternalId = await _externalTicketingSystem.GetExistingTicketInternalIdAsync(weighingScale.Id, cancellationToken);
            if (string.IsNullOrEmpty(existingInternalId))
            {
                await _unitOfWork.LogEventMessage.GenerateLogEventMessage(
                    $"No se encontró ticket existente para WeighingScale {weighingScale.Id} durante operación de actualización",
                    new Dictionary<string, object>
                    {
                        { "WeighingScaleId", weighingScale.Id },
                        { "Operation", "UpdateTicket" },
                        { "Result", "UpdateFailure" },
                        { "Issue", "NoExistingTicketFound" }
                    });
                return SyncOperationOutcome.UpdateFailure("No existing ticket found to update");
            }

            await _unitOfWork.LogEventMessage.GenerateLogEventMessage(
                $"Se encontró ticket existente para WeighingScale {weighingScale.Id} con ID interno {existingInternalId}, procediendo con eliminación",
                new Dictionary<string, object>
                {
                    { "WeighingScaleId", weighingScale.Id },
                    { "ExistingInternalId", existingInternalId },
                    { "Operation", "UpdateTicket" },
                    { "Step", "FoundExistingTicket" }
                });

            await _externalTicketingSystem.DeleteTicketAsync(existingInternalId, cancellationToken);

            var ticketAfterDelete = await _externalTicketingSystem.GetExistingTicketInternalIdAsync(weighingScale.Id, cancellationToken);
            if (!string.IsNullOrEmpty(ticketAfterDelete))
            {
                await _unitOfWork.LogEventMessage.GenerateLogEventMessage(
                    $"Error al eliminar ticket existente para WeighingScale {weighingScale.Id}. El ticket aún existe con ID interno {ticketAfterDelete}",
                    new Dictionary<string, object>
                    {
                        { "WeighingScaleId", weighingScale.Id },
                        { "TicketAfterDelete", ticketAfterDelete },
                        { "Operation", "UpdateTicket" },
                        { "Result", "RetryableFailure" },
                        { "Issue", "FailedToDeleteExistingTicket" }
                    });
                return SyncOperationOutcome.RetryableFailure("Failed to delete existing ticket before creating new one", SynchronizationStatus.PendingModification);
            }

            await _unitOfWork.LogEventMessage.GenerateLogEventMessage(
                $"Ticket eliminado exitosamente para WeighingScale {weighingScale.Id}, procediendo con creación",
                new Dictionary<string, object>
                {
                    { "WeighingScaleId", weighingScale.Id },
                    { "Operation", "UpdateTicket" },
                    { "Step", "SuccessfullyDeleted" }
                });

            await _externalTicketingSystem.CreateTicketAsync(weighingScale, cancellationToken);
            await _unitOfWork.LogEventMessage.GenerateLogEventMessage(
                $"Solicitud de creación de ticket enviada para WeighingScale {weighingScale.Id}",
                new Dictionary<string, object>
                {
                    { "WeighingScaleId", weighingScale.Id },
                    { "Operation", "UpdateTicket" },
                    { "Step", "CreationRequestSent" }
                });

            var newInternalId = await _externalTicketingSystem.GetExistingTicketInternalIdAsync(weighingScale.Id, cancellationToken);
            if (!string.IsNullOrEmpty(newInternalId))
            {
                await _unitOfWork.LogEventMessage.GenerateLogEventMessage(
                    $"Ticket actualizado exitosamente para WeighingScale {weighingScale.Id} con nuevo ID interno {newInternalId}",
                    new Dictionary<string, object>
                    {
                        { "WeighingScaleId", weighingScale.Id },
                        { "NewInternalId", newInternalId },
                        { "PreviousInternalId", urbetrackInternalId },
                        { "Operation", "UpdateTicket" },
                        { "Result", "Success" }
                    });
                return SyncOperationOutcome.Success(newInternalId);
            }

            await _unitOfWork.LogEventMessage.GenerateLogEventMessage(
                $"Ticket actualizado para WeighingScale {weighingScale.Id} pero no se pudo recuperar el nuevo ID interno",
                new Dictionary<string, object>
                {
                    { "WeighingScaleId", weighingScale.Id },
                    { "Operation", "UpdateTicket" },
                    { "Result", "PartialSuccess" },
                    { "Issue", "NewInternalIdNotRetrieved" }
                });
            return SyncOperationOutcome.RetryableFailure("Ticket updated but new InternalId not retrieved", SynchronizationStatus.PendingModification);
        }
        catch (Exception ex)
        {
            await _unitOfWork.LogEventMessage.GenerateLogEventMessage(
                $"Error actualizando ticket para WeighingScale {weighingScale.Id}: {ex.Message}",
                new Dictionary<string, object>
                {
                    { "WeighingScaleId", weighingScale.Id },
                    { "Operation", "UpdateTicket" },
                    { "ErrorMessage", ex.Message },
                    { "StackTrace", ex.StackTrace ?? string.Empty },
                    { "Result", "RetryableFailure" }
                });
            return SyncOperationOutcome.RetryableFailure(ex.Message, SynchronizationStatus.PendingModification);
        }
    }

    public async Task<SyncOperationOutcome> CancelTicketAsync(WeighingScale weighingScale, string? urbetrackInternalId, CancellationToken cancellationToken = default)
    {
        var validationResult = weighingScale.ValidateForCancellationSynchronization();
        if (!validationResult.IsValid)
        {
            return SyncOperationOutcome.CancellationFailure(validationResult.ErrorMessage ?? "Validation failed");
        }

        try
        {
            var internalIdToCancel = urbetrackInternalId;

            if (string.IsNullOrEmpty(internalIdToCancel))
            {
                var existingInternalId = await _externalTicketingSystem.GetExistingTicketInternalIdAsync(weighingScale.Id, cancellationToken);

                if (string.IsNullOrEmpty(existingInternalId))
                {
                    await _unitOfWork.LogEventMessage.GenerateLogEventMessage(
                        $"No se encontró ticket existente para WeighingScale {weighingScale.Id} durante operación de cancelación",
                        new Dictionary<string, object>
                        {
                            { "WeighingScaleId", weighingScale.Id },
                            { "Operation", "CancelTicket" },
                            { "Result", "CancellationFailure" },
                            { "Issue", "NoExistingTicketFound" }
                        });
                    return SyncOperationOutcome.CancellationFailure("No existing ticket found to cancel");
                }

                internalIdToCancel = existingInternalId;

                await _unitOfWork.LogEventMessage.GenerateLogEventMessage(
                    $"Detectado registro antiguo para WeighingScale {weighingScale.Id} con ID interno {existingInternalId}, procediendo con cancelación",
                    new Dictionary<string, object>
                    {
                        { "WeighingScaleId", weighingScale.Id },
                        { "LegacyInternalId", existingInternalId },
                        { "Operation", "CancelTicket" },
                        { "Step", "FoundLegacyTicket" }
                    });
            }

            await _externalTicketingSystem.DeleteTicketAsync(internalIdToCancel, cancellationToken);
            await _unitOfWork.LogEventMessage.GenerateLogEventMessage(
                $"Ticket cancelado exitosamente para WeighingScale {weighingScale.Id} con ID interno {internalIdToCancel}",
                new Dictionary<string, object>
                {
                    { "WeighingScaleId", weighingScale.Id },
                    { "UrbetrackInternalId", internalIdToCancel },
                    { "Operation", "CancelTicket" },
                    { "Result", "Success" }
                });
            return SyncOperationOutcome.CancellationSuccess(internalIdToCancel);
        }
        catch (Exception ex)
        {
            await _unitOfWork.LogEventMessage.GenerateLogEventMessage(
                $"Error al cancelar ticket para WeighingScale {weighingScale.Id}: {ex.Message}",
                new Dictionary<string, object>
                {
                    { "WeighingScaleId", weighingScale.Id },
                    { "UrbetrackInternalId", urbetrackInternalId ?? "null" },
                    { "Operation", "CancelTicket" },
                    { "ErrorMessage", ex.Message },
                    { "StackTrace", ex.StackTrace ?? string.Empty },
                    { "Result", "RetryableFailure" }
                });
            return SyncOperationOutcome.RetryableFailure(ex.Message, SynchronizationStatus.PendingCancellation);
        }
    }
}