namespace Reports.Domain.ValueObjects;

public class SyncValidationResult
{
    public bool IsValid { get; private set; }
    public string? ErrorMessage { get; private set; }

    private SyncValidationResult(bool isValid, string? errorMessage = null)
    {
        IsValid = isValid;
        ErrorMessage = errorMessage;
    }

    public static SyncValidationResult Success()
    {
        return new SyncValidationResult(true);
    }

    public static SyncValidationResult Failure(string errorMessage)
    {
        if (string.IsNullOrWhiteSpace(errorMessage))
            throw new ArgumentException("Error message cannot be null or empty", nameof(errorMessage));

        return new SyncValidationResult(false, errorMessage);
    }

    public SyncOperationOutcome ToSyncOperationOutcome()
    {
        return IsValid
            ? SyncOperationOutcome.Success(string.Empty)
            : SyncOperationOutcome.CreationFailure(ErrorMessage!);
    }

    public SyncOperationOutcome ToCreationFailure()
    {
        return IsValid
            ? SyncOperationOutcome.Success(string.Empty)
            : SyncOperationOutcome.CreationFailure(ErrorMessage!);
    }

    public SyncOperationOutcome ToUpdateFailure()
    {
        return IsValid
            ? SyncOperationOutcome.Success(string.Empty)
            : SyncOperationOutcome.UpdateFailure(ErrorMessage!);
    }

    public SyncOperationOutcome ToCancellationFailure()
    {
        return IsValid
            ? SyncOperationOutcome.Success(string.Empty)
            : SyncOperationOutcome.CancellationFailure(ErrorMessage!);
    }
}