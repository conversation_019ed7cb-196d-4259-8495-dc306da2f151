using Reports.Domain.Common;
using Reports.Domain.Constants;

namespace Reports.Domain.ValueObjects;

public sealed class SUIReportFormat14Extended : SUIReportFormat14
{
    public long? CE_ID_TICKET { get; init; }
    public string CE_NOMBRE_AREA { get; init; }
    public string CE_RUTA_LARGA { get; init; }
    public string CE_TON_TOTAL { get; init; }
    public bool? CE_REL_COMPENSACION { get; init; }
    public long? CE_REL_COMPENSACION_ID_TICKET { get; init; }
    
    protected SUIReportFormat14Extended(long NUAP, DestinationType DestinationType, string DestinationCode,
        string LicensePlate, DateOnly VehicleArrival, TimeOnly VehicleArrivalTime,
        long MicrorouteId, decimal UrbanCleaningTons, decimal SweepingTons,
        decimal NonRecyclableTons, decimal RejectedTons, decimal RecyclableTons,
        int MeasuringUnit, decimal Toll,
        long? ServiceTicketId, string RecyclingArea, string ExtendedRouteCode, decimal TotalTons,
        bool? CompensationRelation, long? CompensationRelationTicketId)
        : base(NUAP, DestinationType, DestinationCode, LicensePlate, VehicleArrival, VehicleArrivalTime,
            MicrorouteId, UrbanCleaningTons, SweepingTons, NonRecyclableTons, RejectedTons, RecyclableTons,
            MeasuringUnit, Toll)
    {
        CE_ID_TICKET = ServiceTicketId;
        CE_NOMBRE_AREA = RecyclingArea;
        CE_RUTA_LARGA = ExtendedRouteCode;
        CE_TON_TOTAL = SUIFormats.FormattedTons(TotalTons);
        CE_REL_COMPENSACION = CompensationRelation;
        CE_REL_COMPENSACION_ID_TICKET = CompensationRelationTicketId;
    }
    
    public static SUIReportFormat14Extended Create(long NUAP, DestinationType DestinationType, string DestinationCode,
        string LicensePlate, DateOnly VehicleArrival, TimeOnly VehicleArrivalTime,
        long MicrorouteId, decimal UrbanCleaningTons, decimal SweepingTons,
        decimal NonRecyclableTons, decimal RejectedTons, decimal RecyclableTons,
        int MeasuringUnit, decimal Toll,
        long? ServiceTicketId, string RecyclingArea, string ExtendedRouteCode, decimal TotalTons,
        bool? CompensationRelation, long? CompensationRelationTicketId) =>
        new(NUAP, DestinationType, DestinationCode, LicensePlate, VehicleArrival, VehicleArrivalTime,
            MicrorouteId, UrbanCleaningTons, SweepingTons, NonRecyclableTons, RejectedTons, RecyclableTons,
            MeasuringUnit, Toll, ServiceTicketId, RecyclingArea, ExtendedRouteCode, TotalTons,
            CompensationRelation, CompensationRelationTicketId);
}
    