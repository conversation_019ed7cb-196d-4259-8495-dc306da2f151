using Orion.SharedKernel.Domain.ValueObjects;
using Reports.Domain.Entities;

namespace Reports.Domain.ValueObjects;

public class BatchSyncSummary : ValueObject
{
    private readonly List<UrbetrackSynchronization> _processedEntries;
    private readonly List<UrbetrackSynchronization> _failedEntries;

    public BatchSyncSummary()
    {
        _processedEntries = new List<UrbetrackSynchronization>();
        _failedEntries = new List<UrbetrackSynchronization>();
    }

    public IReadOnlyList<UrbetrackSynchronization> ProcessedEntries => _processedEntries.AsReadOnly();

    public IReadOnlyList<UrbetrackSynchronization> FailedEntries => _failedEntries.AsReadOnly();

    public int TotalProcessed => _processedEntries.Count + _failedEntries.Count;

    public int SuccessCount => _processedEntries.Count(e => e.Status == Constants.SynchronizationStatus.Synchronized || 
                                                            e.Status == Constants.SynchronizationStatus.Cancelled);

    public int ErrorCount => _processedEntries.Count(e => e.Status != Constants.SynchronizationStatus.Synchronized && 
                                                          e.Status != Constants.SynchronizationStatus.Cancelled);
                                                          
    public int FailedDueToRetryLimit => _failedEntries.Count;

    public bool HasErrors => ErrorCount > 0 || FailedDueToRetryLimit > 0;

    public void AddProcessedEntry(UrbetrackSynchronization entry)
    {
        if (entry == null) throw new ArgumentNullException(nameof(entry));
        _processedEntries.Add(entry);
    }

    public void AddFailedEntry(UrbetrackSynchronization entry)
    {
        if (entry == null) throw new ArgumentNullException(nameof(entry));
        _failedEntries.Add(entry);
    }

    public void Merge(IReadOnlyList<UrbetrackSynchronization> processedEntries, IReadOnlyList<UrbetrackSynchronization> failedEntries)
    {
        _processedEntries.AddRange(processedEntries);
        _failedEntries.AddRange(failedEntries);
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return _processedEntries;
        yield return _failedEntries;
    }
}