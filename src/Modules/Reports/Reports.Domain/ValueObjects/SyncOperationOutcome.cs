using Orion.SharedKernel.Domain.ValueObjects;
using Reports.Domain.Constants;

namespace Reports.Domain.ValueObjects;

public class SyncOperationOutcome : ValueObject
{
    public bool IsSuccess { get; private set; }
    public SynchronizationStatus Status { get; private set; }
    public string? UrbetrackInternalId { get; private set; }
    public string? ErrorMessage { get; private set; }
    public bool ShouldRetry { get; private set; }

    private SyncOperationOutcome(bool isSuccess, SynchronizationStatus status, string? urbetrackInternalId = null, 
        string? errorMessage = null, bool shouldRetry = false)
    {
        IsSuccess = isSuccess;
        Status = status;
        UrbetrackInternalId = urbetrackInternalId;
        ErrorMessage = errorMessage;
        ShouldRetry = shouldRetry;
    }

    public static SyncOperationOutcome Success(string urbetrackInternalId)
        => new(true, SynchronizationStatus.Synchronized, urbetrackInternalId);

    public static SyncOperationOutcome AlreadyExists(string urbetrackInternalId)
        => new(true, SynchronizationStatus.Synchronized, urbetrackInternalId);

    public static SyncOperationOutcome RetryableFailure(string errorMessage, SynchronizationStatus status)
        => new(false, status, null, errorMessage, true);

    public static SyncOperationOutcome CreationFailure(string errorMessage)
        => new(false, SynchronizationStatus.CreationFailure, null, errorMessage, false);

    public static SyncOperationOutcome UpdateFailure(string errorMessage)
        => new(false, SynchronizationStatus.UpdateFailure, null, errorMessage, false);

    public static SyncOperationOutcome CancellationFailure(string errorMessage)
        => new(false, SynchronizationStatus.CancellationFailure, null, errorMessage, false);

    public static SyncOperationOutcome CancellationSuccess(string urbetrackInternalId)
        => new(true, SynchronizationStatus.Cancelled, urbetrackInternalId);

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return IsSuccess;
        yield return Status;
        yield return UrbetrackInternalId;
        yield return ErrorMessage;
        yield return ShouldRetry;
    }
}
