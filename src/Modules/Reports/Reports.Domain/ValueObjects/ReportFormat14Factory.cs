using Orion.SharedKernel.Domain.ValueObjects;
using Reports.Domain.Entities;

namespace Reports.Domain.ValueObjects;

public sealed class ReportFormat14Factory : ValueObject
{
    private const int EmvariasNUAP = *********;
    private const int ItaguiNUAP = *********;
    private const int AmountOfTravelsPerEntry = 2;
    private const string RouteCodeWithSpecialDistribution = "0614001";
    private readonly List<ReportFormat14> _reportEntries = new List<ReportFormat14>();
    private readonly List<Toll> _periodTolls = new List<Toll>();
    private readonly string[] TollExcludedAreas = { "CLUS - CARGADO", "APROVECHAMIENTO - CARGADO" };

    private ReportFormat14Factory(IEnumerable<ReportFormat14> reportEntries,
        IEnumerable<Toll> allTolls, DateOnly filteredPeriod)
    {
        _reportEntries.AddRange(reportEntries);

        ConfigureClosestTollsToPeriod(allTolls, filteredPeriod);
        ConfigureTolls();
        ConfigureSpecialDistributionTolls();
    }

    private void ConfigureClosestTollsToPeriod(IEnumerable<Toll> allTolls, DateOnly filteredPeriod)
    {
        var targetDate = filteredPeriod.ToDateTime(TimeOnly.MinValue);
        var closestToll = GetClosestToll(allTolls, targetDate);

        _periodTolls.AddRange(allTolls.Where(toll => toll.ValidFromDate == closestToll.ValidFromDate));
    }

    private static Toll GetClosestToll(IEnumerable<Toll> tolls, DateTime targetDate)
    {
        var tollsFromTargetYear = tolls.Where(toll => toll.ValidFromDate.Year == targetDate.Year);

        return tollsFromTargetYear.Any()
            ? GetClosestTollByDate(tollsFromTargetYear, targetDate)
            : GetClosestTollByDate(tolls, targetDate);
    }

    private static Toll GetClosestTollByDate(IEnumerable<Toll> tolls, DateTime targetDate)
    {
        return tolls
            .OrderBy(toll => Math.Abs((toll.ValidFromDate - targetDate).TotalDays))
            .First();
    }

    private void ConfigureTolls()
    {
        var tollsToSubstractPerArea = _reportEntries
            .Where(x => x.NUAP is not EmvariasNUAP)
            .GroupBy(x => x.ServiceTicketId)
            .Select(x => new
            {
                x.Key,
                AcumulatedTolls = x.Sum(y => y.Toll)
            })
            .ToList();

        foreach (var entry in _reportEntries
                     .Where(x => x.NUAP is EmvariasNUAP))
        {
            if (TollExcludedAreas.Contains(entry.RecyclingArea)) continue;

            var latestTotalTollAmount = _periodTolls
                                            .Find(x => x.LicensePlate == entry.LicensePlate)?.Value
                                        ?? decimal.Zero;

            var tollsToSubstract = tollsToSubstractPerArea
                .Find(y => y.Key == entry.ServiceTicketId)?
                .AcumulatedTolls ?? decimal.Zero;

            entry.Toll = (latestTotalTollAmount * AmountOfTravelsPerEntry) - tollsToSubstract;
        }
    }

    private void ConfigureSpecialDistributionTolls()
    {
        _reportEntries
            .Where(x => x is
            {
                ExtendedRouteCode: RouteCodeWithSpecialDistribution,
                NUAP: EmvariasNUAP
            })
            .ToList()
            .ForEach(x =>
            {
                var itaguiChildEntry = _reportEntries
                     .Find(y => y.NUAP == ItaguiNUAP && y.ServiceTicketId == x.ServiceTicketId);

                if (itaguiChildEntry is null)
                    return;

                itaguiChildEntry.Toll = x.Toll * 0.7m;

                x.Toll *= 0.3m;
            });
    }

    public IReadOnlyList<ReportFormat14> GetReportFormat14() => _reportEntries;

    public IReadOnlyList<SUIReportFormat14Extended> GetExtendedReportFormat14() =>
        _reportEntries
            .Select(x => x.ToSUIReportFormat14Extended())
            .ToList();

    public static ReportFormat14Factory Create(IEnumerable<ReportFormat14> reportEntries,
        IEnumerable<Toll> periodTolls,
        DateOnly filteredPeriod) =>
        new(reportEntries, periodTolls, filteredPeriod);

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return _reportEntries;
        yield return _periodTolls;
    }
}