namespace Reports.Domain.ValueObjects;

public class RetryPolicy
{
    public int MaxRetryCount { get; private set; }
    public int CurrentRetryCount { get; private set; }

    public RetryPolicy(int maxRetryCount, int currentRetryCount = 0)
    {
        if (maxRetryCount < 0)
            throw new ArgumentException("Max retry count cannot be negative", nameof(maxRetryCount));
        
        if (currentRetryCount < 0)
            throw new ArgumentException("Current retry count cannot be negative", nameof(currentRetryCount));

        MaxRetryCount = maxRetryCount;
        CurrentRetryCount = currentRetryCount;
    }

    public bool CanRetry => CurrentRetryCount < MaxRetryCount;
    public bool HasExceededLimit => CurrentRetryCount >= MaxRetryCount;

    public RetryPolicy IncrementRetry()
    {
        return new RetryPolicy(MaxRetryCount, CurrentRetryCount + 1);
    }

    public static RetryPolicy Default() => new(3);
}
