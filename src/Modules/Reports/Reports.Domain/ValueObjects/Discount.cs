using Orion.SharedKernel.Domain.ValueObjects;

namespace Reports.Domain.Entities;

public sealed class Discount : ValueObject
{
    public const string NotFoundDescription = "N/A";
    public long Id { get; init; }
    public string Period { get; init; }
    public string LicensePlate { get; init; }
    public string RouteCode { get; init; }
    public string ServiceDescription { get; init; }
    public decimal Tons { get; init; }

    public Discount(long id, DateOnly date, string licensePlate, decimal tons,
        IReadOnlyList<CollectionByMicroroute> collections)
    {
        Validate(id, date, licensePlate, tons, collections);
        
        var collection = collections.FirstOrDefault(x => x.Id == id);

        Id = id;
        Period = date.ToString("yyyy-MM");
        LicensePlate = licensePlate;
        RouteCode = collection?.RouteCode ?? NotFoundDescription;
        ServiceDescription = collection?.ServiceType ?? NotFoundDescription;
        Tons = tons;
    }
    
    private static void Validate(long id, DateOnly date, string licensePlate, decimal tons, IReadOnlyList<CollectionByMicroroute> collections)
    {
        const int LicensePlateMaxLength = 6;
        
        if (id == default)
            throw new ArgumentException(
                "Se necesita el número de ticket para crear el descuento.",
                nameof(id));
        
        if (date == default)
            throw new ArgumentException(
                "Se necesita la fecha para crear el descuento.",
                nameof(date));
        
        if (string.IsNullOrEmpty(licensePlate))
            throw new ArgumentException(
                "La placa del vehículo del descuento no puede ser nula.",
                nameof(licensePlate));
        
        if (licensePlate.Length > LicensePlateMaxLength)
            throw new ArgumentException(
                "La placa del vehículo del descuento no debe tener más de 6 caracteres.",
                nameof(licensePlate));
        
        if (tons <= 0)
            throw new ArgumentException(
                "Las toneladas del descuento no pueden ser menores a cero.",
                nameof(tons));
        
        if (collections.Count == 0)
            throw new ArgumentException(
                "El listado de despachos no puede estar vacío.",
                nameof(collections));
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Id;
        yield return Period;
        yield return LicensePlate;
        yield return RouteCode;
        yield return ServiceDescription;
        yield return Tons;
    }
}