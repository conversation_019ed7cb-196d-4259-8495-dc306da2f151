using Reports.Domain.Entities;

namespace Reports.Domain.ValueObjects;

public class WeighingClassificationResult
{
    public IReadOnlyList<WeighingScale> WeighingsToInsert { get; init; } = [];
    public IReadOnlyList<WeighingScale> WeighingsToUpdate { get; init; } = [];
    public IReadOnlyList<WeighingScale> WeighingsToCancel { get; init; } = [];
    public IReadOnlyList<WeighingScale> PreviousEntitiesForUpdate { get; init; } = [];
    public IReadOnlyList<WeighingScale> PreviousEntitiesForCancel { get; init; } = [];
}