using Reports.Domain.Entities;

namespace Reports.Domain.Services;

public interface IExternalTicketingSystem
{
    Task<string?> GetExistingTicketInternalIdAsync(long weighingScaleId, CancellationToken cancellationToken = default);

    Task CreateTicketAsync(WeighingScale weighingScale, CancellationToken cancellationToken = default);

    Task DeleteTicketAsync(string internalId, CancellationToken cancellationToken = default);
}