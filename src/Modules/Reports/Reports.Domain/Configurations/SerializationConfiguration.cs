using System.Text.Json;

namespace Reports.Domain.Configurations;

public static class SerializationConfiguration
{
    public static JsonSerializerOptions DefaultJsonOptions => new()
    {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        WriteIndented = false,
        DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.Never,
        IncludeFields = true,
        PropertyNameCaseInsensitive = true
    };
}