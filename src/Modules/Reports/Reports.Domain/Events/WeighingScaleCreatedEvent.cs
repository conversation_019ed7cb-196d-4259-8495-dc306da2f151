using Orion.SharedKernel.Domain.Entities.Events;
using Reports.Domain.Entities;

namespace Reports.Domain.Events;

public class WeighingScaleCreatedEvent : DomainEvent
{
    public WeighingScaleCreatedEvent(IEnumerable<WeighingScale> weighingScales, string user)
    {
        WeighingScales = weighingScales;
        User = user;
    }
    
    public IEnumerable<WeighingScale> WeighingScales { get; }
    public string User { get; }
}
