namespace Reports.Domain.Exceptions;

public class SynchronizationDomainException : Exception
{
    public SynchronizationDomainException(string message) : base(message)
    {
    }

    public SynchronizationDomainException(string message, Exception innerException) : base(message, innerException)
    {
    }
}

public class RetryLimitExceededException : SynchronizationDomainException
{
    public int RetryCount { get; }
    public int MaxRetryCount { get; }

    public RetryLimitExceededException(int retryCount, int maxRetryCount) 
        : base($"Retry limit exceeded. Current: {retryCount}, Max: {maxRetryCount}")
    {
        RetryCount = retryCount;
        MaxRetryCount = maxRetryCount;
    }
}

public class AuthenticationFailedException : SynchronizationDomainException
{
    public AuthenticationFailedException(string message) : base($"Authentication failed: {message}")
    {
    }

    public AuthenticationFailedException(string message, Exception innerException) 
        : base($"Authentication failed: {message}", innerException)
    {
    }
}
