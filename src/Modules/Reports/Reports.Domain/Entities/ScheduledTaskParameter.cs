using Orion.SharedKernel.Domain.Entities;
using Reports.Domain.Constants;

namespace Reports.Domain.Entities;

public class ScheduledTaskParameter : Entity<int>
{
    public string Name { get; set; }
    public string? Description { get; set; }
    public ScheduledTaskExecutors ScheduledTaskExecutors { get; set; }
    public bool IsEnabled { get; set; }
    public int FrequencyInMinutes { get; set; }
    public DateTime? LastRunTime { get; set; }
    public DateTime? NextRunTime { get; set; }
}
