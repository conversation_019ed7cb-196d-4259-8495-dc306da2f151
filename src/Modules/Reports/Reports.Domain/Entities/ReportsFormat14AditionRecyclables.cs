using Orion.SharedKernel.Domain.Entities;
using Reports.Domain.Constants;

namespace Reports.Domain.Entities;

public class ReportsFormat14AditionRecyclables : Entity<string>
{
    public long NUAP { get; private set; }
    public DestinationType DestinationType { get; private set; }
    public string DestinationCode { get; private set; }
    public string LicensePlate { get; private set; }
    public DateOnly VehicleArrival { get; private set; }
    public TimeOnly VehicleArrivalTime { get; private set; }
    public long MicrorouteId { get; private set; }
    public double UrbanCleaningTons { get; set; }
    public double SweepingTons { get; set; }
    public double NonRecyclableTons { get; set; }
    public double RejectedTons { get; set; }
    public double RecyclableTons { get; set; }
    public int MeasuringUnit { get; set; }
    public double Toll { get; set; }
    public string ExtendedRouteCode { get; set; }
    public double TotalTons { get; set; }
}