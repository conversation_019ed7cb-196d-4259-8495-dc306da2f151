using Reports.Domain.Constants;

namespace Reports.Domain.Entities;

public record ReportFilter
{
    public DateOnly FromDate { get; init; }
    public DateOnly ToDate { get; init; }
    public OriginType? OriginType { get; init; }
    public long? NIT { get; init; }
    public string? LicensePlate { get; init; }
    public long? WeighinId { get; init; }
    public long? NUAP { get; init; }
    public string? RouteCode { get; init; }
    public bool? ExistsInFormat14 { get; init; }
    public ReportType ReportType { get; init; }

    protected ReportFilter(DateTime fromDate, DateTime toDate, string? originType,
        long? nit, string? licensePlate, long? weighinId, long? nuap, string? routeCode, bool? existsInFormat14,
        string reportType)
    {
        FromDate = DateOnly.FromDateTime(fromDate);
        ToDate = DateOnly.FromDateTime(toDate);
        OriginType = originType != null 
            ? Enum.Parse<OriginType>(originType) 
            : null;
        NIT = nit;
        LicensePlate = licensePlate;
        WeighinId = weighinId;
        NUAP = nuap;
        RouteCode = string.IsNullOrEmpty(routeCode) ? null : routeCode;
        ExistsInFormat14 = existsInFormat14;
        ReportType = Enum.Parse<ReportType>(reportType!);
    }
    
    public static ReportFilter Create(DateTime fromDate, DateTime toDate, string? originType,
        long? nit, string? licensePlate, long? weighinId, long? nuap, string? routeCode,
        bool? existsInFormat14, string reportType) =>
        new(fromDate, toDate, originType, nit, licensePlate, weighinId, nuap, routeCode, existsInFormat14, reportType);
}