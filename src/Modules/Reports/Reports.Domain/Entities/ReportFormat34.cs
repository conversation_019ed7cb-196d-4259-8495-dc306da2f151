using Orion.SharedKernel.Domain.Entities;
using Reports.Domain.Constants;
using Reports.Domain.ValueObjects;

namespace Reports.Domain.Entities;

public class ReportFormat34 : Entity<string>
{
    public long NUSD { get; init; }
    public OriginType OriginType { get; init; }
    public long? PlaceOriginNumber { get; init; }
    public string? CompanyName { get; init; }
    public long? NIT { get; init; }
    public string? DaneCode { get; init; }
    public string LicensePlate { get; init; }
    public DateOnly ArrivalDate { get; init; }
    public TimeOnly ArrivalTime { get; init; }
    public DateOnly DepartureDate { get; init; }
    public TimeOnly DepartureTime { get; init; }
    public decimal Tons { get; init; }
    
    //Validation Fields
    public long ServiceTicketId { get; init; }
    public decimal ServiceTicketWeight { get; init; }
    public decimal ServiceTicketTonnage { get; init; }
    public decimal RejectedTonnage { get; init; }
    public bool ExistsInReport14 { get; init; }
    public long CompanyNIT { get; init; }
    public long? NUAP { get; init; }
    public DateOnly FilteredDate { get; init; }
    
    public SUIReportFormat34 ToSUIReportFormat34() =>
        SUIReportFormat34.Create(NUSD, OriginType, PlaceOriginNumber, CompanyName, NIT, DaneCode,
            LicensePlate, ArrivalDate, DepartureDate, ArrivalTime, DepartureTime, Tons);
}