using Orion.SharedKernel.Domain.Entities;

namespace Reports.Domain.Entities;

public class Rejection : Entity<long>
{
    public string LicensePlate { get; private set; }
    public DateOnly RejectionDate { get; private set; }
    public string ExtendedRouteCode { get; private set; }
    public long ECA { get; private set; }
    public decimal Tonnage { get; private set; }

    public Rejection() { }

    public Rejection(string licensePlate, DateOnly rejectionDate, string extendedRouteCode, long eca, decimal tonnage)
    {
        LicensePlate = licensePlate;
        RejectionDate = rejectionDate;
        ExtendedRouteCode = extendedRouteCode;
        ECA = eca;
        Tonnage = tonnage;
    }
}