using Orion.SharedKernel.Domain.Entities;

namespace Reports.Domain.Entities;

public class MicroRoute : Entity<long>
{
    public long ExternalRouteCode { get; set; }
    public string ExtendedRouteCode { get; set; }
    public int UrbanCleaningPercentage { get; set; }
    public int SweepingPercentage { get; set; }
    public int NonAforatedPercentage { get; set; }
    public int RecyclableTonsPercentage { get; set; }
    public DateTime StartDateValidity { get; set; }
    public DateTime? EndDateValidity { get; set; }
    public long NUAP { get; set; }
}