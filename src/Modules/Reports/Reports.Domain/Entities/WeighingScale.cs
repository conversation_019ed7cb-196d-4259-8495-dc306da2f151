using Orion.SharedKernel.Domain.Entities;
using Reports.Domain.Constants;
using Reports.Domain.ValueObjects;

namespace Reports.Domain.Entities;

public class WeighingScale : Entity<long>
{
    private const long DefaultDepositPlace = 720105237;
    private const int MinimumTownCodeChars = 5;

    public string LicensePlate { get; set; }
    public long NIT { get; set; }
    public int ArrivingWeight { get; set; }
    public int DepositWeight { get; set; }
    public DateTime EntryDate { get; set; }
    public DateTime EgressDate { get; set; }
    public string MaterialType { get; set; }
    public long DepositPlace { get; set; } = DefaultDepositPlace;
    public OriginType OriginType { get; set; }
    public long NUAP { get; set; }
    public DateTime LoadingDate { get; set; }
    public DateTime? CancelDate { get; set; }
    public LoadingType LoadingType { get; set; }
    public string TownCode { get; set; }
    public Town Town { get; set; }
    public int LeavingWeight => ArrivingWeight - DepositWeight;
    public string CompanyName { get; set; } = string.Empty;

    public WeighingScale() { }

    public WeighingScale(long id, string licensePlate, long nit, int arrivingWeight, int depositWeight, DateTime entryDate,
        DateTime egressDate, long depositPlace, OriginType originType, long nuap, DateTime loadingDate,
        LoadingType loadingType, string townCode, Town town)
    {
        Id = id;
        LicensePlate = licensePlate;
        NIT = nit;
        ArrivingWeight = arrivingWeight;
        DepositWeight = depositWeight;
        EntryDate = entryDate;
        EgressDate = egressDate;
        DepositPlace = depositPlace;
        OriginType = originType;
        NUAP = nuap;
        LoadingDate = loadingDate;
        LoadingType = loadingType;
        TownCode = townCode;
        Town = town;
    }

    public WeighingScale SetTownCode(string code)
    {
        TownCode = code.PadLeft(MinimumTownCodeChars, '0');
        return this;
    }

    public SyncValidationResult ValidateForUpsertSynchronization()
    {
        if (CancelDate.HasValue)
            return SyncValidationResult.Failure("WeighingScale cannot be synchronized because it has been cancelled");

        return SyncValidationResult.Success();
    }

    public SyncValidationResult ValidateForCancellationSynchronization()
    {
        if (!CancelDate.HasValue)
            return SyncValidationResult.Failure("Synchronized WeighingScale cannot be cancelled because it does not have a cancellation date");

        return SyncValidationResult.Success();
    }

    private bool IsCancellationOperation() => CancelDate.HasValue;

    public WeighingOperationType DetermineOperationType(WeighingScale? existingWeighing = null)
    {
        if (existingWeighing is null)
            return IsCancellationOperation()
                ? WeighingOperationType.Cancel
                : WeighingOperationType.Insert;

        return IsCancellationOperation()
            ? WeighingOperationType.Cancel
            : WeighingOperationType.Update;
    }
}