using Orion.SharedKernel.Domain.Entities;
using Reports.Domain.Constants;
using Reports.Domain.ValueObjects;

namespace Reports.Domain.Entities;

public class UrbetrackSynchronization : Entity<int>
{
    public long WeighingScaleId { get; set; }
    public WeighingScale WeighingScale { get; set; }
    public SynchronizationStatus Status { get; set; }
    public int RetryCount { get; set; }
    public DateTime? LastSynchronization { get; set; }
    public string? UrbetrackInternalId { get; set; }
    public DateTime? LockedAt { get; set; }
    public string? LockedBy { get; set; }
    
    public void ApplySynchronizationResult(SyncOperationOutcome result)
    {
        if (result == null)
            throw new ArgumentNullException(nameof(result));

        Status = result.Status;

        if (result.IsSuccess)
        {
            UrbetrackInternalId = result.UrbetrackInternalId;
            LastSynchronization = DateTime.SpecifyKind(DateTime.UtcNow, DateTimeKind.Unspecified);
        }
        else if (result.ShouldRetry)
        {
            RetryCount++;
        }
    }

    public bool HasExceededRetryLimit(RetryPolicy retryPolicy)
    {
        var currentRetryPolicy = new RetryPolicy(retryPolicy.MaxRetryCount, RetryCount);
        return currentRetryPolicy.HasExceededLimit;
    }

    public bool IsLocked()
    {
        return Status == SynchronizationStatus.LockedForCreation ||
               Status == SynchronizationStatus.LockedForModification ||
               Status == SynchronizationStatus.LockedForCancellation ||
               Status == SynchronizationStatus.ProcessingCreation ||
               Status == SynchronizationStatus.ProcessingModification ||
               Status == SynchronizationStatus.ProcessingCancellation;
    }

    public void AcquireLock(string lockIdentifier)
    {
        Status = Status switch
        {
            SynchronizationStatus.PendingCreation => SynchronizationStatus.LockedForCreation,
            SynchronizationStatus.PendingModification => SynchronizationStatus.LockedForModification,
            SynchronizationStatus.PendingCancellation => SynchronizationStatus.LockedForCancellation,
            _ => throw new InvalidOperationException($"Cannot acquire lock for status {Status}")
        };
        LockedAt = DateTime.SpecifyKind(DateTime.UtcNow, DateTimeKind.Unspecified);
        LockedBy = lockIdentifier;
    }

    public void StartProcessing()
    {
        Status = Status switch
        {
            SynchronizationStatus.LockedForCreation => SynchronizationStatus.ProcessingCreation,
            SynchronizationStatus.LockedForModification => SynchronizationStatus.ProcessingModification,
            SynchronizationStatus.LockedForCancellation => SynchronizationStatus.ProcessingCancellation,
            _ => throw new InvalidOperationException($"Cannot start processing from status {Status}. Entry must be locked first.")
        };
    }

    public void ReleaseLock()
    {
        Status = Status switch
        {
            SynchronizationStatus.LockedForCreation => SynchronizationStatus.PendingCreation,
            SynchronizationStatus.LockedForModification => SynchronizationStatus.PendingModification,
            SynchronizationStatus.LockedForCancellation => SynchronizationStatus.PendingCancellation,
            SynchronizationStatus.ProcessingCreation => SynchronizationStatus.PendingCreation,
            SynchronizationStatus.ProcessingModification => SynchronizationStatus.PendingModification,
            SynchronizationStatus.ProcessingCancellation => SynchronizationStatus.PendingCancellation,
            _ => Status
        };
        LockedAt = null;
        LockedBy = null;
    }
}
