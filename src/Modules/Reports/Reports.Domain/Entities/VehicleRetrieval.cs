using Orion.SharedKernel.Domain.Entities;

namespace Reports.Domain.Entities;

public class VehicleRetrieval : Entity<int>
{
    public long NUAP { get; set; }
    public int Year { get; set; }
    public int Month { get; set; }
    public decimal Tons { get; set; }
        
    public VehicleRetrieval() { }

    public VehicleRetrieval(long nuap, int year, int month, decimal tons)
    {
        NUAP = nuap;
        Tons = tons;
    }
}