using System.Text.RegularExpressions;

namespace Reports.Domain.Common;

public class SortingOptions
{
    public static SortingOptions Default { get; } = new SortingOptions(SortDirection.Ascending);
    public SortDirection SortDirection { get;  }
    public IReadOnlyCollection<string> SortFields { get; }

    public SortingOptions(SortDirection sortDirection, IEnumerable<string>? sortFields = null)
    {
        SortDirection = sortDirection;
        SortFields = sortFields?.ToList() ?? [];
    }

    public SortingOptions(SortDirection sortDirection, string? sortFields)
        : this(sortDirection, ParseFields(sortFields)) { }

    public SortingOptions(string sortFields)
        : this(SortDirection.Ascending, ParseFields(sortFields)) { }

    public SortingOptions(string? sortDirection, string? sortFields)
        : this(ParseSortDirection(sortDirection), ParseFields(sortFields)) { }
    
    private static IEnumerable<string> ParseFields(string? sortFields)
    {
        return string.IsNullOrWhiteSpace(sortFields)
            ? []
            : Regex.Split(sortFields, @"\s*,\s*").Select(x => x);
    }
    
    private static SortDirection ParseSortDirection(string? sortDirection)
    {
        if (string.IsNullOrWhiteSpace(sortDirection))
            return SortDirection.Ascending;

        if (!Enum.TryParse<SortDirection>(sortDirection, true, out var direction))
            throw new ArgumentException($"Invalid sort direction: {sortDirection}", nameof(sortDirection));

        return direction;
    }
}