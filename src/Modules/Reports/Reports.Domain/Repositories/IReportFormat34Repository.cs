using System.Linq.Expressions;
using Orion.SharedKernel.Domain.Entities;
using Orion.SharedKernel.Domain.Repositories.Entities;
using Reports.Domain.Common;
using Reports.Domain.Entities;

namespace Reports.Domain.Repositories;

public interface IReportFormat34Repository : IReadRepository<ReportFormat34, string>
{
    Task<PaginatedResult<ReportFormat34>> GetFilteredReportAsync(Expression<Func<ReportFormat34, bool>> predicate,
        CancellationToken cancellationToken,
        bool isPaginated = true,
        int pageNumber = 1,
        int pageSize = 25,
        SortingOptions? sortingOptions = null);
}