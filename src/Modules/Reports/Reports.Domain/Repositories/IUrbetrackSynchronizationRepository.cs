using Orion.SharedKernel.Domain.Repositories.Entities;
using Reports.Domain.Constants;
using Reports.Domain.Entities;

namespace Reports.Domain.Repositories;

public interface IUrbetrackSynchronizationRepository : IRepository<UrbetrackSynchronization, int>
{
    Task<Dictionary<long, UrbetrackSynchronization>> GetByWeighingScaleIdsAsync(IEnumerable<long> weighingScaleIds, CancellationToken cancellationToken = default);
    Task<List<UrbetrackSynchronization>> GetByStatusAsync(SynchronizationStatus status, CancellationToken cancellationToken = default);
    Task BulkInsertAsync(List<UrbetrackSynchronization> synchronizations, CancellationToken cancellationToken = default);
    Task BulkUpdateAsync(List<UrbetrackSynchronization> synchronizations, CancellationToken cancellationToken = default);    
    Task<List<UrbetrackSynchronization>> AcquireLockBatchAsync(SynchronizationStatus status, int batchSize, string lockIdentifier, CancellationToken cancellationToken = default);
    Task ReleaseLockBatchAsync(IEnumerable<UrbetrackSynchronization> entries, CancellationToken cancellationToken = default);
    Task<int> CleanupStaleLocks(TimeSpan lockTimeout, CancellationToken cancellationToken = default);
}
