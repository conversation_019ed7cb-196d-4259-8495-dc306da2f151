using System.Collections.Immutable;
using System.Linq.Expressions;
using Orion.SharedKernel.Domain.Entities;
using Orion.SharedKernel.Domain.Repositories.Entities;
using Reports.Domain.Entities;
using Reports.Domain.Common;
using Reports.Domain.ValueObjects;

namespace Reports.Domain.Repositories;

public interface IWeighingScaleRepository : IRepository<WeighingScale, long>
{
    Task BulkInsertAsync(List<WeighingScale> weighins, CancellationToken cancellationToken);
    
    Task BulkUpdateAsync(List<WeighingScale> weighins, CancellationToken cancellationToken);
    
    Task<PaginatedResult<WeighingScale>> GetFilteredReportAsync(Expression<Func<WeighingScale, bool>> predicate,
        bool isPaginated,
        SortingOptions sortingOptions,
        CancellationToken cancellationToken,
        int pageNumber = 1,
        int pageSize = 10);
}