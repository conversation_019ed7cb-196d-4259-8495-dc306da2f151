using Orion.SharedKernel.Domain.Repositories.Entities;
using Orion.SharedKernel.Domain.Services;
using Reports.Domain.Repositories;

namespace Reports.Domain;

public interface IReportsUnitOfWork : IUnitOfWork
{
    IWeighingScaleRepository WeighingScales { get; }
    IVehicleRetrievalRepository VehicleRetrievals { get; }
    ITownRepository Towns { get; }
    ITollRepository Tolls { get; }
    IClientRepository Clients { get; }
    IMicrorouteRepository Microroutes { get; }
    IReportFormat14Repository ReportsFormat14 { get; }
    IReportFormat34Repository ReportsFormat34 { get; }
    IRecyclingAreaRepository RecyclingAreaRepository { get; }
    IDistributionRepository DistributionRepository { get; }
    ICollectionByMicrorouteRepository CollectionByMicrorouteRepository { get; }
    IHistoricalAuditRepository HistoricalAudits { get; }

    IUrbetrackSynchronizationRepository UrbetrackSynchronizations { get; }
    IScheduledTaskParameterRepository ScheduledTaskParameters { get; }

    IErrorService ErrorService { get; }
    ILogEventMessage LogEventMessage { get; }
}