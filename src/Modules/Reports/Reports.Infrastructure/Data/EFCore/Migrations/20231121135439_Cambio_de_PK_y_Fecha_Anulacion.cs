using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Reports.Infrastructure.Data.EFCore.Migrations
{
    public partial class Cambio_de_PK_y_Fecha_Anulacion : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "REPEM03_Id",
                table: "Reporting-Emvarias_03-<PERSON><PERSON><PERSON><PERSON>_<PERSON>_Balanza",
                type: "character varying(10)",
                maxLength: 10,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "text");

            migrationBuilder.AddColumn<DateTime>(
                name: "REPEM03_Fecha_de_cancelacion",
                table: "Reporting-Emvarias_03-<PERSON><PERSON><PERSON><PERSON>_<PERSON>_Balan<PERSON>",
                type: "timestamp without time zone",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "REPEM03_Fecha_de_cancelacion",
                table: "Reporting-Emvarias_03-<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>lanza");

            migrationBuilder.AlterColumn<string>(
                name: "REPEM03_Id",
                table: "Reporting-Emvarias_03-<PERSON><PERSON><PERSON><PERSON>_de_<PERSON>za",
                type: "text",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(10)",
                oldMaxLength: 10);
        }
    }
}
