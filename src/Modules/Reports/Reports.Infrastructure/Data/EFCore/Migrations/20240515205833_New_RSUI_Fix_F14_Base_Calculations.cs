using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Reports.Infrastructure.Data.EFCore.Migrations
{
    public partial class New_RSUI_Fix_F14_Base_Calculations : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"
                DROP VIEW IF EXISTS ""REPEM_Reporte_SUI_Recolecciones_F34"";
                DROP VIEW IF EXISTS ""REPEM_Toneladas_Rechazos_Agrupadas_Por_Ticket"" CASCADE;
                DROP VIEW IF EXISTS ""REPEM_Reporte_SUI_Recolecciones_F14_con_Aditivos"";
                DROP VIEW IF EXISTS ""REPEM_Reporte_SUI_Recolecciones_F14"";
                DROP VIEW IF EXISTS ""REPEM_Toneladas_Rechazos_Por_Ticket"";
                DROP VIEW IF EXISTS ""REPEM_Descuento_Pesaje_Excepcional_Por_Ticket"";
                DROP VIEW IF EXISTS ""REPEM_Tickets_Compensables"";
                DROP VIEW IF EXISTS ""REPEM_Detalle_Compensacion_Tickets"" CASCADE;
                DROP VIEW IF EXISTS ""REPEM_Dependencia_Toneladas_A_Compensar"";
                DROP VIEW IF EXISTS ""REPEM_Distribuciones_de_Microrutas"";
                DROP VIEW IF EXISTS ""REPEM_Sumatoria_Pesos_Rutas_Compartidas"";
                DROP VIEW IF EXISTS ""REPEM_Toneladas_Descuento_Excepcional"";
                DROP VIEW IF EXISTS ""REPEM_Descuento_Por_Ticket"";
                DROP FUNCTION IF EXISTS coalesce_non_negative(NUMERIC, NUMERIC);
            ");
            
            //1. Funcion de Utilidad para retornar numeros no negativos
            migrationBuilder.Sql(@"
                CREATE OR REPLACE FUNCTION coalesce_non_negative(value NUMERIC, fallback_value NUMERIC)
                    RETURNS NUMERIC AS
                $$
                BEGIN
                    IF value < 0 THEN
                        RETURN fallback_value;
                    ELSE
                        RETURN value;
                    END IF;
                END;
                $$ LANGUAGE plpgsql;
            ");

            //2. Vista de Toneladas con Descuento Excepcional
            migrationBuilder.Sql(@"
                create or replace view ""REPEM_Toneladas_Descuento_Excepcional"" as
                    SELECT a.""Año"",
                           a.""Mes"",
                           f.""Rut_LMV"",
                           f.""Rut_MJS"",
                           f.""Nro_LMV"",
                           f.""Nro_MJS"",
                           f.""Nro_Domingo"",
                           f.""Total_Dias"",
                           f.""Rut_LMV"" * f.""Nro_LMV""                                             as ""Klmts_LMV"",
                           round((f.""Rut_MJS"" * f.""Nro_MJS"")::numeric, 3)                        as ""Klmts_MJS"",
                           f.""Rut_LMV"" * f.""Nro_LMV"" + f.""Rut_MJS"" * f.""Nro_MJS""                 as ""Klmts_Total"",
                           40::numeric                                                           as ""Densidad"",
                           (f.""Rut_LMV"" * f.""Nro_LMV"" + f.""Rut_MJS"" * f.""Nro_MJS"") * 40::numeric as ""Kilos_Total_Descuento"",
                           round(((f.""Rut_LMV"" * f.""Nro_LMV"" + f.""Rut_MJS"" * f.""Nro_MJS"") * 40)::numeric / 1000,
                                 3)                                                              as ""Toneladas_Total_Descuento""
                    FROM (SELECT extract(year from ""REPEM04_FechaHora_Pesaje"")::int  as ""Año"",
                                 extract(month from ""REPEM04_FechaHora_Pesaje"")::int as ""Mes""
                          FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta""
                          GROUP BY extract(year from ""REPEM04_FechaHora_Pesaje""), extract(month from ""REPEM04_FechaHora_Pesaje"")) a
                             LEFT JOIN LATERAL (
                        SELECT 4.00::double precision                                               AS ""Rut_LMV"",
                               4.02::double precision                                               AS ""Rut_MJS"",
                               SUM(CASE WHEN EXTRACT(DOW FROM date) IN (0, 2, 4) THEN 1 ELSE 0 END) AS ""Nro_LMV"",
                               SUM(CASE WHEN EXTRACT(DOW FROM date) IN (1, 3, 5) THEN 1 ELSE 0 END) AS ""Nro_MJS"",
                               SUM(CASE WHEN EXTRACT(DOW FROM date) IN (6) THEN 1 ELSE 0 END)       AS ""Nro_Domingo"",
                               COUNT(*)                                                             AS ""Total_Dias""
                        FROM (SELECT date::date
                              FROM generate_series(
                                           date_trunc('month', pg_catalog.make_date(a.""Año"", a.""Mes"", 1)), -- Fecha de pesaje, truncada al primer día del mes
                                           date_trunc('month', pg_catalog.make_date(a.""Año"", a.""Mes"", 1)) +
                                           interval '1 month - 1 day', -- Fecha de pesaje, truncada al último día del mes
                                           '1 day' -- Intervalo de un día para contar todos los días del mes
                                   ) date) AS final_subquery
                        ) f ON TRUE;
            ");
            
            //3. Vista de Sumatoria de Pesos de Rutas Compartidas
            migrationBuilder.Sql(@"
                create or replace view ""REPEM_Sumatoria_Pesos_Rutas_Compartidas"" as
                SELECT ""Año"",
                       ""Mes"",
                       SUM(""PesoTotal"")        as ""Suma_Pesos_M3"",
                       SUM(""PesoTotal"") / 1000 as ""Suma_Pesos_Toneladas""
                FROM (SELECT rpm.""REPEM04_Id""                                   as ""IdTicket"",
                             extract(year from rpm.""REPEM04_FechaHora_Pesaje"")  as ""Año"",
                             extract(month from rpm.""REPEM04_FechaHora_Pesaje"") as ""Mes"",
                             rpm.""REPEM04_PesoTotal""                            as ""PesoTotal""
                      FROM ""Reporting-Emvarias_07-Microrutas"" mr
                               join ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                                    on ""REPEM07_Numero_de_Microruta""::text = ""REPEM04_RutaCodigo""
                      where mr.""REPEM07_NUAP"" <> 440405001
                        and mr.""REPEM07_Ruta_Larga"" <> '0614001'
                      group by rpm.""REPEM04_Id"", extract(year from rpm.""REPEM04_FechaHora_Pesaje""),
                               extract(month from rpm.""REPEM04_FechaHora_Pesaje"")) subquery
                GROUP BY ""Año"", ""Mes""
                ORDER BY ""Año"", ""Mes"";
            ");
            
            //4. Vista de Distribuciones de Microrutas
            migrationBuilder.Sql(@"
                create or replace view ""REPEM_Distribuciones_de_Microrutas"" as
                SELECT rv.""REPEM05_Año""                           AS ""Año"",
                       rv.""REPEM05_Mes""                           AS ""Mes"",
                       ap.""REPEM02_Codigo""                        AS ""NUAP"",
                       ap.""REPEM02_Nombre""                        AS ""Area_Aprovechamiento"",
                       count(*)                                   AS ""Cantidad_de_Viajes"",
                       round(rv.""REPEM05_Toneladas""::numeric, 3)  AS ""Toneladas_Recogidas"",
                       round(rv.""REPEM05_Toneladas"" / count(*)::numeric, 3) AS ""Toneladas_Distribuidas"",
                       round(
                               round(rv.""REPEM05_Toneladas""::numeric, 3) /
                               src.""Suma_Pesos_Toneladas""::numeric, 4) * 100
                                                                  AS ""Porcentaje_Distribucion_Peaje""
                FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm2
                         JOIN ""Reporting-Emvarias_07-Microrutas"" mic2
                              ON rpm2.""REPEM04_RutaCodigo""::text = mic2.""REPEM07_Numero_de_Microruta""::character varying(16)::text
                         JOIN ""Reporting-Emvarias_05-Recoleccion_Vehicular"" rv
                              ON rv.""REPEM05_Año""::numeric = EXTRACT(year FROM rpm2.""REPEM04_FechaHora_Pesaje"") AND
                                 rv.""REPEM05_Mes""::numeric = EXTRACT(month FROM rpm2.""REPEM04_FechaHora_Pesaje"") AND
                                 rv.""REPEM05_NUAP"" = mic2.""REPEM07_NUAP""
                         JOIN ""Reporting-Emvarias_02-Areas_de_Aprovechamiento"" ap ON ap.""REPEM02_Codigo"" = mic2.""REPEM07_NUAP""
                         JOIN ""REPEM_Sumatoria_Pesos_Rutas_Compartidas"" src
                              ON src.""Año""::numeric = EXTRACT(year FROM rpm2.""REPEM04_FechaHora_Pesaje"") AND
                                 src.""Mes""::numeric = EXTRACT(month FROM rpm2.""REPEM04_FechaHora_Pesaje"")
                WHERE mic2.""REPEM07_Porcentaje_No_Aforado"" = 0::double precision
                  AND ap.""REPEM02_Codigo"" <> 440405001
                  AND NOT (mic2.""REPEM07_NUAP"" = 441005380 AND mic2.""REPEM07_Ruta_Larga"" = '0911504')
                GROUP BY rv.""REPEM05_Toneladas"", src.""Suma_Pesos_Toneladas"", ap.""REPEM02_Nombre"", rv.""REPEM05_Año"",
                         rv.""REPEM05_Mes"", ap.""REPEM02_Codigo""
                ORDER BY rv.""REPEM05_Año"", rv.""REPEM05_Mes"", ap.""REPEM02_Nombre"";
            ");
            
            //5. Vista de dependencia para calculo de detalle de compensación de tickets
            migrationBuilder.Sql(@"
                create or replace view ""REPEM_Dependencia_Toneladas_A_Compensar"" as
                    SELECT rpm.""REPEM04_Id""                                                                             as ""Id_Ticket"",
                                                     mic.""REPEM07_NUAP""                                                                           as ""NUAP"",
                                                     mic.""REPEM07_Numero_de_Microruta""                                                            as ""Numero_de_Microruta"",
                                                     mic.""REPEM07_Numero_Ruta_Intendencia""                                                        as ""Numero_Ruta_Intendencia"",
                                                     rpm.""REPEM04_Patente""                                                                        as ""Patente"",
                                                     dpm.""Toneladas_Distribuidas""                                                                 as ""Toneladas_Distribuidas"",
                                                     rpm.""REPEM04_PesoTotal_Toneladas""                                                            as ""PesoTotal_Toneladas"",
                                                     mic.""REPEM07_Porcentaje_No_Aforado""                                                          as ""Porcentaje_No_Aforado"",
                                                     mic.""REPEM07_Porcentaje_Limpieza_Urbana""                                                     as ""Porcentaje_Limpieza_Urbana"",
                                                     mic.""REPEM07_Porcentaje_Barrido""                                                             as ""Porcentaje_Barrido"",
                                                     mic.""REPEM07_Porcentaje_Residuos_Aprovechables""                                              as ""Porcentaje_Residuos_Aprovechables"",
                                                     rpm.""REPEM04_FechaHora_Pesaje""                                                               as ""Fecha_Hora_Pesaje"",
                                                     SUM(dpm.""Toneladas_Distribuidas"")
                                                     OVER (PARTITION BY rpm.""REPEM04_Id""
                                                         order by mic.""REPEM07_NUAP""
                                                         RANGE BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)::numeric                     AS ""Sumatoria_Acumulada""
                                              FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                                                       INNER JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                                                                  ON rpm.""REPEM04_RutaCodigo"" = mic.""REPEM07_Numero_de_Microruta""::text
                                                       INNER JOIN ""Reporting-Emvarias_02-Areas_de_Aprovechamiento"" ap
                                                                  ON ap.""REPEM02_Codigo"" = mic.""REPEM07_NUAP""
                                                       JOIN ""REPEM_Distribuciones_de_Microrutas"" dpm
                                                            ON dpm.""NUAP"" = mic.""REPEM07_NUAP""
                                                                AND dpm.""Año"" = extract(year from rpm.""REPEM04_FechaHora_Pesaje"")
                                                                AND dpm.""Mes"" = extract(month from rpm.""REPEM04_FechaHora_Pesaje"");
            ");

            //6. Vista de detalle de compensación de tickets
            migrationBuilder.Sql(@"
                create or replace view ""REPEM_Detalle_Compensacion_Tickets"" as
                    with cte AS (
                        select *,
                           CASE WHEN ""Sumatoria_Acumulada"" < ""PesoTotal_Toneladas""
                               THEN 0
                               ELSE round(""Sumatoria_Acumulada"" - ""PesoTotal_Toneladas""::numeric, 3)
                           END as ""Exceso_Acumulado""
                           from ""REPEM_Dependencia_Toneladas_A_Compensar"" dtc
                    ), cnt AS (
                        select
                            cte.""Id_Ticket"",
                            count(cte.""Id_Ticket"") as ""Cantidad""
                        from cte
                        group by cte.""Id_Ticket""
                    ), rst AS (
                        select distinct on (cte.""Id_Ticket"")
                            cte.""Id_Ticket"",
                            round((cte.""PesoTotal_Toneladas"" - MAX(cte.""Sumatoria_Acumulada"") OVER (PARTITION BY cte.""Id_Ticket""))::numeric, 3) as ""Resto_Disponible""
                        from cte
                        inner join cnt on cnt.""Id_Ticket"" = cte.""Id_Ticket""
                        where ""Exceso_Acumulado"" = 0 and cnt.""Cantidad"" > 1
                        order by cte.""Id_Ticket"", cte.""Toneladas_Distribuidas"" desc
                    ), sprst AS (
                        select distinct on (cte.""Id_Ticket"")
                            cte.""Id_Ticket"",
                            round(cte.""Toneladas_Distribuidas"" - cte.""PesoTotal_Toneladas""::numeric, 3) as ""Resto_Disponible""
                        from cte
                        inner join cnt on cnt.""Id_Ticket"" = cte.""Id_Ticket""
                        where ""Exceso_Acumulado"" > 0 and cnt.""Cantidad"" = 1
                        order by cte.""Id_Ticket"", cte.""Toneladas_Distribuidas"" desc
                    ), pcomp AS (
                        SELECT
                           cte.*,
                           cte.""Toneladas_Distribuidas"" - rst.""Resto_Disponible"" as ""Toneladas_Resultantes""
                        FROM cte
                        INNER JOIN (
                            SELECT icte.""Id_Ticket"", MAX(icte.""Toneladas_Distribuidas"") AS max_toneladas
                            FROM cte icte
                            GROUP BY icte.""Id_Ticket""
                        ) sub ON cte.""Id_Ticket"" = sub.""Id_Ticket"" AND cte.""Toneladas_Distribuidas"" = sub.max_toneladas
                        inner join rst on rst.""Id_Ticket"" = cte.""Id_Ticket""
                        where cte.""Exceso_Acumulado"" > 0
                        UNION ALL
                        SELECT
                            cte.*,
                            sprst.""Resto_Disponible"" as ""Toneladas_Resultantes""
                            FROM cte
                            inner join sprst on sprst.""Id_Ticket"" = cte.""Id_Ticket""
                    )
                    select
                        cte.""Id_Ticket"",
                        cte.""NUAP"",
                        cte.""Fecha_Hora_Pesaje"",
                        cte.""Toneladas_Distribuidas"" - coalesce(pcomp.""Toneladas_Resultantes"", 0) as ""Toneladas_Resultantes"",
                        CASE WHEN cte.""Exceso_Acumulado"" = 0 or pcomp is not null THEN 'ORIGINAL' ELSE 'TOTAL' END as ""Tipo_Compensacion""
                    from cte
                    left join pcomp on pcomp.""Id_Ticket"" = cte.""Id_Ticket"" and cte.""NUAP"" = pcomp.""NUAP""
                    union all
                    select
                        pcomp.""Id_Ticket"",
                        pcomp.""NUAP"",
                        pcomp.""Fecha_Hora_Pesaje"",
                        pcomp.""Toneladas_Resultantes"",
                        'PARCIAL' as ""Tipo""
                    from pcomp;
            ");
            
            //7. Vista de tickets compensables
            migrationBuilder.Sql(@"
                create or replace view ""REPEM_Tickets_Compensables"" AS
                    WITH cte AS (
                    SELECT
                           td.""Id_Ticket"",
                           td.""Fecha_Hora_Pesaje""::date AS ""Fecha_Pesaje"",
                           SUM(td.""Toneladas_Resultantes"") AS ""Suma_Toneladas_Agrupadas""
                        FROM ""REPEM_Detalle_Compensacion_Tickets"" td
                        WHERE td.""Tipo_Compensacion"" = 'PARCIAL' or td.""Tipo_Compensacion"" = 'TOTAL'
                        group by td.""Fecha_Hora_Pesaje""::date, td.""Id_Ticket""
                    )
                    SELECT cte.""Id_Ticket"",
                           cte.""Suma_Toneladas_Agrupadas"" as ""Suma_Agrupacion_Toneladas_Por_Compensar"",
                           rpm.""REPEM04_Id"" as ""Nro_Ticket_Compensacion"",
                           rpm.""REPEM04_PesoTotal_Toneladas"" as ""Maximo_Toneladas_Compensables""
                    FROM cte
                    LEFT JOIN ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm on
                        rpm.""REPEM04_Id"" = (
                            SELECT
                                rpm2.""REPEM04_Id""
                            FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm2
                            WHERE rpm2.""REPEM04_FechaHora_Pesaje""::date = cte.""Fecha_Pesaje""
                            and rpm2.""REPEM04_RutaCodigo"" IN ('615618', '618219', '618119', '618319')
                            and rpm2.""REPEM04_PesoTotal_Toneladas"" >= cte.""Suma_Toneladas_Agrupadas""
                            ORDER BY rpm2.""REPEM04_PesoTotal_Toneladas"" DESC
                            LIMIT 1
                        );
            ");
            
            //8. Vista de Descuento de Pesaje por Ticket
            migrationBuilder.Sql(@"create or replace view ""REPEM_Descuento_Por_Ticket"" as
                SELECT rpm.""REPEM04_Id""          AS ""Id_Ticket"",
                       sum(
                               CASE
                                   WHEN mic.""REPEM07_NUAP"" = 440405001 THEN 0::double precision
                                   WHEN mic.""REPEM07_NUAP"" = 440705360 AND
                                        mic.""REPEM07_Porcentaje_No_Aforado"" <> 0::double precision THEN
                                       mic.""REPEM07_Porcentaje_No_Aforado"" / 100::double precision *
                                       rpm.""REPEM04_PesoTotal_Toneladas""
                                   ELSE td.""Toneladas_Resultantes""::double precision
                                   END)::numeric AS ""Toneladas_Descuento_Medellin"",
                       sum(
                               CASE
                                   WHEN mic.""REPEM07_NUAP"" = 440405001 THEN 0::double precision
                                   WHEN mic.""REPEM07_NUAP"" = 440705360 AND
                                        mic.""REPEM07_Porcentaje_No_Aforado"" <> 0::double precision THEN
                                       mic.""REPEM07_Porcentaje_No_Aforado"" / 100::double precision
                                           * 2
                                           * pe.""REPEM06_Valor""
                                   ELSE (pe.""REPEM06_Valor""
                                       * 2
                                       * (dpm.""Porcentaje_Distribucion_Peaje"" / 100)
                                       )::double precision
                                   END)::numeric AS ""Peaje_Descuento_Medellin""
                FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                         JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                              ON rpm.""REPEM04_RutaCodigo""::text = mic.""REPEM07_Numero_de_Microruta""::text
                         JOIN ""REPEM_Distribuciones_de_Microrutas"" dpm
                              ON dpm.""NUAP"" = mic.""REPEM07_NUAP"" AND
                                 dpm.""Año""::numeric = EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"") AND
                                 dpm.""Mes""::numeric = EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje"")
                         JOIN ""REPEM_Detalle_Compensacion_Tickets"" td
                              ON td.""Id_Ticket"" = rpm.""REPEM04_Id"" AND
                                 td.""NUAP"" = mic.""REPEM07_NUAP"" AND
                                 td.""Tipo_Compensacion"" = 'ORIGINAL'
                         JOIN ""Reporting-Emvarias_06-Peajes"" pe ON pe.""REPEM06_Placa"" = rpm.""REPEM04_Patente"" AND
                                                                   pe.""REPEM06_Fecha_Validez"" = (SELECT p.""REPEM06_Fecha_Validez""
                                                                                                 FROM ""Reporting-Emvarias_06-Peajes"" p
                                                                                                 WHERE p.""REPEM06_Placa"" = rpm.""REPEM04_Patente""
                                                                                                 ORDER BY abs(EXTRACT(epoch FROM
                                                                                                                      rpm.""REPEM04_FechaHora_Pesaje"" -
                                                                                                                      p.""REPEM06_Fecha_Validez""))
                                                                                                 LIMIT 1) -- El valor del pesaje mas cercano a la fecha de pesaje, por fecha de validez/vigencia desde.
                WHERE mic.""REPEM07_NUAP"" <> '440405001'::bigint
                GROUP BY rpm.""REPEM04_Id"";
            ");

            //9. Vista de Toneladas de Rechazos Agrupadas por Ticket
            migrationBuilder.Sql(@"
                create or replace view ""REPEM_Toneladas_Rechazos_Agrupadas_Por_Ticket"" as
                select
                    rpm.""REPEM04_Id"" as ""Ticket_Asignable"",
                    round(sum(r.""REPEM09_Toneladas"")::numeric, 6) as ""Toneladas_Rechazadas"",
                    count(r.""REPEM09_Id"") as ""Cantidad_Rechazos"",
                    MAX(r.""REPEM09_Fecha_Corta"") as ""Fecha_Rechazo""
                from ""Reporting-Emvarias_09-Rechazos"" r
                left join LATERAL (
                        select * from ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                        join ""Reporting-Emvarias_07-Microrutas"" mic
                        on rpm.""REPEM04_RutaCodigo"" = mic.""REPEM07_Numero_de_Microruta""::text
                        where r.""REPEM09_Placa"" = rpm.""REPEM04_Patente"" and
                              r.""REPEM09_Fecha_Corta"" = rpm.""REPEM04_FechaHora_Pesaje""::date and
                              mic.""REPEM07_Ruta_Larga"" = r.""REPEM09_Ruta_Larga""
                        limit 1
                    ) rpm on true
                group by rpm.""REPEM04_Id"";
            ");
            
            //10. Vista de Toneladas descuento de Pesaje excepcional por Ticket
            migrationBuilder.Sql(@"
                create or replace view ""REPEM_Descuento_Pesaje_Excepcional_Por_Ticket"" as
                    select MAX(rpm.""REPEM04_Id"")                              as ""NroTicket"",
                           440705360                                          as ""NUAP"",
                           614001                                             as ""RutaCodigo"",
                           tde.""Toneladas_Total_Descuento""                    as ""Toneladas_Descuento"",
                           extract(year from rpm.""REPEM04_FechaHora_Pesaje"")  as ""Año"",
                           extract(month from rpm.""REPEM04_FechaHora_Pesaje"") as ""Mes""
                    from ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                             join ""Reporting-Emvarias_07-Microrutas"" mic
                                  on rpm.""REPEM04_RutaCodigo""::bigint = mic.""REPEM07_Numero_de_Microruta""
                             join ""REPEM_Toneladas_Descuento_Excepcional"" tde
                                  on extract(year from rpm.""REPEM04_FechaHora_Pesaje"") = tde.""Año"" and
                                     extract(month from rpm.""REPEM04_FechaHora_Pesaje"") = tde.""Mes""
                             LEFT JOIN ""REPEM_Distribuciones_de_Microrutas"" dpm
                                       ON dpm.""NUAP"" = mic.""REPEM07_NUAP""
                                           AND dpm.""Año"" = extract(year from rpm.""REPEM04_FechaHora_Pesaje"")
                                           AND dpm.""Mes"" = extract(month from rpm.""REPEM04_FechaHora_Pesaje"")
                             LEFT JOIN ""REPEM_Descuento_Por_Ticket"" tr
                                       ON tr.""Id_Ticket"" = rpm.""REPEM04_Id""
                    where rpm.""REPEM04_RutaCodigo"" NOT IN ('N9911111', 'N0000133', 'T9911111', 'T0000133', '3RECDON0312201F2')
                      and mic.""REPEM07_Numero_de_Microruta"" = 614001
                      and mic.""REPEM07_NUAP"" = 440705360
                      and (CASE
                               WHEN mic.""REPEM07_NUAP"" = 440705360 AND mic.""REPEM07_Porcentaje_No_Aforado"" <> 0
                                   THEN tr.""Toneladas_Descuento_Medellin""
                               ELSE round(dpm.""Toneladas_Distribuidas""::numeric, 3)
                               END - CASE
                                         WHEN mic.""REPEM07_Porcentaje_Barrido"" <> 0
                                             THEN round(((CASE
                                                              WHEN mic.""REPEM07_NUAP"" = 440405001
                                                                  THEN round(
                                                                      (rpm.""REPEM04_PesoTotal_Toneladas"" -
                                                                       coalesce(tr.""Toneladas_Descuento_Medellin"", 0))::numeric, 3)
                                                              WHEN mic.""REPEM07_NUAP"" = 440705360 AND
                                                                   mic.""REPEM07_Porcentaje_No_Aforado"" <> 0
                                                                  THEN tr.""Toneladas_Descuento_Medellin""
                                                              ELSE round(dpm.""Toneladas_Distribuidas""::numeric, 3)
                                             END) * (mic.""REPEM07_Porcentaje_Barrido"" / 100))::numeric, 3)
                                         ELSE 0
                               END) >= tde.""Toneladas_Total_Descuento""
                    group by extract(year from rpm.""REPEM04_FechaHora_Pesaje""),
                             extract(month from rpm.""REPEM04_FechaHora_Pesaje""),
                             tde.""Toneladas_Total_Descuento"";
            ");
            
            //11. Vista de Reporte 14
            migrationBuilder.Sql(@"
                create or replace view ""REPEM_Reporte_SUI_Recolecciones_F14"" AS
                                WITH cte AS (SELECT rpm.""REPEM04_Id""                                     AS ""Id_Ticket"",
                                    mic.""REPEM07_NUAP""                                   AS ""NUAP"",
                                    CASE
                                        WHEN mic.""REPEM07_NUAP"" = 440405001 THEN round((rpm.""REPEM04_PesoTotal_Toneladas"" -
                                                                                        COALESCE(tr.""Toneladas_Descuento_Medellin"", 0::numeric)::double precision)::numeric,
                                                                                       3)
                                        WHEN mic.""REPEM07_NUAP"" = 440705360 AND
                                             mic.""REPEM07_Porcentaje_No_Aforado"" <> 0::double precision
                                            THEN tr.""Toneladas_Descuento_Medellin""
                                        ELSE round(tdha.""Toneladas_Resultantes"", 3)
                                        END                                              AS ""Calculo_Toneladas"",
                                    CASE
                                        WHEN mic.""REPEM07_Porcentaje_Barrido"" <> 0::double precision THEN round((
                                                                                                                    CASE
                                                                                                                        WHEN mic.""REPEM07_NUAP"" = 440405001
                                                                                                                            THEN round(
                                                                                                                                (rpm.""REPEM04_PesoTotal_Toneladas"" -
                                                                                                                                 COALESCE(tr.""Toneladas_Descuento_Medellin"", 0::numeric)::double precision)::numeric,
                                                                                                                                3)
                                                                                                                        WHEN
                                                                                                                            mic.""REPEM07_NUAP"" =
                                                                                                                            440705360 AND
                                                                                                                            mic.""REPEM07_Porcentaje_No_Aforado"" <>
                                                                                                                            0::double precision
                                                                                                                            THEN tr.""Toneladas_Descuento_Medellin""
                                                                                                                        ELSE round(tdha.""Toneladas_Resultantes"", 3)
                                                                                                                        END::double precision *
                                                                                                                    (mic.""REPEM07_Porcentaje_Barrido"" / 100::double precision))::numeric,
                                                                                                                3)
                                        ELSE 0::numeric
                                        END                                              AS ""Calculo_Barrido"",
                                    CASE
                                        WHEN pe.* IS NULL THEN 0::numeric
                                        WHEN mic.""REPEM07_NUAP"" = 440405001 THEN round((pe.""REPEM06_Valor"" * 2::double precision -
                                                                                        COALESCE(tr.""Peaje_Descuento_Medellin"", 0::numeric)::double precision)::numeric,
                                                                                       0)
                                        WHEN mic.""REPEM07_NUAP"" = 440705360 AND
                                             mic.""REPEM07_Porcentaje_No_Aforado"" <> 0::double precision
                                            THEN round(tr.""Peaje_Descuento_Medellin"", 0)
                                        ELSE round((pe.""REPEM06_Valor"" * 2::double precision *
                                                    (COALESCE(dpm.""Porcentaje_Distribucion_Peaje"", 100::numeric) /
                                                     100::numeric)::double precision)::numeric, 0)
                                        END                                              AS ""Calculo_Peaje"",
                                    COALESCE(tdha.""Tipo_Compensacion"", 'ORIGINAL'::text) AS ""Tipo_Compensacion""
                             FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                                      JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                                           ON rpm.""REPEM04_RutaCodigo""::text = mic.""REPEM07_Numero_de_Microruta""::text
                                      LEFT JOIN ""REPEM_Distribuciones_de_Microrutas"" dpm ON dpm.""NUAP"" = mic.""REPEM07_NUAP"" AND
                                                                                            dpm.""Año""::numeric =
                                                                                            EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"") AND
                                                                                            dpm.""Mes""::numeric =
                                                                                            EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje"")
                                      LEFT JOIN ""Reporting-Emvarias_06-Peajes"" pe ON pe.""REPEM06_Placa""::text = rpm.""REPEM04_Patente""::text
                                      LEFT JOIN ""REPEM_Descuento_Por_Ticket"" tr ON tr.""Id_Ticket"" = rpm.""REPEM04_Id""
                                      LEFT JOIN ""REPEM_Detalle_Compensacion_Tickets"" tdha
                                                ON tdha.""Id_Ticket"" = rpm.""REPEM04_Id"" AND tdha.""NUAP"" = mic.""REPEM07_NUAP"" AND
                                                   tdha.""Tipo_Compensacion"" = 'ORIGINAL'::text)
                SELECT mic.""REPEM07_NUAP""                                                     AS ""C1_NUAP"",
                       1                                                                      AS ""C2_TIPO_SITIO"",
                       720105237                                                              AS ""C3_NUSD"",
                       rpm.""REPEM04_Patente""                                                  AS ""C4_PLACA"",
                       rpm.""REPEM04_FechaHora_Pesaje""::date                                   AS ""C5_FECHA"",
                       rpm.""REPEM04_FechaHora_Pesaje""::time without time zone                 AS ""C6_HORA"",
                       mic.""REPEM07_Numero_Ruta_Intendencia""                                  AS ""C7_NUMICRO"",
                       0                                                                      AS ""C8_TON_LIMP_URB"",
                       bs.""Calculo_Barrido"" + COALESCE(tde.""Toneladas_Descuento"", 0::numeric) AS ""C9_TON_BARRIDO"",
                       bs.""Calculo_Toneladas"" - bs.""Calculo_Barrido"" - COALESCE(tde.""Toneladas_Descuento"", 0::numeric) -
                       COALESCE(rtc.""Suma_Agrupacion_Toneladas_Por_Compensar"", 0::numeric)    AS ""C10_TONRESNA"",
                       COALESCE(tre.""Toneladas_Rechazadas"", 0::numeric)                       AS ""C11_TONRECHAPR"",
                       CASE
                           WHEN mic.""REPEM07_Porcentaje_Residuos_Aprovechables"" <> 0::double precision THEN round(
                                   (bs.""Calculo_Toneladas""::double precision *
                                    (mic.""REPEM07_Porcentaje_Residuos_Aprovechables"" / 100::double precision))::numeric, 3)
                           ELSE 0::numeric
                           END                                                                AS ""C12_TONRESAPR"",
                       '1'::text                                                              AS ""C13_SISTEMA_MEDICION"",
                       bs.""Calculo_Peaje""                                                     AS ""C14_VLRPEAJ"",
                       rpm.""REPEM04_Id""                                                       AS ""CE_ID_TICKET"",
                       ap.""REPEM02_Nombre""                                                    AS ""CE_NOMBRE_AREA"",
                       mic.""REPEM07_Ruta_Larga""                                               AS ""CE_RUTA_LARGA"",
                       rpm.""REPEM04_PesoTotal_Toneladas""                                      AS ""CE_TON_TOTAL"",
                       rtc.* IS NOT NULL                                                      AS ""CE_REL_COMPENSACION"",
                       rtc.""Id_Ticket""                                                        AS ""CE_REL_COMPENSACION_ID_TICKET""
                FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                         JOIN cte bs ON bs.""Calculo_Toneladas"" IS NOT NULL AND rpm.""REPEM04_Id"" = bs.""Id_Ticket""
                         JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                              ON rpm.""REPEM04_RutaCodigo""::text = mic.""REPEM07_Numero_de_Microruta""::text AND
                                 mic.""REPEM07_NUAP"" = bs.""NUAP""
                         JOIN ""Reporting-Emvarias_02-Areas_de_Aprovechamiento"" ap ON ap.""REPEM02_Codigo"" = mic.""REPEM07_NUAP""
                         LEFT JOIN ""REPEM_Descuento_Pesaje_Excepcional_Por_Ticket"" tde ON rpm.""REPEM04_Id"" = tde.""NroTicket"" AND
                                                                                          EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"") =
                                                                                          tde.""Año"" AND
                                                                                          EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje"") =
                                                                                          tde.""Mes"" AND
                                                                                          mic.""REPEM07_Numero_de_Microruta"" =
                                                                                          tde.""RutaCodigo"" AND
                                                                                          mic.""REPEM07_NUAP"" = tde.""NUAP""
                         LEFT JOIN ""REPEM_Toneladas_Rechazos_Agrupadas_Por_Ticket"" tre
                                   ON rpm.""REPEM04_Id"" = tre.""Ticket_Asignable"" AND mic.""REPEM07_NUAP"" = 440405001
                         LEFT JOIN ""REPEM_Tickets_Compensables"" rtc ON rpm.""REPEM04_Id"" = rtc.""Nro_Ticket_Compensacion""
                UNION ALL
                (WITH cte_comp AS (SELECT rpm_1.""REPEM04_Id""                    AS ""Id_Ticket"",
                                          mic.""REPEM07_NUAP""                    AS ""NUAP"",
                                          mic.""REPEM07_Numero_Ruta_Intendencia"" AS ""Numero_Ruta_Indendencia"",
                                          mic.""REPEM07_Ruta_Larga""              AS ""Ruta_Larga"",
                                          ap.""REPEM02_Nombre""                   AS ""Area_Aprovechamiento"",
                                          CASE
                                              WHEN mic.""REPEM07_NUAP"" = 440405001 THEN round((rpm_1.""REPEM04_PesoTotal_Toneladas"" -
                                                                                              COALESCE(tr.""Toneladas_Descuento_Medellin"", 0::numeric)::double precision)::numeric,
                                                                                             3)
                                              WHEN mic.""REPEM07_NUAP"" = 440705360 AND
                                                   mic.""REPEM07_Porcentaje_No_Aforado"" <> 0::double precision
                                                  THEN tr.""Toneladas_Descuento_Medellin""
                                              ELSE round(tdha.""Toneladas_Resultantes"", 3)
                                              END                               AS ""Calculo_Toneladas"",
                                          CASE
                                              WHEN mic.""REPEM07_Porcentaje_Barrido"" <> 0::double precision THEN round((
                                                                                                                          CASE
                                                                                                                              WHEN mic.""REPEM07_NUAP"" = 440405001
                                                                                                                                  THEN round(
                                                                                                                                      (rpm_1.""REPEM04_PesoTotal_Toneladas"" -
                                                                                                                                       COALESCE(tr.""Toneladas_Descuento_Medellin"", 0::numeric)::double precision)::numeric,
                                                                                                                                      3)
                                                                                                                              WHEN
                                                                                                                                  mic.""REPEM07_NUAP"" =
                                                                                                                                  440705360 AND
                                                                                                                                  mic.""REPEM07_Porcentaje_No_Aforado"" <>
                                                                                                                                  0::double precision
                                                                                                                                  THEN tr.""Toneladas_Descuento_Medellin""
                                                                                                                              ELSE round(tdha.""Toneladas_Resultantes"", 3)
                                                                                                                              END::double precision *
                                                                                                                          (mic.""REPEM07_Porcentaje_Barrido"" / 100::double precision))::numeric,
                                                                                                                      3)
                                              ELSE 0::numeric
                                              END                               AS ""Calculo_Barrido"",
                                          0                                     AS ""Calculo_Peaje"",
                                          tdha.""Tipo_Compensacion""
                                   FROM ""REPEM_Detalle_Compensacion_Tickets"" tdha
                                            JOIN ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm_1
                                                 ON tdha.""Id_Ticket"" = rpm_1.""REPEM04_Id""
                                            JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                                                 ON rpm_1.""REPEM04_RutaCodigo""::text = mic.""REPEM07_Numero_de_Microruta""::text AND
                                                    mic.""REPEM07_NUAP"" = tdha.""NUAP""
                                            JOIN ""Reporting-Emvarias_02-Areas_de_Aprovechamiento"" ap
                                                 ON ap.""REPEM02_Codigo"" = mic.""REPEM07_NUAP""
                                            LEFT JOIN ""REPEM_Distribuciones_de_Microrutas"" dpm ON dpm.""NUAP"" = mic.""REPEM07_NUAP"" AND
                                                                                                  dpm.""Año""::numeric =
                                                                                                  EXTRACT(year FROM rpm_1.""REPEM04_FechaHora_Pesaje"") AND
                                                                                                  dpm.""Mes""::numeric =
                                                                                                  EXTRACT(month FROM rpm_1.""REPEM04_FechaHora_Pesaje"")
                                            LEFT JOIN ""Reporting-Emvarias_06-Peajes"" pe
                                                      ON pe.""REPEM06_Placa""::text = rpm_1.""REPEM04_Patente""::text
                                            LEFT JOIN ""REPEM_Descuento_Por_Ticket"" tr ON tr.""Id_Ticket"" = rpm_1.""REPEM04_Id""
                                   WHERE tdha.""Tipo_Compensacion"" = 'TOTAL'::text
                                      OR tdha.""Tipo_Compensacion"" = 'PARCIAL'::text)
                 SELECT bs.""NUAP""                                              AS ""C1_NUAP"",
                        1                                                      AS ""C2_TIPO_SITIO"",
                        720105237                                              AS ""C3_NUSD"",
                        rpm.""REPEM04_Patente""                                  AS ""C4_PLACA"",
                        rpm.""REPEM04_FechaHora_Pesaje""::date                   AS ""C5_FECHA"",
                        rpm.""REPEM04_FechaHora_Pesaje""::time without time zone AS ""C6_HORA"",
                        bs.""Numero_Ruta_Indendencia""                           AS ""C7_NUMICRO"",
                        0                                                      AS ""C8_TON_LIMP_URB"",
                        0                                                      AS ""C9_TON_BARRIDO"",
                        CASE
                            WHEN rtc.* IS NOT NULL THEN bs.""Calculo_Toneladas""::double precision
                            ELSE rpm.""REPEM04_PesoTotal_Toneladas""
                            END                                                AS ""C10_TONRESNA"",
                        COALESCE(tre.""Toneladas_Rechazadas"", 0::numeric)       AS ""C11_TONRECHAPR"",
                        CASE
                            WHEN mr.""REPEM07_Porcentaje_Residuos_Aprovechables"" <> 0::double precision THEN round(
                                    (bs.""Calculo_Toneladas""::double precision *
                                     (mr.""REPEM07_Porcentaje_Residuos_Aprovechables"" / 100::double precision))::numeric, 3)
                            ELSE 0::numeric
                            END                                                AS ""C12_TONRESAPR"",
                        '1'::text                                              AS ""C13_SISTEMA_MEDICION"",
                        bs.""Calculo_Peaje""                                     AS ""C14_VLRPEAJ"",
                        rtc.""Nro_Ticket_Compensacion""                          AS ""CE_ID_TICKET"",
                        bs.""Area_Aprovechamiento""                              AS ""CE_NOMBRE_AREA"",
                        bs.""Ruta_Larga""                                        AS ""CE_RUTA_LARGA"",
                        rtc.""Maximo_Toneladas_Compensables""                    AS ""CE_TON_TOTAL"",
                        rtc.* IS NOT NULL                                      AS ""CE_REL_COMPENSACION"",
                        CASE
                            WHEN rtc.* IS NOT NULL THEN rtc.""Id_Ticket""
                            ELSE bs.""Id_Ticket""
                            END                                                AS ""CE_REL_COMPENSACION_ID_TICKET""
                 FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                          JOIN cte_comp bs ON rpm.""REPEM04_Id"" = bs.""Id_Ticket""
                          JOIN ""Reporting-Emvarias_07-Microrutas"" mr
                               ON mr.""REPEM07_Ruta_Larga"" = bs.""Ruta_Larga"" AND mr.""REPEM07_NUAP"" = bs.""NUAP""
                          JOIN ""REPEM_Tickets_Compensables"" rtc ON rpm.""REPEM04_Id"" = rtc.""Id_Ticket""
                          LEFT JOIN ""REPEM_Toneladas_Rechazos_Agrupadas_Por_Ticket"" tre
                                    ON rpm.""REPEM04_Id"" = tre.""Ticket_Asignable"" AND mr.""REPEM07_NUAP"" = 440405001);
            ");
            
            //12. Vista de Reporte 14 con Aditivos
            migrationBuilder.Sql(@"
                create or replace view ""REPEM_Reporte_SUI_Recolecciones_F14_con_Aditivos"" AS
                    SELECT
                        f14.""C1_NUAP"",
                        f14.""C2_TIPO_SITIO"",
                        f14.""C3_NUSD""::varchar(10) as ""C3_NUSD"",
                        f14.""C4_PLACA"",
                        f14.""C5_FECHA"",
                        f14.""C6_HORA"",
                        f14.""C7_NUMICRO"",
                        f14.""C8_TON_LIMP_URB"",
                        f14.""C9_TON_BARRIDO"",
                        f14.""C10_TONRESNA"",
                        f14.""C11_TONRECHAPR"",
                        f14.""C12_TONRESAPR"",
                        f14.""C13_SISTEMA_MEDICION""::integer as ""C13_SISTEMA_MEDICION"",
                        f14.""C14_VLRPEAJ"",
                        f14.""CE_ID_TICKET"",
                        f14.""CE_NOMBRE_AREA"",
                        f14.""CE_RUTA_LARGA"",
                        f14.""CE_TON_TOTAL"",
                        f14.""CE_REL_COMPENSACION"",
                        f14.""CE_REL_COMPENSACION_ID_TICKET""
                    from ""REPEM_Reporte_SUI_Recolecciones_F14"" f14
                    UNION ALL
                    SELECT
                           clu.""C1_NUAP"" as ""C1_NUAP"",
                           clu.""C2_TIPO_SITIO"" as ""C2_TIPO_SITIO"",
                           clu.""C3_NUSD""::varchar(10) as ""C3_NUSD"",
                           clu.""C4_PLACA"" as ""C4_PLACA"",
                           clu.""C5_FECHA"" as ""C5_FECHA"",
                           clu.""C6_HORA"" as ""C6_HORA"",
                           clu.""C7_NUMICRO"" as ""C7_NUMICRO"",
                           clu.""C8_TON_LIMP_URB"" as ""C8_TON_LIMP_URB"",
                           clu.""C9_TON_BARRIDO"" as ""C9_TON_BARRIDO"",
                           clu.""C10_TONRESNA"" as ""C10_TONRESNA"",
                           clu.""C11_TONRECHAPR"" as ""C11_TONRECHAPR"",
                           clu.""C12_TONRESAPR"" as ""C12_TONRESAPR"",
                           clu.""C13_SISTEMA_MEDICION""::integer as ""C13_SISTEMA_MEDICION"",
                           clu.""C14_VLRPEAJ"" as ""C14_VLRPEAJ"",
                           null as ""CE_ID_TICKET"",
                           'CLUS - CARGADO' as ""CE_NOMBRE_AREA"",
                           clu.""CE_RUTA_LARGA"" as ""CE_RUTA_LARGA"",
                           clu.""CE_TON_TOTAL"" as ""CE_TON_TOTAL"",
                           null as ""CE_REL_COMPENSACION"",
                           null as ""CE_REL_COMPENSACION_ID_TICKET""
                    from ""Reporting-Emvarias_11-Reporte_F14_Adiciones_CLU"" clu
                    UNION ALL
                    SELECT
                           apr.""C1_NUAP"" as ""C1_NUAP"",
                           apr.""C2_TIPO_SITIO"" as ""C2_TIPO_SITIO"",
                           apr.""C3_NUSD""::varchar(10) as ""C3_NUSD"",
                           apr.""C4_PLACA"" as ""C4_PLACA"",
                           apr.""C5_FECHA"" as ""C5_FECHA"",
                           apr.""C6_HORA"" as ""C6_HORA"",
                           apr.""C7_NUMICRO"" as ""C7_NUMICRO"",
                           apr.""C8_TON_LIMP_URB"" as ""C8_TON_LIMP_URB"",
                           apr.""C9_TON_BARRIDO"" as ""C9_TON_BARRIDO"",
                           apr.""C10_TONRESNA"" as ""C10_TONRESNA"",
                           apr.""C11_TONRECHAPR"" as ""C11_TONRECHAPR"",
                           apr.""C12_TONRESAPR"" as ""C12_TONRESAPR"",
                           apr.""C13_SISTEMA_MEDICION""::integer as ""C13_SISTEMA_MEDICION"",
                           apr.""C14_VLRPEAJ"" as ""C14_VLRPEAJ"",
                           null as ""CE_ID_TICKET"",
                           'APROVECHAMIENTO - CARGADO' as ""CE_NOMBRE_AREA"",
                           apr.""CE_RUTA_LARGA"" as ""CE_RUTA_LARGA"",
                           apr.""CE_TON_TOTAL"" as ""CE_TON_TOTAL"",
                           null as ""CE_REL_COMPENSACION"",
                           null as ""CE_REL_COMPENSACION_ID_TICKET""
                    from ""Reporting-Emvarias_12-Reporte_F14_Adiciones_Aprovechamiento"" apr;
            ");
            
            //13. Vista de Toneladas de rechazos sin agrupar por ticket
            migrationBuilder.Sql(@"
                create or replace view ""REPEM_Toneladas_Rechazos_Por_Ticket"" as
                    SELECT rpm.""REPEM04_Id""                              AS ""Id_Ticket"",
                           rpm.""REPEM07_NUAP""                            AS ""NUAP"",
                           round(r.""REPEM09_Toneladas""::numeric, 6)      AS ""Toneladas_Rechazadas"",
                           r.""REPEM09_Fecha_Corta""                       AS ""Fecha_Rechazo"",
                           r.""REPEM09_ECA""                               AS ""Num_ECA""
                    FROM ""Reporting-Emvarias_09-Rechazos"" r
                             INNER JOIN LATERAL ( SELECT irpm.""REPEM04_Id"",
                                                        irpm.""REPEM04_Patente"",
                                                        irpm.""REPEM04_EstadoServicio"",
                                                        irpm.""REPEM04_GrupoTurno"",
                                                        irpm.""REPEM04_TipoServicio"",
                                                        irpm.""REPEM04_RutaCodigo"",
                                                        irpm.""REPEM04_EsRefuerzo"",
                                                        irpm.""REPEM04_Interno"",
                                                        irpm.""REPEM04_PesoTotal"",
                                                        irpm.""REPEM04_IdServicio"",
                                                        irpm.""REPEM04_FechaHora_EntradaRuta"",
                                                        irpm.""REPEM04_FechaHora_SalidaRuta"",
                                                        irpm.""REPEM04_Observaciones"",
                                                        irpm.""REPEM04_PesoTotal_Toneladas"",
                                                        irpm.""REPEM04_FechaHora_Pesaje"",
                                                        irpm.""REPEM04_Fecha_de_Servicio"",
                                                        irpm.""REPEM04_FechaHora_InicioServicio"",
                                                        irpm.""REPEM04_FechaHora_LlegadaBase"",
                                                        irpm.""REPEM04_FechaHora_SalidaBase"",
                                                        mic.""REPEM07_Numero_de_Microruta"",
                                                        mic.""REPEM07_NUAP"",
                                                        mic.""REPEM07_Numero_Ruta_Intendencia"",
                                                        mic.""REPEM07_Ruta_Larga"",
                                                        mic.""REPEM07_Porcentaje_Limpieza_Urbana"",
                                                        mic.""REPEM07_Porcentaje_Barrido"",
                                                        mic.""REPEM07_Porcentaje_No_Aforado"",
                                                        mic.""REPEM07_Porcentaje_Residuos_Aprovechables""
                                                 FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" irpm
                                                          JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                                                               ON irpm.""REPEM04_RutaCodigo""::text = mic.""REPEM07_Numero_de_Microruta""::text
                                                 WHERE r.""REPEM09_Placa""::text = irpm.""REPEM04_Patente""::text
                                                   AND r.""REPEM09_Fecha_Corta"" = irpm.""REPEM04_FechaHora_Pesaje""::date
                                                   AND mic.""REPEM07_Ruta_Larga"" = r.""REPEM09_Ruta_Larga""
                                                 LIMIT 1) rpm ON true;
            ");
            
            //14. Vista de Reporte 34
            migrationBuilder.Sql(@"
                create or replace view ""REPEM_Reporte_SUI_Recolecciones_F34"" AS
                WITH f14dep as (
                    SELECT 720105237                                                  AS ""C1_NUSD"",
                       CASE
                           WHEN pb.""REPEM03_Tipo_de_origen"" = 'Others'::text THEN '4'::bigint
                           WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUAP'::text THEN '1'::bigint
                           WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUET'::text THEN '2'::bigint
                           WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUECA'::text THEN '3'::bigint
                           END                                                    AS ""C2_TIPO_ORIGEN"",
                       CASE
                           WHEN pb.""REPEM03_Tipo_de_origen"" = 'Others'::text THEN NULL
                           ELSE r14.""C1_NUAP""::bigint
                           END                                                    AS ""C3_NUSITIO_ORI"",
                       CASE
                           WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL
                           ELSE cli.""REPEM08_Nombre_Completo""
                           END                                                    AS ""C4_NOMBRE_EMPRESA"",
                       CASE
                           WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL
                           ELSE pb.""REPEM03_Nro_Identificacion_Tributaria""
                           END                                                    AS ""C5_NIT_EMPRESA"",
                       CASE
                           WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL
                           ELSE mun.""REPEM01_Codigo""
                           END                                                    AS ""C6_COD_DANE_ORI"",
                       pb.""REPEM03_Patente""                                       AS ""C7_PLACA"",
                       pb.""REPEM03_Fecha_de_entrada""::date                        AS ""C8_FECHA_INGRESO"",
                       pb.""REPEM03_Fecha_de_egreso""::date                         AS ""C9_FECHA_SALIDA"",
                       pb.""REPEM03_Fecha_de_entrada""::time without time zone      AS ""C10_HORA_INGRESO"",
                       pb.""REPEM03_Fecha_de_egreso""::time without time zone       AS ""C11_HORA_SALIDA"",
                       (r14.""C9_TON_BARRIDO""
                                 + r14.""C10_TONRESNA""
                                 - coalesce(r14.""C11_TONRECHAPR"", 0))::numeric
                                                                                  AS ""C12_TONELADAS"",
                       pb.""REPEM03_Id""                                            AS ""CE_ID_TICKET"",
                       coalesce(tct.""REPEM10_Valor"", 0)                           AS ""CE_VALOR_TICKET_LEGACY"",
                       coalesce(round(tct.""REPEM10_Valor""::numeric / 1000, 2), 0) AS ""CE_VALOR_TON_TICKET_LEGACY"",
                       coalesce(r14.""C11_TONRECHAPR"", 0)                          AS ""CE_TONELADAS_RECHAZADAS"",
                       TRUE                                                       AS ""CE_EXISTE_EN_F14"",
                       pb.""REPEM03_Nro_Identificacion_Tributaria""                 AS ""CE_NIT_EMPRESA"",
                       r14.""C1_NUAP""::bigint                                      AS ""CE_NUAP""
                from ""REPEM_Reporte_SUI_Recolecciones_F14"" r14
                         inner join ""Reporting-Emvarias_03-Pesaje_de_Balanza"" pb
                             on pb.""REPEM03_Id"" = r14.""CE_ID_TICKET""
                                and pb.""REPEM03_Fecha_de_cancelacion"" is null
                         left join ""Reporting-Emvarias_08-Clientes"" cli
                                   on pb.""REPEM03_Nro_Identificacion_Tributaria"" = cli.""REPEM08_NIT""
                         left join ""Reporting-Emvarias_01-Municipios"" mun on pb.""REPEM03_Municipio"" = mun.""REPEM01_Codigo""
                         left join ""Reporting-Emvarias_10-Tickets_de_Servicio_Generados"" tct
                                   on pb.""REPEM03_Id""::bigint = tct.""REPEM10_Id"" and pb.""REPEM03_Fecha_de_cancelacion"" is null
                ),
                base as (
                    SELECT * from f14dep
                    UNION ALL
                        (
                            SELECT 720105237                                              AS ""C1_NUSD"",
                               CASE
                                   WHEN pb.""REPEM03_Tipo_de_origen"" = 'Others'::text THEN '4'::bigint
                                   WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUAP'::text THEN '1'::bigint
                                   WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUET'::text THEN '2'::bigint
                                   WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUECA'::text THEN '3'::bigint
                               END                                                        AS ""C2_TIPO_ORIGEN"",
                               CASE
                                   WHEN pb.""REPEM03_Tipo_de_origen"" = 'Others'::text THEN NULL
                                   ELSE pb.""REPEM03_Nro_Unico_Area_Prestacion""::bigint
                               END                                                    AS ""C3_NUSITIO_ORI"",
                               CASE
                                   WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL
                                   ELSE cli.""REPEM08_Nombre_Completo""
                                   END                                                    AS ""C4_NOMBRE_EMPRESA"",
                               CASE
                                   WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL
                                   ELSE pb.""REPEM03_Nro_Identificacion_Tributaria""
                                   END                                                    AS ""C5_NIT_EMPRESA"",
                               CASE
                                   WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL
                                   ELSE mun.""REPEM01_Codigo""
                                   END                                                    AS ""C6_COD_DANE_ORI"",
                               pb.""REPEM03_Patente""                                       AS ""C7_PLACA"",
                               pb.""REPEM03_Fecha_de_entrada""::date                        AS ""C8_FECHA_INGRESO"",
                               pb.""REPEM03_Fecha_de_egreso""::date                         AS ""C9_FECHA_SALIDA"",
                               pb.""REPEM03_Fecha_de_entrada""::time without time zone      AS ""C10_HORA_INGRESO"",
                               pb.""REPEM03_Fecha_de_egreso""::time without time zone       AS ""C11_HORA_SALIDA"",
                               round(tct.""REPEM10_Valor""::numeric / 1000, 3) -
                                    coalesce(tre.""Toneladas_Rechazadas"", 0)::numeric      AS ""C12_TONELADAS"",
                               pb.""REPEM03_Id""                                            AS ""CE_ID_TICKET"",
                               coalesce(tct.""REPEM10_Valor"", 0)                           AS ""CE_VALOR_TICKET_LEGACY"",
                               coalesce(round(tct.""REPEM10_Valor""::numeric / 1000, 2), 0) AS ""CE_VALOR_TON_TICKET_LEGACY"",
                               coalesce(tre.""Toneladas_Rechazadas"", 0)::numeric           AS ""CE_TONELADAS_RECHAZADAS"",
                               FALSE                                                      AS ""CE_EXISTE_EN_F14"",
                               pb.""REPEM03_Nro_Identificacion_Tributaria""                 AS ""CE_NIT_EMPRESA"",
                               ap.""REPEM02_Codigo""                                        AS ""CE_NUAP""
                            from ""Reporting-Emvarias_10-Tickets_de_Servicio_Generados"" tct
                                    inner join ""Reporting-Emvarias_03-Pesaje_de_Balanza"" pb
                                        on pb.""REPEM03_Id""::bigint = tct.""REPEM10_Id"" and pb.""REPEM03_Fecha_de_cancelacion"" is null
                                    left join ""REPEM_Toneladas_Rechazos_Agrupadas_Por_Ticket"" tre
                                        on (pb.""REPEM03_Id"" = tre.""Ticket_Asignable"" or tre.""Ticket_Asignable""::bigint = tct.""REPEM10_Id"")
                                            and pb.""REPEM03_Nro_Unico_Area_Prestacion"" = 440405001
                                    left join ""Reporting-Emvarias_08-Clientes"" cli
                                        on pb.""REPEM03_Nro_Identificacion_Tributaria"" = cli.""REPEM08_NIT""
                                    left join ""Reporting-Emvarias_01-Municipios"" mun on pb.""REPEM03_Municipio"" = mun.""REPEM01_Codigo""
                                    left join ""Reporting-Emvarias_02-Areas_de_Aprovechamiento"" ap on ap.""REPEM02_Nombre"" = mun.""REPEM01_Nombre""
                                    left join lateral (
                                        select
                                            r14.""CE_EXISTE_EN_F14""
                                        from f14dep r14
                                        where pb.""REPEM03_Id"" = r14.""CE_ID_TICKET""
                                        LIMIT 1
                                    ) as r14 on true
                            where r14 is null
                        )
                ), rch AS (
                    SELECT
                        ""C1_NUSD"",
                        3 AS ""C2_TIPO_ORIGEN"",
                        trept.""Num_ECA"" AS ""C3_NUSITIO_ORI"",
                        ""C4_NOMBRE_EMPRESA"",
                        ""C5_NIT_EMPRESA"",
                        ""C6_COD_DANE_ORI"",
                        ""C7_PLACA"",
                        ""C8_FECHA_INGRESO"",
                        ""C9_FECHA_SALIDA"",
                        ""C10_HORA_INGRESO"",
                        ""C11_HORA_SALIDA"",
                        trept.""Toneladas_Rechazadas"" as ""C12_TONELADAS"",
                        ""CE_ID_TICKET"",
                        ""CE_VALOR_TICKET_LEGACY"",
                        ""CE_VALOR_TON_TICKET_LEGACY"",
                        0 AS ""CE_TONELADAS_RECHAZADAS"",
                        ""CE_EXISTE_EN_F14"",
                        ""CE_NIT_EMPRESA"",
                        trept.""NUAP"" AS ""CE_NUAP""
                    from base bs
                        join ""REPEM_Toneladas_Rechazos_Por_Ticket"" trept
                            on bs.""CE_ID_TICKET"" = trept.""Id_Ticket"" and trept.""NUAP"" = bs.""CE_NUAP""
                        left join ""Reporting-Emvarias_08-Clientes"" cli
                                        on bs.""CE_NIT_EMPRESA"" = cli.""REPEM08_NIT""
                )
                SELECT * from base
                UNION ALL
                SELECT * from rch;
            ");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"
                            DROP VIEW IF EXISTS ""REPEM_Reporte_SUI_Recolecciones_F34"";
                            DROP VIEW IF EXISTS ""REPEM_Toneladas_Rechazos_Agrupadas_Por_Ticket"" CASCADE;
                            DROP VIEW IF EXISTS ""REPEM_Reporte_SUI_Recolecciones_F14_con_Aditivos"";
                            DROP VIEW IF EXISTS ""REPEM_Reporte_SUI_Recolecciones_F14"";
                            DROP VIEW IF EXISTS ""REPEM_Toneladas_Rechazos_Por_Ticket"";
                            DROP VIEW IF EXISTS ""REPEM_Descuento_Pesaje_Excepcional_Por_Ticket"";
                            DROP VIEW IF EXISTS ""REPEM_Tickets_Compensables"";
                            DROP VIEW IF EXISTS ""REPEM_Detalle_Compensacion_Tickets"" CASCADE;
                            DROP VIEW IF EXISTS ""REPEM_Dependencia_Toneladas_A_Compensar"";
                            DROP VIEW IF EXISTS ""REPEM_Distribuciones_de_Microrutas"";
                            DROP VIEW IF EXISTS ""REPEM_Sumatoria_Pesos_Rutas_Compartidas"";
                            DROP VIEW IF EXISTS ""REPEM_Toneladas_Descuento_Excepcional"";
                            DROP VIEW IF EXISTS ""REPEM_Descuento_Por_Ticket"";
                            DROP FUNCTION IF EXISTS coalesce_non_negative(NUMERIC, NUMERIC);
                        ");
            
            //1. Funcion de Utilidad para retornar numeros no negativos
            migrationBuilder.Sql(@"
                CREATE OR REPLACE FUNCTION coalesce_non_negative(value NUMERIC, fallback_value NUMERIC)
                    RETURNS NUMERIC AS
                $$
                BEGIN
                    IF value < 0 THEN
                        RETURN fallback_value;
                    ELSE
                        RETURN value;
                    END IF;
                END;
                $$ LANGUAGE plpgsql;
            ");

            //2. Vista de Toneladas con Descuento Excepcional
            migrationBuilder.Sql(@"
                create or replace view ""REPEM_Toneladas_Descuento_Excepcional"" as
                    SELECT a.""Año"",
                           a.""Mes"",
                           f.""Rut_LMV"",
                           f.""Rut_MJS"",
                           f.""Nro_LMV"",
                           f.""Nro_MJS"",
                           f.""Nro_Domingo"",
                           f.""Total_Dias"",
                           f.""Rut_LMV"" * f.""Nro_LMV""                                             as ""Klmts_LMV"",
                           round((f.""Rut_MJS"" * f.""Nro_MJS"")::numeric, 3)                        as ""Klmts_MJS"",
                           f.""Rut_LMV"" * f.""Nro_LMV"" + f.""Rut_MJS"" * f.""Nro_MJS""                 as ""Klmts_Total"",
                           40::numeric                                                           as ""Densidad"",
                           (f.""Rut_LMV"" * f.""Nro_LMV"" + f.""Rut_MJS"" * f.""Nro_MJS"") * 40::numeric as ""Kilos_Total_Descuento"",
                           round(((f.""Rut_LMV"" * f.""Nro_LMV"" + f.""Rut_MJS"" * f.""Nro_MJS"") * 40)::numeric / 1000,
                                 3)                                                              as ""Toneladas_Total_Descuento""
                    FROM (SELECT extract(year from ""REPEM04_FechaHora_Pesaje"")::int  as ""Año"",
                                 extract(month from ""REPEM04_FechaHora_Pesaje"")::int as ""Mes""
                          FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta""
                          GROUP BY extract(year from ""REPEM04_FechaHora_Pesaje""), extract(month from ""REPEM04_FechaHora_Pesaje"")) a
                             LEFT JOIN LATERAL (
                        SELECT 4.00::double precision                                               AS ""Rut_LMV"",
                               4.02::double precision                                               AS ""Rut_MJS"",
                               SUM(CASE WHEN EXTRACT(DOW FROM date) IN (0, 2, 4) THEN 1 ELSE 0 END) AS ""Nro_LMV"",
                               SUM(CASE WHEN EXTRACT(DOW FROM date) IN (1, 3, 5) THEN 1 ELSE 0 END) AS ""Nro_MJS"",
                               SUM(CASE WHEN EXTRACT(DOW FROM date) IN (6) THEN 1 ELSE 0 END)       AS ""Nro_Domingo"",
                               COUNT(*)                                                             AS ""Total_Dias""
                        FROM (SELECT date::date
                              FROM generate_series(
                                           date_trunc('month', pg_catalog.make_date(a.""Año"", a.""Mes"", 1)), -- Fecha de pesaje, truncada al primer día del mes
                                           date_trunc('month', pg_catalog.make_date(a.""Año"", a.""Mes"", 1)) +
                                           interval '1 month - 1 day', -- Fecha de pesaje, truncada al último día del mes
                                           '1 day' -- Intervalo de un día para contar todos los días del mes
                                   ) date) AS final_subquery
                        ) f ON TRUE;
            ");
            
            //3. Vista de Sumatoria de Pesos de Rutas Compartidas
            migrationBuilder.Sql(@"
                create or replace view ""REPEM_Sumatoria_Pesos_Rutas_Compartidas"" as
                SELECT ""Año"",
                       ""Mes"",
                       SUM(""PesoTotal"")        as ""Suma_Pesos_M3"",
                       SUM(""PesoTotal"") / 1000 as ""Suma_Pesos_Toneladas""
                FROM (SELECT rpm.""REPEM04_Id""                                   as ""IdTicket"",
                             extract(year from rpm.""REPEM04_FechaHora_Pesaje"")  as ""Año"",
                             extract(month from rpm.""REPEM04_FechaHora_Pesaje"") as ""Mes"",
                             rpm.""REPEM04_PesoTotal""                            as ""PesoTotal""
                      FROM ""Reporting-Emvarias_07-Microrutas"" mr
                               join ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                                    on ""REPEM07_Numero_de_Microruta""::text = ""REPEM04_RutaCodigo""
                      where mr.""REPEM07_NUAP"" <> 440405001
                        and mr.""REPEM07_Ruta_Larga"" <> '0614001'
                      group by rpm.""REPEM04_Id"", extract(year from rpm.""REPEM04_FechaHora_Pesaje""),
                               extract(month from rpm.""REPEM04_FechaHora_Pesaje"")) subquery
                GROUP BY ""Año"", ""Mes""
                ORDER BY ""Año"", ""Mes"";
            ");
            
            //4. Vista de Distribuciones de Microrutas
            migrationBuilder.Sql(@"
                create or replace view ""REPEM_Distribuciones_de_Microrutas"" as
                SELECT rv.""REPEM05_Año""                           AS ""Año"",
                       rv.""REPEM05_Mes""                           AS ""Mes"",
                       ap.""REPEM02_Codigo""                        AS ""NUAP"",
                       ap.""REPEM02_Nombre""                        AS ""Area_Aprovechamiento"",
                       count(*)                                   AS ""Cantidad_de_Viajes"",
                       round(rv.""REPEM05_Toneladas""::numeric, 3)  AS ""Toneladas_Recogidas"",
                       round(rv.""REPEM05_Toneladas"" / count(*)::numeric, 3) AS ""Toneladas_Distribuidas"",
                       round(
                               round(rv.""REPEM05_Toneladas""::numeric, 3) /
                               src.""Suma_Pesos_Toneladas""::numeric, 4) * 100
                                                                  AS ""Porcentaje_Distribucion_Peaje""
                FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm2
                         JOIN ""Reporting-Emvarias_07-Microrutas"" mic2
                              ON rpm2.""REPEM04_RutaCodigo""::text = mic2.""REPEM07_Numero_de_Microruta""::character varying(16)::text
                         JOIN ""Reporting-Emvarias_05-Recoleccion_Vehicular"" rv
                              ON rv.""REPEM05_Año""::numeric = EXTRACT(year FROM rpm2.""REPEM04_FechaHora_Pesaje"") AND
                                 rv.""REPEM05_Mes""::numeric = EXTRACT(month FROM rpm2.""REPEM04_FechaHora_Pesaje"") AND
                                 rv.""REPEM05_NUAP"" = mic2.""REPEM07_NUAP""
                         JOIN ""Reporting-Emvarias_02-Areas_de_Aprovechamiento"" ap ON ap.""REPEM02_Codigo"" = mic2.""REPEM07_NUAP""
                         JOIN ""REPEM_Sumatoria_Pesos_Rutas_Compartidas"" src
                              ON src.""Año""::numeric = EXTRACT(year FROM rpm2.""REPEM04_FechaHora_Pesaje"") AND
                                 src.""Mes""::numeric = EXTRACT(month FROM rpm2.""REPEM04_FechaHora_Pesaje"")
                WHERE mic2.""REPEM07_Porcentaje_No_Aforado"" = 0::double precision
                  AND ap.""REPEM02_Codigo"" <> 440405001
                  AND NOT (mic2.""REPEM07_NUAP"" = 441005380 AND mic2.""REPEM07_Ruta_Larga"" = '0911504')
                GROUP BY rv.""REPEM05_Toneladas"", src.""Suma_Pesos_Toneladas"", ap.""REPEM02_Nombre"", rv.""REPEM05_Año"",
                         rv.""REPEM05_Mes"", ap.""REPEM02_Codigo""
                ORDER BY rv.""REPEM05_Año"", rv.""REPEM05_Mes"", ap.""REPEM02_Nombre"";
            ");
            
            //5. Vista de dependencia para calculo de detalle de compensación de tickets
            migrationBuilder.Sql(@"
                create or replace view ""REPEM_Dependencia_Toneladas_A_Compensar"" as
                    SELECT rpm.""REPEM04_Id""                                                                             as ""Id_Ticket"",
                                                     mic.""REPEM07_NUAP""                                                                           as ""NUAP"",
                                                     mic.""REPEM07_Numero_de_Microruta""                                                            as ""Numero_de_Microruta"",
                                                     mic.""REPEM07_Numero_Ruta_Intendencia""                                                        as ""Numero_Ruta_Intendencia"",
                                                     rpm.""REPEM04_Patente""                                                                        as ""Patente"",
                                                     dpm.""Toneladas_Distribuidas""                                                                 as ""Toneladas_Distribuidas"",
                                                     rpm.""REPEM04_PesoTotal_Toneladas""                                                            as ""PesoTotal_Toneladas"",
                                                     mic.""REPEM07_Porcentaje_No_Aforado""                                                          as ""Porcentaje_No_Aforado"",
                                                     mic.""REPEM07_Porcentaje_Limpieza_Urbana""                                                     as ""Porcentaje_Limpieza_Urbana"",
                                                     mic.""REPEM07_Porcentaje_Barrido""                                                             as ""Porcentaje_Barrido"",
                                                     mic.""REPEM07_Porcentaje_Residuos_Aprovechables""                                              as ""Porcentaje_Residuos_Aprovechables"",
                                                     rpm.""REPEM04_FechaHora_Pesaje""                                                               as ""Fecha_Hora_Pesaje"",
                                                     SUM(dpm.""Toneladas_Distribuidas"")
                                                     OVER (PARTITION BY rpm.""REPEM04_Id""
                                                         order by mic.""REPEM07_NUAP""
                                                         RANGE BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)::numeric                     AS ""Sumatoria_Acumulada""
                                              FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                                                       INNER JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                                                                  ON rpm.""REPEM04_RutaCodigo"" = mic.""REPEM07_Numero_de_Microruta""::text
                                                       INNER JOIN ""Reporting-Emvarias_02-Areas_de_Aprovechamiento"" ap
                                                                  ON ap.""REPEM02_Codigo"" = mic.""REPEM07_NUAP""
                                                       JOIN ""REPEM_Distribuciones_de_Microrutas"" dpm
                                                            ON dpm.""NUAP"" = mic.""REPEM07_NUAP""
                                                                AND dpm.""Año"" = extract(year from rpm.""REPEM04_FechaHora_Pesaje"")
                                                                AND dpm.""Mes"" = extract(month from rpm.""REPEM04_FechaHora_Pesaje"");
            ");

            //6. Vista de detalle de compensación de tickets
            migrationBuilder.Sql(@"
                create or replace view ""REPEM_Detalle_Compensacion_Tickets"" as
                    with cte AS (
                        select *,
                           CASE WHEN ""Sumatoria_Acumulada"" < ""PesoTotal_Toneladas""
                               THEN 0
                               ELSE round(""Sumatoria_Acumulada"" - ""PesoTotal_Toneladas""::numeric, 3)
                           END as ""Exceso_Acumulado""
                           from ""REPEM_Dependencia_Toneladas_A_Compensar"" dtc
                    ), cnt AS (
                        select
                            cte.""Id_Ticket"",
                            count(cte.""Id_Ticket"") as ""Cantidad""
                        from cte
                        group by cte.""Id_Ticket""
                    ), rst AS (
                        select distinct on (cte.""Id_Ticket"")
                            cte.""Id_Ticket"",
                            round((cte.""PesoTotal_Toneladas"" - MAX(cte.""Sumatoria_Acumulada"") OVER (PARTITION BY cte.""Id_Ticket""))::numeric, 3) as ""Resto_Disponible""
                        from cte
                        inner join cnt on cnt.""Id_Ticket"" = cte.""Id_Ticket""
                        where ""Exceso_Acumulado"" = 0 and cnt.""Cantidad"" > 1
                        order by cte.""Id_Ticket"", cte.""Toneladas_Distribuidas"" desc
                    ), sprst AS (
                        select distinct on (cte.""Id_Ticket"")
                            cte.""Id_Ticket"",
                            round(cte.""Toneladas_Distribuidas"" - cte.""PesoTotal_Toneladas""::numeric, 3) as ""Resto_Disponible""
                        from cte
                        inner join cnt on cnt.""Id_Ticket"" = cte.""Id_Ticket""
                        where ""Exceso_Acumulado"" > 0 and cnt.""Cantidad"" = 1
                        order by cte.""Id_Ticket"", cte.""Toneladas_Distribuidas"" desc
                    ), pcomp AS (
                        SELECT
                           cte.*,
                           cte.""Toneladas_Distribuidas"" - rst.""Resto_Disponible"" as ""Toneladas_Resultantes""
                        FROM cte
                        INNER JOIN (
                            SELECT icte.""Id_Ticket"", MAX(icte.""Toneladas_Distribuidas"") AS max_toneladas
                            FROM cte icte
                            GROUP BY icte.""Id_Ticket""
                        ) sub ON cte.""Id_Ticket"" = sub.""Id_Ticket"" AND cte.""Toneladas_Distribuidas"" = sub.max_toneladas
                        inner join rst on rst.""Id_Ticket"" = cte.""Id_Ticket""
                        where cte.""Exceso_Acumulado"" > 0
                        UNION ALL
                        SELECT
                            cte.*,
                            sprst.""Resto_Disponible"" as ""Toneladas_Resultantes""
                            FROM cte
                            inner join sprst on sprst.""Id_Ticket"" = cte.""Id_Ticket""
                    )
                    select
                        cte.""Id_Ticket"",
                        cte.""NUAP"",
                        cte.""Fecha_Hora_Pesaje"",
                        cte.""Toneladas_Distribuidas"" - coalesce(pcomp.""Toneladas_Resultantes"", 0) as ""Toneladas_Resultantes"",
                        CASE WHEN cte.""Exceso_Acumulado"" = 0 or pcomp is not null THEN 'ORIGINAL' ELSE 'TOTAL' END as ""Tipo_Compensacion""
                    from cte
                    left join pcomp on pcomp.""Id_Ticket"" = cte.""Id_Ticket"" and cte.""NUAP"" = pcomp.""NUAP""
                    union all
                    select
                        pcomp.""Id_Ticket"",
                        pcomp.""NUAP"",
                        pcomp.""Fecha_Hora_Pesaje"",
                        pcomp.""Toneladas_Resultantes"",
                        'PARCIAL' as ""Tipo""
                    from pcomp;
            ");
            
            //7. Vista de tickets compensables
            migrationBuilder.Sql(@"
                create or replace view ""REPEM_Tickets_Compensables"" AS
                    WITH cte AS (
                    SELECT
                           td.""Id_Ticket"",
                           td.""Fecha_Hora_Pesaje""::date AS ""Fecha_Pesaje"",
                           SUM(td.""Toneladas_Resultantes"") AS ""Suma_Toneladas_Agrupadas""
                        FROM ""REPEM_Detalle_Compensacion_Tickets"" td
                        WHERE td.""Tipo_Compensacion"" = 'PARCIAL' or td.""Tipo_Compensacion"" = 'TOTAL'
                        group by td.""Fecha_Hora_Pesaje""::date, td.""Id_Ticket""
                    )
                    SELECT cte.""Id_Ticket"",
                           cte.""Suma_Toneladas_Agrupadas"" as ""Suma_Agrupacion_Toneladas_Por_Compensar"",
                           rpm.""REPEM04_Id"" as ""Nro_Ticket_Compensacion"",
                           rpm.""REPEM04_PesoTotal_Toneladas"" as ""Maximo_Toneladas_Compensables""
                    FROM cte
                    LEFT JOIN ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm on
                        rpm.""REPEM04_Id"" = (
                            SELECT
                                rpm2.""REPEM04_Id""
                            FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm2
                            WHERE rpm2.""REPEM04_FechaHora_Pesaje""::date = cte.""Fecha_Pesaje""
                            and rpm2.""REPEM04_RutaCodigo"" IN ('615618', '618219', '618119', '618319')
                            and rpm2.""REPEM04_PesoTotal_Toneladas"" >= cte.""Suma_Toneladas_Agrupadas""
                            ORDER BY rpm2.""REPEM04_PesoTotal_Toneladas"" DESC
                            LIMIT 1
                        );
            ");
            
            //8. Vista de Descuento de Pesaje por Ticket
            migrationBuilder.Sql(@"create or replace view ""REPEM_Descuento_Por_Ticket"" as
                SELECT rpm.""REPEM04_Id""          AS ""Id_Ticket"",
                       sum(
                               CASE
                                   WHEN mic.""REPEM07_NUAP"" = 440405001 THEN 0::double precision
                                   WHEN mic.""REPEM07_NUAP"" = 440705360 AND
                                        mic.""REPEM07_Porcentaje_No_Aforado"" <> 0::double precision THEN
                                       mic.""REPEM07_Porcentaje_No_Aforado"" / 100::double precision *
                                       rpm.""REPEM04_PesoTotal_Toneladas""
                                   ELSE td.""Toneladas_Resultantes""::double precision
                                   END)::numeric AS ""Toneladas_Descuento_Medellin"",
                       sum(
                               CASE
                                   WHEN mic.""REPEM07_NUAP"" = 440405001 THEN 0::double precision
                                   WHEN mic.""REPEM07_NUAP"" = 440705360 AND
                                        mic.""REPEM07_Porcentaje_No_Aforado"" <> 0::double precision THEN
                                       mic.""REPEM07_Porcentaje_No_Aforado"" / 100::double precision
                                           * 2
                                           * pe.""REPEM06_Valor""
                                   ELSE (pe.""REPEM06_Valor""
                                       * 2
                                       * (dpm.""Porcentaje_Distribucion_Peaje"" / 100)
                                       )::double precision
                                   END)::numeric AS ""Peaje_Descuento_Medellin""
                FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                         JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                              ON rpm.""REPEM04_RutaCodigo""::text = mic.""REPEM07_Numero_de_Microruta""::text
                         JOIN ""REPEM_Distribuciones_de_Microrutas"" dpm
                              ON dpm.""NUAP"" = mic.""REPEM07_NUAP"" AND
                                 dpm.""Año""::numeric = EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"") AND
                                 dpm.""Mes""::numeric = EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje"")
                         JOIN ""REPEM_Detalle_Compensacion_Tickets"" td
                              ON td.""Id_Ticket"" = rpm.""REPEM04_Id"" AND
                                 td.""NUAP"" = mic.""REPEM07_NUAP"" AND
                                 td.""Tipo_Compensacion"" = 'ORIGINAL'
                         JOIN ""Reporting-Emvarias_06-Peajes"" pe ON pe.""REPEM06_Placa"" = rpm.""REPEM04_Patente"" AND
                                                                   pe.""REPEM06_Fecha_Validez"" = (SELECT p.""REPEM06_Fecha_Validez""
                                                                                                 FROM ""Reporting-Emvarias_06-Peajes"" p
                                                                                                 WHERE p.""REPEM06_Placa"" = rpm.""REPEM04_Patente""
                                                                                                 ORDER BY abs(EXTRACT(epoch FROM
                                                                                                                      rpm.""REPEM04_FechaHora_Pesaje"" -
                                                                                                                      p.""REPEM06_Fecha_Validez""))
                                                                                                 LIMIT 1) -- El valor del pesaje mas cercano a la fecha de pesaje, por fecha de validez/vigencia desde.
                WHERE mic.""REPEM07_NUAP"" <> '440405001'::bigint
                GROUP BY rpm.""REPEM04_Id"";
            ");

            //9. Vista de Toneladas de Rechazos Agrupadas por Ticket
            migrationBuilder.Sql(@"
                create or replace view ""REPEM_Toneladas_Rechazos_Agrupadas_Por_Ticket"" as
                select
                    rpm.""REPEM04_Id"" as ""Ticket_Asignable"",
                    round(sum(r.""REPEM09_Toneladas"")::numeric, 6) as ""Toneladas_Rechazadas"",
                    count(r.""REPEM09_Id"") as ""Cantidad_Rechazos"",
                    MAX(r.""REPEM09_Fecha_Corta"") as ""Fecha_Rechazo""
                from ""Reporting-Emvarias_09-Rechazos"" r
                left join LATERAL (
                        select * from ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                        join ""Reporting-Emvarias_07-Microrutas"" mic
                        on rpm.""REPEM04_RutaCodigo"" = mic.""REPEM07_Numero_de_Microruta""::text
                        where r.""REPEM09_Placa"" = rpm.""REPEM04_Patente"" and
                              r.""REPEM09_Fecha_Corta"" = rpm.""REPEM04_FechaHora_Pesaje""::date and
                              mic.""REPEM07_Ruta_Larga"" = r.""REPEM09_Ruta_Larga""
                        limit 1
                    ) rpm on true
                group by rpm.""REPEM04_Id"";
            ");
            
            //10. Vista de Toneladas descuento de Pesaje excepcional por Ticket
            migrationBuilder.Sql(@"
                create or replace view ""REPEM_Descuento_Pesaje_Excepcional_Por_Ticket"" as
                    select MAX(rpm.""REPEM04_Id"")                              as ""NroTicket"",
                           440705360                                          as ""NUAP"",
                           614001                                             as ""RutaCodigo"",
                           tde.""Toneladas_Total_Descuento""                    as ""Toneladas_Descuento"",
                           extract(year from rpm.""REPEM04_FechaHora_Pesaje"")  as ""Año"",
                           extract(month from rpm.""REPEM04_FechaHora_Pesaje"") as ""Mes""
                    from ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                             join ""Reporting-Emvarias_07-Microrutas"" mic
                                  on rpm.""REPEM04_RutaCodigo""::bigint = mic.""REPEM07_Numero_de_Microruta""
                             join ""REPEM_Toneladas_Descuento_Excepcional"" tde
                                  on extract(year from rpm.""REPEM04_FechaHora_Pesaje"") = tde.""Año"" and
                                     extract(month from rpm.""REPEM04_FechaHora_Pesaje"") = tde.""Mes""
                             LEFT JOIN ""REPEM_Distribuciones_de_Microrutas"" dpm
                                       ON dpm.""NUAP"" = mic.""REPEM07_NUAP""
                                           AND dpm.""Año"" = extract(year from rpm.""REPEM04_FechaHora_Pesaje"")
                                           AND dpm.""Mes"" = extract(month from rpm.""REPEM04_FechaHora_Pesaje"")
                             LEFT JOIN ""REPEM_Descuento_Por_Ticket"" tr
                                       ON tr.""Id_Ticket"" = rpm.""REPEM04_Id""
                    where rpm.""REPEM04_RutaCodigo"" NOT IN ('N9911111', 'N0000133', 'T9911111', 'T0000133', '3RECDON0312201F2')
                      and mic.""REPEM07_Numero_de_Microruta"" = 614001
                      and mic.""REPEM07_NUAP"" = 440705360
                      and (CASE
                               WHEN mic.""REPEM07_NUAP"" = 440705360 AND mic.""REPEM07_Porcentaje_No_Aforado"" <> 0
                                   THEN tr.""Toneladas_Descuento_Medellin""
                               ELSE round(dpm.""Toneladas_Distribuidas""::numeric, 3)
                               END - CASE
                                         WHEN mic.""REPEM07_Porcentaje_Barrido"" <> 0
                                             THEN round(((CASE
                                                              WHEN mic.""REPEM07_NUAP"" = 440405001
                                                                  THEN round(
                                                                      (rpm.""REPEM04_PesoTotal_Toneladas"" -
                                                                       coalesce(tr.""Toneladas_Descuento_Medellin"", 0))::numeric, 3)
                                                              WHEN mic.""REPEM07_NUAP"" = 440705360 AND
                                                                   mic.""REPEM07_Porcentaje_No_Aforado"" <> 0
                                                                  THEN tr.""Toneladas_Descuento_Medellin""
                                                              ELSE round(dpm.""Toneladas_Distribuidas""::numeric, 3)
                                             END) * (mic.""REPEM07_Porcentaje_Barrido"" / 100))::numeric, 3)
                                         ELSE 0
                               END) >= tde.""Toneladas_Total_Descuento""
                    group by extract(year from rpm.""REPEM04_FechaHora_Pesaje""),
                             extract(month from rpm.""REPEM04_FechaHora_Pesaje""),
                             tde.""Toneladas_Total_Descuento"";
            ");
            
            //11. Vista de Reporte 14
            migrationBuilder.Sql(@"
                create or replace view ""REPEM_Reporte_SUI_Recolecciones_F14"" AS
                    WITH cte as (
                        SELECT
                                rpm.""REPEM04_Id"" AS ""Id_Ticket"",
                                mic.""REPEM07_NUAP"" AS ""NUAP"",
                                CASE
                                    WHEN mic.""REPEM07_NUAP"" = 440405001 THEN round((rpm.""REPEM04_PesoTotal_Toneladas"" - coalesce(tr.""Toneladas_Descuento_Medellin"", 0))::numeric, 3)
                                    WHEN mic.""REPEM07_NUAP"" = 440705360 AND mic.""REPEM07_Porcentaje_No_Aforado"" <> 0 THEN tr.""Toneladas_Descuento_Medellin""
                                    ELSE round(tdha.""Toneladas_Resultantes""::numeric, 3)
                                END AS ""Calculo_Toneladas"",
                                CASE
                                    WHEN mic.""REPEM07_Porcentaje_Barrido"" <> 0 THEN round(((CASE
                                        WHEN mic.""REPEM07_NUAP"" = 440405001 THEN round((rpm.""REPEM04_PesoTotal_Toneladas"" - coalesce(tr.""Toneladas_Descuento_Medellin"", 0))::numeric, 3)
                                        WHEN mic.""REPEM07_NUAP"" = 440705360 AND mic.""REPEM07_Porcentaje_No_Aforado"" <> 0 THEN tr.""Toneladas_Descuento_Medellin""
                                        ELSE round(tdha.""Toneladas_Resultantes""::numeric, 3)
                                    END) * (mic.""REPEM07_Porcentaje_Barrido"" / 100))::numeric, 3)
                                    ELSE 0
                                END AS ""Calculo_Barrido"",
                                CASE
                                    WHEN pe IS NULL THEN 0
                                    WHEN mic.""REPEM07_NUAP"" = 440405001 THEN round((pe.""REPEM06_Valor"" * 2 - coalesce(tr.""Peaje_Descuento_Medellin"", 0))::numeric, 0)
                                    WHEN mic.""REPEM07_NUAP"" = 440705360 AND mic.""REPEM07_Porcentaje_No_Aforado"" <> 0 THEN round(tr.""Peaje_Descuento_Medellin""::numeric, 0)
                                    ELSE round((pe.""REPEM06_Valor"" * 2 * (coalesce(dpm.""Porcentaje_Distribucion_Peaje"", 100) / 100))::numeric, 0)
                                END AS ""Calculo_Peaje"",
                                coalesce(tdha.""Tipo_Compensacion"", 'ORIGINAL') as ""Tipo_Compensacion""
                            FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                            INNER JOIN ""Reporting-Emvarias_07-Microrutas"" mic ON rpm.""REPEM04_RutaCodigo"" = mic.""REPEM07_Numero_de_Microruta""::text
                            LEFT JOIN ""REPEM_Distribuciones_de_Microrutas"" dpm
                                ON dpm.""NUAP"" = mic.""REPEM07_NUAP""
                               AND dpm.""Año"" = extract(year from rpm.""REPEM04_FechaHora_Pesaje"")
                               AND dpm.""Mes"" = extract(month from rpm.""REPEM04_FechaHora_Pesaje"")
                            LEFT JOIN ""Reporting-Emvarias_06-Peajes"" pe ON pe.""REPEM06_Placa"" = rpm.""REPEM04_Patente""
                            LEFT JOIN ""REPEM_Descuento_Por_Ticket"" tr
                                ON tr.""Id_Ticket"" = rpm.""REPEM04_Id""
                            LEFT JOIN ""REPEM_Detalle_Compensacion_Tickets"" tdha
                                ON tdha.""Id_Ticket"" = rpm.""REPEM04_Id""
                                       AND tdha.""NUAP"" = mic.""REPEM07_NUAP""
                                       AND tdha.""Tipo_Compensacion"" = 'ORIGINAL'
                    )
                    SELECT
                        mic.""REPEM07_NUAP"" AS ""C1_NUAP"",
                        1 AS ""C2_TIPO_SITIO"",
                        720105237 AS ""C3_NUSD"",
                        rpm.""REPEM04_Patente"" AS ""C4_PLACA"",
                        rpm.""REPEM04_FechaHora_Pesaje""::date AS ""C5_FECHA"",
                        rpm.""REPEM04_FechaHora_Pesaje""::time without time zone AS ""C6_HORA"",
                        mic.""REPEM07_Numero_Ruta_Intendencia"" AS ""C7_NUMICRO"",
                        0 AS ""C8_TON_LIMP_URB"",
                        bs.""Calculo_Barrido"" + coalesce(tde.""Toneladas_Descuento"", 0) AS ""C9_TON_BARRIDO"",
                        bs.""Calculo_Toneladas""
                            - bs.""Calculo_Barrido""
                            - coalesce(tde.""Toneladas_Descuento"", 0)
                            - coalesce(rtc.""Suma_Agrupacion_Toneladas_Por_Compensar"", 0) AS ""C10_TONRESNA"",
                        coalesce(tre.""Toneladas_Rechazadas"", 0) AS ""C11_TONRECHAPR"",
                        CASE
                            WHEN mic.""REPEM07_Porcentaje_Residuos_Aprovechables"" <> 0 THEN round((bs.""Calculo_Toneladas"" * (mic.""REPEM07_Porcentaje_Residuos_Aprovechables"" / 100))::numeric, 3)
                            ELSE 0
                        END AS ""C12_TONRESAPR"",
                        '1' AS ""C13_SISTEMA_MEDICION"",
                        bs.""Calculo_Peaje"" AS ""C14_VLRPEAJ"",
                        rpm.""REPEM04_Id"" AS ""CE_ID_TICKET"",
                        ap.""REPEM02_Nombre"" AS ""CE_NOMBRE_AREA"",
                        mic.""REPEM07_Ruta_Larga"" AS ""CE_RUTA_LARGA"",
                        rpm.""REPEM04_PesoTotal_Toneladas"" AS ""CE_TON_TOTAL"",
                        bool(rtc IS NOT NULL) AS ""CE_REL_COMPENSACION"",
                        rtc.""Id_Ticket"" AS ""CE_REL_COMPENSACION_ID_TICKET""
                        from ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                    INNER JOIN cte bs on
                        bs.""Calculo_Toneladas"" is not null
                        AND rpm.""REPEM04_Id"" = bs.""Id_Ticket""
                    INNER JOIN ""Reporting-Emvarias_07-Microrutas"" mic on rpm.""REPEM04_RutaCodigo"" = mic.""REPEM07_Numero_de_Microruta""::text
                        AND mic.""REPEM07_NUAP"" = bs.""NUAP""
                    INNER JOIN ""Reporting-Emvarias_02-Areas_de_Aprovechamiento"" ap
                        ON ap.""REPEM02_Codigo"" = mic.""REPEM07_NUAP""
                    LEFT JOIN ""REPEM_Descuento_Pesaje_Excepcional_Por_Ticket"" tde ON rpm.""REPEM04_Id"" = tde.""NroTicket""
                        AND extract(year from rpm.""REPEM04_FechaHora_Pesaje"") = tde.""Año""
                        AND extract(month from rpm.""REPEM04_FechaHora_Pesaje"") = tde.""Mes""
                            AND mic.""REPEM07_Numero_de_Microruta"" = tde.""RutaCodigo""
                            AND mic.""REPEM07_NUAP"" = tde.""NUAP""
                    LEFT JOIN ""REPEM_Toneladas_Rechazos_Agrupadas_Por_Ticket"" tre ON rpm.""REPEM04_Id"" = tre.""Ticket_Asignable""
                             and mic.""REPEM07_NUAP"" = 440405001
                    LEFT JOIN ""REPEM_Tickets_Compensables"" rtc on rpm.""REPEM04_Id"" = rtc.""Nro_Ticket_Compensacion""
                    UNION ALL
                    (with cte_comp AS (
                        SELECT
                            rpm.""REPEM04_Id"" AS ""Id_Ticket"",
                            mic.""REPEM07_NUAP"" AS ""NUAP"",
                            mic.""REPEM07_Numero_Ruta_Intendencia"" AS ""Numero_Ruta_Indendencia"",
                            mic.""REPEM07_Ruta_Larga"" AS ""Ruta_Larga"",
                            ap.""REPEM02_Nombre"" AS ""Area_Aprovechamiento"",
                            CASE
                                WHEN mic.""REPEM07_NUAP"" = 440405001 THEN round((rpm.""REPEM04_PesoTotal_Toneladas"" - coalesce(tr.""Toneladas_Descuento_Medellin"", 0))::numeric, 3)
                                WHEN mic.""REPEM07_NUAP"" = 440705360 AND mic.""REPEM07_Porcentaje_No_Aforado"" <> 0 THEN tr.""Toneladas_Descuento_Medellin""
                                ELSE round(tdha.""Toneladas_Resultantes""::numeric, 3)
                            END AS ""Calculo_Toneladas"",
                            CASE
                                WHEN mic.""REPEM07_Porcentaje_Barrido"" <> 0 THEN round(((CASE
                                    WHEN mic.""REPEM07_NUAP"" = 440405001 THEN round((rpm.""REPEM04_PesoTotal_Toneladas"" - coalesce(tr.""Toneladas_Descuento_Medellin"", 0))::numeric, 3)
                                    WHEN mic.""REPEM07_NUAP"" = 440705360 AND mic.""REPEM07_Porcentaje_No_Aforado"" <> 0 THEN tr.""Toneladas_Descuento_Medellin""
                                    ELSE round(tdha.""Toneladas_Resultantes""::numeric, 3)
                                END) * (mic.""REPEM07_Porcentaje_Barrido"" / 100))::numeric, 3)
                                ELSE 0
                            END AS ""Calculo_Barrido"",
                            0 AS ""Calculo_Peaje"",
                            tdha.""Tipo_Compensacion"" AS ""Tipo_Compensacion""
                        FROM ""REPEM_Detalle_Compensacion_Tickets"" tdha
                            INNER JOIN ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                                ON tdha.""Id_Ticket"" = rpm.""REPEM04_Id""
                            INNER JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                                ON rpm.""REPEM04_RutaCodigo"" = mic.""REPEM07_Numero_de_Microruta""::text and mic.""REPEM07_NUAP"" = tdha.""NUAP""
                            INNER JOIN ""Reporting-Emvarias_02-Areas_de_Aprovechamiento"" ap
                                ON ap.""REPEM02_Codigo"" = mic.""REPEM07_NUAP""
                            LEFT JOIN ""REPEM_Distribuciones_de_Microrutas"" dpm
                                ON dpm.""NUAP"" = mic.""REPEM07_NUAP""
                                   AND dpm.""Año"" = extract(year from rpm.""REPEM04_FechaHora_Pesaje"")
                                   AND dpm.""Mes"" = extract(month from rpm.""REPEM04_FechaHora_Pesaje"")
                            LEFT JOIN ""Reporting-Emvarias_06-Peajes"" pe
                                ON pe.""REPEM06_Placa"" = rpm.""REPEM04_Patente""
                            LEFT JOIN ""REPEM_Descuento_Por_Ticket"" tr
                                ON tr.""Id_Ticket"" = rpm.""REPEM04_Id""
                        WHERE
                            (tdha.""Tipo_Compensacion"" = 'TOTAL' OR tdha.""Tipo_Compensacion"" = 'PARCIAL')
                    )
                    SELECT
                        bs.""NUAP"" AS ""C1_NUAP"",
                        1 AS ""C2_TIPO_SITIO"",
                        720105237 AS ""C3_NUSD"",
                        rpm.""REPEM04_Patente"" AS ""C4_PLACA"",
                        rpm.""REPEM04_FechaHora_Pesaje""::date AS ""C5_FECHA"",
                        rpm.""REPEM04_FechaHora_Pesaje""::time without time zone AS ""C6_HORA"",
                        bs.""Numero_Ruta_Indendencia"" AS ""C7_NUMICRO"",
                        0 AS ""C8_TON_LIMP_URB"",
                        0 AS ""C9_TON_BARRIDO"",
                        CASE
                            WHEN rtc IS NOT NULL THEN bs.""Calculo_Toneladas""
                        ELSE rpm.""REPEM04_PesoTotal_Toneladas""
                        END AS ""C10_TONRESNA"",
                        coalesce(tre.""Toneladas_Rechazadas"", 0) AS ""C11_TONRECHAPR"",
                        CASE
                            WHEN mr.""REPEM07_Porcentaje_Residuos_Aprovechables"" <> 0 THEN round((bs.""Calculo_Toneladas"" * (mr.""REPEM07_Porcentaje_Residuos_Aprovechables"" / 100))::numeric, 3)
                            ELSE 0
                        END AS ""C12_TONRESAPR"",
                        '1' AS ""C13_SISTEMA_MEDICION"",
                        bs.""Calculo_Peaje"" AS ""C14_VLRPEAJ"",
                        rtc.""Nro_Ticket_Compensacion"" AS ""CE_ID_TICKET"",
                        bs.""Area_Aprovechamiento"" AS ""CE_NOMBRE_AREA"",
                        bs.""Ruta_Larga"" AS ""CE_RUTA_LARGA"",
                        rtc.""Maximo_Toneladas_Compensables"" AS ""CE_TON_TOTAL"",
                        bool(rtc is not null) AS ""CE_REL_COMPENSACION"",
                        CASE WHEN rtc IS NOT NULL
                            THEN rtc.""Id_Ticket""
                            ELSE bs.""Id_Ticket""
                        END AS ""CE_REL_COMPENSACION_ID_TICKET""
                    FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                    INNER JOIN cte_comp bs on rpm.""REPEM04_Id"" = bs.""Id_Ticket""
                    INNER JOIN ""Reporting-Emvarias_07-Microrutas"" mr on mr.""REPEM07_Ruta_Larga"" = bs.""Ruta_Larga"" and mr.""REPEM07_NUAP"" = bs.""NUAP""
                    INNER JOIN ""REPEM_Tickets_Compensables"" rtc on rpm.""REPEM04_Id"" = rtc.""Id_Ticket""
                    LEFT JOIN ""REPEM_Toneladas_Rechazos_Agrupadas_Por_Ticket"" tre ON rpm.""REPEM04_Id"" = tre.""Ticket_Asignable""
                        and mr.""REPEM07_NUAP"" = 440405001);
            ");
            
            //12. Vista de Reporte 14 con Aditivos
            migrationBuilder.Sql(@"
                create or replace view ""REPEM_Reporte_SUI_Recolecciones_F14_con_Aditivos"" AS
                    SELECT
                        f14.""C1_NUAP"",
                        f14.""C2_TIPO_SITIO"",
                        f14.""C3_NUSD""::varchar(10) as ""C3_NUSD"",
                        f14.""C4_PLACA"",
                        f14.""C5_FECHA"",
                        f14.""C6_HORA"",
                        f14.""C7_NUMICRO"",
                        f14.""C8_TON_LIMP_URB"",
                        f14.""C9_TON_BARRIDO"",
                        f14.""C10_TONRESNA"",
                        f14.""C11_TONRECHAPR"",
                        f14.""C12_TONRESAPR"",
                        f14.""C13_SISTEMA_MEDICION""::integer as ""C13_SISTEMA_MEDICION"",
                        f14.""C14_VLRPEAJ"",
                        f14.""CE_ID_TICKET"",
                        f14.""CE_NOMBRE_AREA"",
                        f14.""CE_RUTA_LARGA"",
                        f14.""CE_TON_TOTAL"",
                        f14.""CE_REL_COMPENSACION"",
                        f14.""CE_REL_COMPENSACION_ID_TICKET""
                    from ""REPEM_Reporte_SUI_Recolecciones_F14"" f14
                    UNION ALL
                    SELECT
                           clu.""C1_NUAP"" as ""C1_NUAP"",
                           clu.""C2_TIPO_SITIO"" as ""C2_TIPO_SITIO"",
                           clu.""C3_NUSD""::varchar(10) as ""C3_NUSD"",
                           clu.""C4_PLACA"" as ""C4_PLACA"",
                           clu.""C5_FECHA"" as ""C5_FECHA"",
                           clu.""C6_HORA"" as ""C6_HORA"",
                           clu.""C7_NUMICRO"" as ""C7_NUMICRO"",
                           clu.""C8_TON_LIMP_URB"" as ""C8_TON_LIMP_URB"",
                           clu.""C9_TON_BARRIDO"" as ""C9_TON_BARRIDO"",
                           clu.""C10_TONRESNA"" as ""C10_TONRESNA"",
                           clu.""C11_TONRECHAPR"" as ""C11_TONRECHAPR"",
                           clu.""C12_TONRESAPR"" as ""C12_TONRESAPR"",
                           clu.""C13_SISTEMA_MEDICION""::integer as ""C13_SISTEMA_MEDICION"",
                           clu.""C14_VLRPEAJ"" as ""C14_VLRPEAJ"",
                           null as ""CE_ID_TICKET"",
                           'CLUS - CARGADO' as ""CE_NOMBRE_AREA"",
                           clu.""CE_RUTA_LARGA"" as ""CE_RUTA_LARGA"",
                           clu.""CE_TON_TOTAL"" as ""CE_TON_TOTAL"",
                           null as ""CE_REL_COMPENSACION"",
                           null as ""CE_REL_COMPENSACION_ID_TICKET""
                    from ""Reporting-Emvarias_11-Reporte_F14_Adiciones_CLU"" clu
                    UNION ALL
                    SELECT
                           apr.""C1_NUAP"" as ""C1_NUAP"",
                           apr.""C2_TIPO_SITIO"" as ""C2_TIPO_SITIO"",
                           apr.""C3_NUSD""::varchar(10) as ""C3_NUSD"",
                           apr.""C4_PLACA"" as ""C4_PLACA"",
                           apr.""C5_FECHA"" as ""C5_FECHA"",
                           apr.""C6_HORA"" as ""C6_HORA"",
                           apr.""C7_NUMICRO"" as ""C7_NUMICRO"",
                           apr.""C8_TON_LIMP_URB"" as ""C8_TON_LIMP_URB"",
                           apr.""C9_TON_BARRIDO"" as ""C9_TON_BARRIDO"",
                           apr.""C10_TONRESNA"" as ""C10_TONRESNA"",
                           apr.""C11_TONRECHAPR"" as ""C11_TONRECHAPR"",
                           apr.""C12_TONRESAPR"" as ""C12_TONRESAPR"",
                           apr.""C13_SISTEMA_MEDICION""::integer as ""C13_SISTEMA_MEDICION"",
                           apr.""C14_VLRPEAJ"" as ""C14_VLRPEAJ"",
                           null as ""CE_ID_TICKET"",
                           'APROVECHAMIENTO - CARGADO' as ""CE_NOMBRE_AREA"",
                           apr.""CE_RUTA_LARGA"" as ""CE_RUTA_LARGA"",
                           apr.""CE_TON_TOTAL"" as ""CE_TON_TOTAL"",
                           null as ""CE_REL_COMPENSACION"",
                           null as ""CE_REL_COMPENSACION_ID_TICKET""
                    from ""Reporting-Emvarias_12-Reporte_F14_Adiciones_Aprovechamiento"" apr;
            ");
            
            //13. Vista de Toneladas de rechazos sin agrupar por ticket
            migrationBuilder.Sql(@"
                create or replace view ""REPEM_Toneladas_Rechazos_Por_Ticket"" as
                    SELECT rpm.""REPEM04_Id""                              AS ""Id_Ticket"",
                           rpm.""REPEM07_NUAP""                            AS ""NUAP"",
                           round(r.""REPEM09_Toneladas""::numeric, 6)      AS ""Toneladas_Rechazadas"",
                           r.""REPEM09_Fecha_Corta""                       AS ""Fecha_Rechazo"",
                           r.""REPEM09_ECA""                               AS ""Num_ECA""
                    FROM ""Reporting-Emvarias_09-Rechazos"" r
                             INNER JOIN LATERAL ( SELECT irpm.""REPEM04_Id"",
                                                        irpm.""REPEM04_Patente"",
                                                        irpm.""REPEM04_EstadoServicio"",
                                                        irpm.""REPEM04_GrupoTurno"",
                                                        irpm.""REPEM04_TipoServicio"",
                                                        irpm.""REPEM04_RutaCodigo"",
                                                        irpm.""REPEM04_EsRefuerzo"",
                                                        irpm.""REPEM04_Interno"",
                                                        irpm.""REPEM04_PesoTotal"",
                                                        irpm.""REPEM04_IdServicio"",
                                                        irpm.""REPEM04_FechaHora_EntradaRuta"",
                                                        irpm.""REPEM04_FechaHora_SalidaRuta"",
                                                        irpm.""REPEM04_Observaciones"",
                                                        irpm.""REPEM04_PesoTotal_Toneladas"",
                                                        irpm.""REPEM04_FechaHora_Pesaje"",
                                                        irpm.""REPEM04_Fecha_de_Servicio"",
                                                        irpm.""REPEM04_FechaHora_InicioServicio"",
                                                        irpm.""REPEM04_FechaHora_LlegadaBase"",
                                                        irpm.""REPEM04_FechaHora_SalidaBase"",
                                                        mic.""REPEM07_Numero_de_Microruta"",
                                                        mic.""REPEM07_NUAP"",
                                                        mic.""REPEM07_Numero_Ruta_Intendencia"",
                                                        mic.""REPEM07_Ruta_Larga"",
                                                        mic.""REPEM07_Porcentaje_Limpieza_Urbana"",
                                                        mic.""REPEM07_Porcentaje_Barrido"",
                                                        mic.""REPEM07_Porcentaje_No_Aforado"",
                                                        mic.""REPEM07_Porcentaje_Residuos_Aprovechables""
                                                 FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" irpm
                                                          JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                                                               ON irpm.""REPEM04_RutaCodigo""::text = mic.""REPEM07_Numero_de_Microruta""::text
                                                 WHERE r.""REPEM09_Placa""::text = irpm.""REPEM04_Patente""::text
                                                   AND r.""REPEM09_Fecha_Corta"" = irpm.""REPEM04_FechaHora_Pesaje""::date
                                                   AND mic.""REPEM07_Ruta_Larga"" = r.""REPEM09_Ruta_Larga""
                                                 LIMIT 1) rpm ON true;
            ");
            
            //14. Vista de Reporte 34
            migrationBuilder.Sql(@"
                create or replace view ""REPEM_Reporte_SUI_Recolecciones_F34"" AS
                WITH f14dep as (
                    SELECT 720105237                                                  AS ""C1_NUSD"",
                       CASE
                           WHEN pb.""REPEM03_Tipo_de_origen"" = 'Others'::text THEN '4'::bigint
                           WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUAP'::text THEN '1'::bigint
                           WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUET'::text THEN '2'::bigint
                           WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUECA'::text THEN '3'::bigint
                           END                                                    AS ""C2_TIPO_ORIGEN"",
                       CASE
                           WHEN pb.""REPEM03_Tipo_de_origen"" = 'Others'::text THEN NULL
                           ELSE r14.""C1_NUAP""::bigint
                           END                                                    AS ""C3_NUSITIO_ORI"",
                       CASE
                           WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL
                           ELSE cli.""REPEM08_Nombre_Completo""
                           END                                                    AS ""C4_NOMBRE_EMPRESA"",
                       CASE
                           WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL
                           ELSE pb.""REPEM03_Nro_Identificacion_Tributaria""
                           END                                                    AS ""C5_NIT_EMPRESA"",
                       CASE
                           WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL
                           ELSE mun.""REPEM01_Codigo""
                           END                                                    AS ""C6_COD_DANE_ORI"",
                       pb.""REPEM03_Patente""                                       AS ""C7_PLACA"",
                       pb.""REPEM03_Fecha_de_entrada""::date                        AS ""C8_FECHA_INGRESO"",
                       pb.""REPEM03_Fecha_de_egreso""::date                         AS ""C9_FECHA_SALIDA"",
                       pb.""REPEM03_Fecha_de_entrada""::time without time zone      AS ""C10_HORA_INGRESO"",
                       pb.""REPEM03_Fecha_de_egreso""::time without time zone       AS ""C11_HORA_SALIDA"",
                       (r14.""C9_TON_BARRIDO""
                                 + r14.""C10_TONRESNA""
                                 - coalesce(r14.""C11_TONRECHAPR"", 0))::numeric
                                                                                  AS ""C12_TONELADAS"",
                       pb.""REPEM03_Id""                                            AS ""CE_ID_TICKET"",
                       coalesce(tct.""REPEM10_Valor"", 0)                           AS ""CE_VALOR_TICKET_LEGACY"",
                       coalesce(round(tct.""REPEM10_Valor""::numeric / 1000, 2), 0) AS ""CE_VALOR_TON_TICKET_LEGACY"",
                       coalesce(r14.""C11_TONRECHAPR"", 0)                          AS ""CE_TONELADAS_RECHAZADAS"",
                       TRUE                                                       AS ""CE_EXISTE_EN_F14"",
                       pb.""REPEM03_Nro_Identificacion_Tributaria""                 AS ""CE_NIT_EMPRESA"",
                       r14.""C1_NUAP""::bigint                                      AS ""CE_NUAP""
                from ""REPEM_Reporte_SUI_Recolecciones_F14"" r14
                         inner join ""Reporting-Emvarias_03-Pesaje_de_Balanza"" pb
                             on pb.""REPEM03_Id"" = r14.""CE_ID_TICKET""
                                and pb.""REPEM03_Fecha_de_cancelacion"" is null
                         left join ""Reporting-Emvarias_08-Clientes"" cli
                                   on pb.""REPEM03_Nro_Identificacion_Tributaria"" = cli.""REPEM08_NIT""
                         left join ""Reporting-Emvarias_01-Municipios"" mun on pb.""REPEM03_Municipio"" = mun.""REPEM01_Codigo""
                         left join ""Reporting-Emvarias_10-Tickets_de_Servicio_Generados"" tct
                                   on pb.""REPEM03_Id""::bigint = tct.""REPEM10_Id"" and pb.""REPEM03_Fecha_de_cancelacion"" is null
                ),
                base as (
                    SELECT * from f14dep
                    UNION ALL
                        (
                            SELECT 720105237                                              AS ""C1_NUSD"",
                               CASE
                                   WHEN pb.""REPEM03_Tipo_de_origen"" = 'Others'::text THEN '4'::bigint
                                   WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUAP'::text THEN '1'::bigint
                                   WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUET'::text THEN '2'::bigint
                                   WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUECA'::text THEN '3'::bigint
                               END                                                        AS ""C2_TIPO_ORIGEN"",
                               CASE
                                   WHEN pb.""REPEM03_Tipo_de_origen"" = 'Others'::text THEN NULL
                                   ELSE pb.""REPEM03_Nro_Unico_Area_Prestacion""::bigint
                               END                                                    AS ""C3_NUSITIO_ORI"",
                               CASE
                                   WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL
                                   ELSE cli.""REPEM08_Nombre_Completo""
                                   END                                                    AS ""C4_NOMBRE_EMPRESA"",
                               CASE
                                   WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL
                                   ELSE pb.""REPEM03_Nro_Identificacion_Tributaria""
                                   END                                                    AS ""C5_NIT_EMPRESA"",
                               CASE
                                   WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL
                                   ELSE mun.""REPEM01_Codigo""
                                   END                                                    AS ""C6_COD_DANE_ORI"",
                               pb.""REPEM03_Patente""                                       AS ""C7_PLACA"",
                               pb.""REPEM03_Fecha_de_entrada""::date                        AS ""C8_FECHA_INGRESO"",
                               pb.""REPEM03_Fecha_de_egreso""::date                         AS ""C9_FECHA_SALIDA"",
                               pb.""REPEM03_Fecha_de_entrada""::time without time zone      AS ""C10_HORA_INGRESO"",
                               pb.""REPEM03_Fecha_de_egreso""::time without time zone       AS ""C11_HORA_SALIDA"",
                               round(tct.""REPEM10_Valor""::numeric / 1000, 3) -
                                    coalesce(tre.""Toneladas_Rechazadas"", 0)::numeric      AS ""C12_TONELADAS"",
                               pb.""REPEM03_Id""                                            AS ""CE_ID_TICKET"",
                               coalesce(tct.""REPEM10_Valor"", 0)                           AS ""CE_VALOR_TICKET_LEGACY"",
                               coalesce(round(tct.""REPEM10_Valor""::numeric / 1000, 2), 0) AS ""CE_VALOR_TON_TICKET_LEGACY"",
                               coalesce(tre.""Toneladas_Rechazadas"", 0)::numeric           AS ""CE_TONELADAS_RECHAZADAS"",
                               FALSE                                                      AS ""CE_EXISTE_EN_F14"",
                               pb.""REPEM03_Nro_Identificacion_Tributaria""                 AS ""CE_NIT_EMPRESA"",
                               ap.""REPEM02_Codigo""                                        AS ""CE_NUAP""
                            from ""Reporting-Emvarias_10-Tickets_de_Servicio_Generados"" tct
                                    inner join ""Reporting-Emvarias_03-Pesaje_de_Balanza"" pb
                                        on pb.""REPEM03_Id""::bigint = tct.""REPEM10_Id"" and pb.""REPEM03_Fecha_de_cancelacion"" is null
                                    left join ""REPEM_Toneladas_Rechazos_Agrupadas_Por_Ticket"" tre
                                        on (pb.""REPEM03_Id"" = tre.""Ticket_Asignable"" or tre.""Ticket_Asignable""::bigint = tct.""REPEM10_Id"")
                                            and pb.""REPEM03_Nro_Unico_Area_Prestacion"" = 440405001
                                    left join ""Reporting-Emvarias_08-Clientes"" cli
                                        on pb.""REPEM03_Nro_Identificacion_Tributaria"" = cli.""REPEM08_NIT""
                                    left join ""Reporting-Emvarias_01-Municipios"" mun on pb.""REPEM03_Municipio"" = mun.""REPEM01_Codigo""
                                    left join ""Reporting-Emvarias_02-Areas_de_Aprovechamiento"" ap on ap.""REPEM02_Nombre"" = mun.""REPEM01_Nombre""
                                    left join lateral (
                                        select
                                            r14.""CE_EXISTE_EN_F14""
                                        from f14dep r14
                                        where pb.""REPEM03_Id"" = r14.""CE_ID_TICKET""
                                        LIMIT 1
                                    ) as r14 on true
                            where r14 is null
                        )
                ), rch AS (
                    SELECT
                        ""C1_NUSD"",
                        3 AS ""C2_TIPO_ORIGEN"",
                        trept.""Num_ECA"" AS ""C3_NUSITIO_ORI"",
                        ""C4_NOMBRE_EMPRESA"",
                        ""C5_NIT_EMPRESA"",
                        ""C6_COD_DANE_ORI"",
                        ""C7_PLACA"",
                        ""C8_FECHA_INGRESO"",
                        ""C9_FECHA_SALIDA"",
                        ""C10_HORA_INGRESO"",
                        ""C11_HORA_SALIDA"",
                        trept.""Toneladas_Rechazadas"" as ""C12_TONELADAS"",
                        ""CE_ID_TICKET"",
                        ""CE_VALOR_TICKET_LEGACY"",
                        ""CE_VALOR_TON_TICKET_LEGACY"",
                        0 AS ""CE_TONELADAS_RECHAZADAS"",
                        ""CE_EXISTE_EN_F14"",
                        ""CE_NIT_EMPRESA"",
                        trept.""NUAP"" AS ""CE_NUAP""
                    from base bs
                        join ""REPEM_Toneladas_Rechazos_Por_Ticket"" trept
                            on bs.""CE_ID_TICKET"" = trept.""Id_Ticket"" and trept.""NUAP"" = bs.""CE_NUAP""
                        left join ""Reporting-Emvarias_08-Clientes"" cli
                                        on bs.""CE_NIT_EMPRESA"" = cli.""REPEM08_NIT""
                )
                SELECT * from base
                UNION ALL
                SELECT * from rch;
            ");
        }
    }
}
