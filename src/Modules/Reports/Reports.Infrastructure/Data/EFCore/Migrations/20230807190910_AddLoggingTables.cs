using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Reports.Infrastructure.Data.EFCore.Migrations
{
    public partial class AddLoggingTables : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"
				CREATE TABLE IF NOT EXISTS ""Reporting-Emvarias-EventsLog""
				(
		            ""Id"" text COLLATE pg_catalog.""default"",
		            ""Timestamp"" timestamp without time zone,
		            ""RequestPath"" text COLLATE pg_catalog.""default"",
            		""ActionName"" text COLLATE pg_catalog.""default"",
		            ""User"" text COLLATE pg_catalog.""default"",
		            ""Message"" text COLLATE pg_catalog.""default""
	            )
			");
            
            migrationBuilder.Sql(@"
				CREATE TABLE IF NOT EXISTS ""ErrorsLog""
				(
		            ""Id"" text COLLATE pg_catalog.""default"",
		            ""Timestamp"" timestamp without time zone,
		            ""RequestPath"" text COLLATE pg_catalog.""default"",
            		""ActionName"" text COLLATE pg_catalog.""default"",
		            ""User"" text COLLATE pg_catalog.""default"",
		            ""Message"" text COLLATE pg_catalog.""default"",
		            ""Exception"" text COLLATE pg_catalog.""default""
	            )
			");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"DROP TABLE IF EXISTS ""Reporting-Emvarias-EventsLog""");
            migrationBuilder.Sql(@"DROP TABLE IF EXISTS ""ErrorsLog""");
        }
    }
}
