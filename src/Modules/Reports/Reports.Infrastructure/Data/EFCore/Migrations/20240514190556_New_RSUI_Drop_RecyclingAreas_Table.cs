using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Reports.Infrastructure.Data.EFCore.Migrations
{
    public partial class New_RSUI_Drop_RecyclingAreas_Table : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Reporting-Emvarias_02-Areas_de_Aprovechamiento");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Reporting-Emvarias_02-Areas_de_Aprovechamiento",
                columns: table => new
                {
                    REPEM02_Codigo = table.Column<string>(type: "text", nullable: false),
                    REPEM02_Nombre = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("Reporting-Emvarias_02-Areas_de_Aprovechamiento_key", x => x.REPEM02_Codigo);
                });
        }
    }
}
