using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Reports.Infrastructure.Data.EFCore.Migrations
{
    public partial class Fix_Performance_Vistas_Materializadas_Y_Scheduled_Job : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            //Configure command timeout
            
            
            //Se borran las vistas normales
            migrationBuilder.Sql(@"
                drop view if exists ""REPEM_Reporte_SUI_Recolecciones_F14_con_Aditivos"";
                drop view if exists ""REPEM_Reporte_SUI_Recolecciones_F34"";
            ");

            //Se crean las vistas materializadas
            
            //F14 Aditivos
            migrationBuilder.Sql(@"
                create materialized view ""REPEM_Reporte_SUI_Recolecciones_F14_con_Aditivos""
                            (""C1_NUAP"", ""C2_TIPO_SITIO"", ""C3_NUSD"", ""C4_PLACA"", ""C5_FECHA"", ""C6_HORA"", ""C7_NUMICRO"", ""C8_TON_LIMP_URB"",
                             ""C9_TON_BARRIDO"", ""C10_TONRESNA"", ""C11_TONRECHAPR"", ""C12_TONRESAPR"", ""C13_SISTEMA_MEDICION"", ""C14_VLRPEAJ"",
                             ""CE_ID_TICKET"", ""CE_NOMBRE_AREA"", ""CE_RUTA_LARGA"", ""CE_TON_TOTAL"", ""CE_REL_COMPENSACION"",
                             ""CE_REL_COMPENSACION_ID_TICKET"")
                as
                SELECT f14.""C1_NUAP"",
                       f14.""C2_TIPO_SITIO"",
                       f14.""C3_NUSD""::character varying(10) AS ""C3_NUSD"",
                       f14.""C4_PLACA"",
                       f14.""C5_FECHA"",
                       f14.""C6_HORA"",
                       f14.""C7_NUMICRO"",
                       f14.""C8_TON_LIMP_URB""::numeric       AS ""C8_TON_LIMP_URB"",
                       f14.""C9_TON_BARRIDO"",
                       f14.""C10_TONRESNA"",
                       f14.""C11_TONRECHAPR"",
                       f14.""C12_TONRESAPR"",
                       f14.""C13_SISTEMA_MEDICION""::integer  AS ""C13_SISTEMA_MEDICION"",
                       f14.""C14_VLRPEAJ"",
                       f14.""CE_ID_TICKET"",
                       f14.""CE_NOMBRE_AREA"",
                       f14.""CE_RUTA_LARGA"",
                       f14.""CE_TON_TOTAL""::numeric          AS ""CE_TON_TOTAL"",
                       f14.""CE_REL_COMPENSACION"",
                       f14.""CE_REL_COMPENSACION_ID_TICKET""
                FROM ""REPEM_Reporte_SUI_Recolecciones_F14"" f14
                UNION ALL
                SELECT clu.""C1_NUAP"",
                       clu.""C2_TIPO_SITIO"",
                       clu.""C3_NUSD""::character varying(10) AS ""C3_NUSD"",
                       clu.""C4_PLACA"",
                       clu.""C5_FECHA"",
                       clu.""C6_HORA"",
                       clu.""C7_NUMICRO"",
                       clu.""C8_TON_LIMP_URB""::numeric       AS ""C8_TON_LIMP_URB"",
                       clu.""C9_TON_BARRIDO""::numeric        AS ""C9_TON_BARRIDO"",
                       clu.""C10_TONRESNA""::numeric          AS ""C10_TONRESNA"",
                       clu.""C11_TONRECHAPR""::numeric        AS ""C11_TONRECHAPR"",
                       clu.""C12_TONRESAPR""::numeric         AS ""C12_TONRESAPR"",
                       clu.""C13_SISTEMA_MEDICION"",
                       clu.""C14_VLRPEAJ""::numeric           AS ""C14_VLRPEAJ"",
                       NULL::bigint                         AS ""CE_ID_TICKET"",
                       'CLUS - CARGADO'::character varying  AS ""CE_NOMBRE_AREA"",
                       clu.""CE_RUTA_LARGA"",
                       clu.""CE_TON_TOTAL""::numeric          AS ""CE_TON_TOTAL"",
                       NULL::boolean                        AS ""CE_REL_COMPENSACION"",
                       NULL::bigint                         AS ""CE_REL_COMPENSACION_ID_TICKET""
                FROM ""Reporting-Emvarias_11-Reporte_F14_Adiciones_CLU"" clu
                UNION ALL
                SELECT apr.""C1_NUAP"",
                       apr.""C2_TIPO_SITIO"",
                       apr.""C3_NUSD""::character varying(10)           AS ""C3_NUSD"",
                       apr.""C4_PLACA"",
                       apr.""C5_FECHA"",
                       apr.""C6_HORA"",
                       apr.""C7_NUMICRO"",
                       apr.""C8_TON_LIMP_URB""::numeric                 AS ""C8_TON_LIMP_URB"",
                       apr.""C9_TON_BARRIDO""::numeric                  AS ""C9_TON_BARRIDO"",
                       apr.""C10_TONRESNA""::numeric                    AS ""C10_TONRESNA"",
                       apr.""C11_TONRECHAPR""::numeric                  AS ""C11_TONRECHAPR"",
                       apr.""C12_TONRESAPR""::numeric                   AS ""C12_TONRESAPR"",
                       apr.""C13_SISTEMA_MEDICION"",
                       apr.""C14_VLRPEAJ""::numeric                     AS ""C14_VLRPEAJ"",
                       NULL::bigint                                   AS ""CE_ID_TICKET"",
                       'APROVECHAMIENTO - CARGADO'::character varying AS ""CE_NOMBRE_AREA"",
                       apr.""CE_RUTA_LARGA"",
                       apr.""CE_TON_TOTAL""::numeric                    AS ""CE_TON_TOTAL"",
                       NULL::boolean                                  AS ""CE_REL_COMPENSACION"",
                       NULL::bigint                                   AS ""CE_REL_COMPENSACION_ID_TICKET""
                FROM ""Reporting-Emvarias_12-Reporte_F14_Adiciones_Aprovechamiento"" apr;
            ");
            
            //Se crea la clave primaria
            migrationBuilder.Sql(@"
                CREATE UNIQUE INDEX ""idx_REPEM_Reporte_SUI_Recolecciones_F14_con_Aditivos_view_pk""
                ON ""REPEM_Reporte_SUI_Recolecciones_F14_con_Aditivos"" (""CE_ID_TICKET"", ""CE_NOMBRE_AREA"", ""CE_REL_COMPENSACION_ID_TICKET"");
            ");
            
            //F34
            migrationBuilder.Sql(@"
                create materialized view ""REPEM_Reporte_SUI_Recolecciones_F34""
                            (""C1_NUSD"", ""C2_TIPO_ORIGEN"", ""C3_NUSITIO_ORI"", ""C4_NOMBRE_EMPRESA"", ""C5_NIT_EMPRESA"", ""C6_COD_DANE_ORI"",
                             ""C7_PLACA"", ""C8_FECHA_INGRESO"", ""C9_FECHA_SALIDA"", ""C10_HORA_INGRESO"", ""C11_HORA_SALIDA"", ""C12_TONELADAS"",
                             ""CE_ID_TICKET"", ""CE_VALOR_TICKET_LEGACY"", ""CE_VALOR_TON_TICKET_LEGACY"", ""CE_TONELADAS_RECHAZADAS"",
                             ""CE_EXISTE_EN_F14"", ""CE_NIT_EMPRESA"", ""CE_NUAP"", ""CE_FECHA_FILTRO"")
                as
                WITH combined_data AS (SELECT 720105237                                                                              AS ""C1_NUSD"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" = 'Others'::text THEN '4'::bigint
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUAP'::text THEN '1'::bigint
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUET'::text THEN '2'::bigint
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUECA'::text THEN '3'::bigint
                                                  ELSE NULL::bigint
                                                  END                                                                                AS ""C2_TIPO_ORIGEN"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" = 'Others'::text THEN NULL::bigint
                                                  ELSE r14.""C1_NUAP""
                                                  END                                                                                AS ""C3_NUSITIO_ORI"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                                                  ELSE cli.""REPEM08_Nombre_Completo""
                                                  END                                                                                AS ""C4_NOMBRE_EMPRESA"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::bigint
                                                  ELSE pb.""REPEM03_Nro_Identificacion_Tributaria""
                                                  END                                                                                AS ""C5_NIT_EMPRESA"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                                                  ELSE mun.""REPEM01_Codigo""
                                                  END                                                                                AS ""C6_COD_DANE_ORI"",
                                              pb.""REPEM03_Patente""                                                                   AS ""C7_PLACA"",
                                              pb.""REPEM03_Fecha_de_entrada""::date                                                    AS ""C8_FECHA_INGRESO"",
                                              pb.""REPEM03_Fecha_de_egreso""::date                                                     AS ""C9_FECHA_SALIDA"",
                                              pb.""REPEM03_Fecha_de_entrada""::time without time zone                                  AS ""C10_HORA_INGRESO"",
                                              pb.""REPEM03_Fecha_de_egreso""::time without time zone                                   AS ""C11_HORA_SALIDA"",
                                              r14.""C9_TON_BARRIDO"" + r14.""C10_TONRESNA"" -
                                              COALESCE(r14.""C11_TONRECHAPR"", 0::numeric)                                             AS ""C12_TONELADAS"",
                                              pb.""REPEM03_Id""                                                                        AS ""CE_ID_TICKET"",
                                              COALESCE(tct.""REPEM10_Valor"", 0::numeric)                                              AS ""CE_VALOR_TICKET_LEGACY"",
                                              COALESCE(tct.""REPEM10_Valor""::numeric / 1000::numeric, 0::numeric)                     AS ""CE_VALOR_TON_TICKET_LEGACY"",
                                              COALESCE(r14.""C11_TONRECHAPR"", 0::numeric)                                             AS ""CE_TONELADAS_RECHAZADAS"",
                                              true                                                                                   AS ""CE_EXISTE_EN_F14"",
                                              pb.""REPEM03_Nro_Identificacion_Tributaria""                                             AS ""CE_NIT_EMPRESA"",
                                              r14.""C1_NUAP""                                                                          AS ""CE_NUAP"",
                                              r14.""C5_FECHA""                                                                         AS ""CE_FECHA_FILTRO""
                                       FROM ""REPEM_Reporte_SUI_Recolecciones_F14"" r14
                                                JOIN ""Reporting-Emvarias_03-Pesaje_de_Balanza"" pb
                                                     ON pb.""REPEM03_Id"" = r14.""CE_ID_TICKET"" AND
                                                        pb.""REPEM03_Fecha_de_cancelacion"" IS NULL
                                                LEFT JOIN ""Reporting-Emvarias_08-Clientes"" cli
                                                          ON pb.""REPEM03_Nro_Identificacion_Tributaria"" = cli.""REPEM08_NIT""
                                                LEFT JOIN ""Reporting-Emvarias_01-Municipios"" mun
                                                          ON pb.""REPEM03_Municipio""::text = mun.""REPEM01_Codigo""::text
                                                LEFT JOIN ""Reporting-Emvarias_10-Tickets_de_Servicio_Generados"" tct
                                                          ON pb.""REPEM03_Id"" = tct.""REPEM10_Id"" AND
                                                             pb.""REPEM03_Fecha_de_cancelacion"" IS NULL
                                       UNION ALL
                                       SELECT 720105237                                                          AS ""C1_NUSD"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" = 'Others'::text THEN '4'::bigint
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUAP'::text THEN '1'::bigint
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUET'::text THEN '2'::bigint
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUECA'::text THEN '3'::bigint
                                                  ELSE NULL::bigint
                                                  END                                                            AS ""C2_TIPO_ORIGEN"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" = 'Others'::text THEN NULL::bigint
                                                  ELSE pb.""REPEM03_Nro_Unico_Area_Prestacion""
                                                  END                                                            AS ""C3_NUSITIO_ORI"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                                                  ELSE cli.""REPEM08_Nombre_Completo""
                                                  END                                                            AS ""C4_NOMBRE_EMPRESA"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::bigint
                                                  ELSE pb.""REPEM03_Nro_Identificacion_Tributaria""
                                                  END                                                            AS ""C5_NIT_EMPRESA"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                                                  ELSE mun.""REPEM01_Codigo""
                                                  END                                                            AS ""C6_COD_DANE_ORI"",
                                              pb.""REPEM03_Patente""                                               AS ""C7_PLACA"",
                                              pb.""REPEM03_Fecha_de_entrada""::date                                AS ""C8_FECHA_INGRESO"",
                                              pb.""REPEM03_Fecha_de_egreso""::date                                 AS ""C9_FECHA_SALIDA"",
                                              pb.""REPEM03_Fecha_de_entrada""::time without time zone              AS ""C10_HORA_INGRESO"",
                                              pb.""REPEM03_Fecha_de_egreso""::time without time zone               AS ""C11_HORA_SALIDA"",
                                              tct.""REPEM10_Valor""::numeric / 1000::numeric -
                                              COALESCE(tre.""Toneladas_Rechazadas"", 0::numeric)                   AS ""C12_TONELADAS"",
                                              pb.""REPEM03_Id""                                                    AS ""CE_ID_TICKET"",
                                              COALESCE(tct.""REPEM10_Valor"", 0::numeric)                          AS ""CE_VALOR_TICKET_LEGACY"",
                                              COALESCE(tct.""REPEM10_Valor""::numeric / 1000::numeric, 0::numeric) AS ""CE_VALOR_TON_TICKET_LEGACY"",
                                              COALESCE(tre.""Toneladas_Rechazadas"", 0::numeric)                   AS ""CE_TONELADAS_RECHAZADAS"",
                                              false                                                              AS ""CE_EXISTE_EN_F14"",
                                              pb.""REPEM03_Nro_Identificacion_Tributaria""                         AS ""CE_NIT_EMPRESA"",
                                              pb.""REPEM03_Nro_Unico_Area_Prestacion""                             AS ""CE_NUAP"",
                                              pb.""REPEM03_Fecha_de_entrada""::date                                AS ""CE_FECHA_FILTRO""
                                       FROM ""Reporting-Emvarias_03-Pesaje_de_Balanza"" pb
                                                LEFT JOIN ""Reporting-Emvarias_10-Tickets_de_Servicio_Generados"" tct
                                                          ON pb.""REPEM03_Id"" = tct.""REPEM10_Id""
                                                LEFT JOIN ""REPEM_Toneladas_Rechazos_Agrupadas_Por_Ticket"" tre
                                                          ON pb.""REPEM03_Id"" = tre.""Ticket_Asignable"" AND
                                                             pb.""REPEM03_Nro_Unico_Area_Prestacion"" = 440405001
                                                LEFT JOIN ""Reporting-Emvarias_08-Clientes"" cli
                                                          ON pb.""REPEM03_Nro_Identificacion_Tributaria"" = cli.""REPEM08_NIT""
                                                LEFT JOIN ""Reporting-Emvarias_01-Municipios"" mun
                                                          ON pb.""REPEM03_Municipio""::text = mun.""REPEM01_Codigo""::text
                                       WHERE pb.""REPEM03_Fecha_de_cancelacion"" IS NULL
                                         AND NOT (pb.""REPEM03_Id"" IN (SELECT DISTINCT rpm.""REPEM04_Id"" AS id
                                                                      FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                                                                               JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                                                                                    ON rpm.""REPEM04_RutaCodigo""::text =
                                                                                       mic.""REPEM07_Numero_de_Microruta""::text))
                                       UNION ALL
                                       SELECT 720105237                                                          AS ""C1_NUSD"",
                                              '3'::bigint                                                        AS ""C2_TIPO_ORIGEN"",
                                              trept.""Num_ECA""                                                    AS ""C3_NUSITIO_ORI"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                                                  ELSE cli.""REPEM08_Nombre_Completo""
                                                  END                                                            AS ""C4_NOMBRE_EMPRESA"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::bigint
                                                  ELSE pb.""REPEM03_Nro_Identificacion_Tributaria""
                                                  END                                                            AS ""C5_NIT_EMPRESA"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                                                  ELSE mun.""REPEM01_Codigo""
                                                  END                                                            AS ""C6_COD_DANE_ORI"",
                                              pb.""REPEM03_Patente""                                               AS ""C7_PLACA"",
                                              pb.""REPEM03_Fecha_de_entrada""::date                                AS ""C8_FECHA_INGRESO"",
                                              pb.""REPEM03_Fecha_de_egreso""::date                                 AS ""C9_FECHA_SALIDA"",
                                              pb.""REPEM03_Fecha_de_entrada""::time without time zone              AS ""C10_HORA_INGRESO"",
                                              pb.""REPEM03_Fecha_de_egreso""::time without time zone               AS ""C11_HORA_SALIDA"",
                                              COALESCE(trept.""Toneladas_Rechazadas""::numeric, 0::numeric)        AS ""C12_TONELADAS"",
                                              pb.""REPEM03_Id""                                                    AS ""CE_ID_TICKET"",
                                              COALESCE(tct.""REPEM10_Valor"", 0::numeric)                          AS ""CE_VALOR_TICKET_LEGACY"",
                                              COALESCE(tct.""REPEM10_Valor""::numeric / 1000::numeric, 0::numeric) AS ""CE_VALOR_TON_TICKET_LEGACY"",
                                              0                                                                  AS ""CE_TONELADAS_RECHAZADAS"",
                                              true                                                               AS ""CE_EXISTE_EN_F14"",
                                              pb.""REPEM03_Nro_Identificacion_Tributaria""                         AS ""CE_NIT_EMPRESA"",
                                              440405001                                                          AS ""CE_NUAP"",
                                              r14.""C5_FECHA""                                                     AS ""CE_FECHA_FILTRO""
                                       FROM ""REPEM_Reporte_SUI_Recolecciones_F14"" r14
                                                JOIN ""REPEM_Toneladas_Rechazos_Por_Ticket"" trept
                                                     ON r14.""CE_ID_TICKET"" = trept.""Id_Ticket"" AND trept.""NUAP"" = r14.""C1_NUAP""
                                                JOIN ""Reporting-Emvarias_03-Pesaje_de_Balanza"" pb
                                                     ON pb.""REPEM03_Id"" = r14.""CE_ID_TICKET"" AND
                                                        pb.""REPEM03_Fecha_de_cancelacion"" IS NULL
                                                LEFT JOIN ""Reporting-Emvarias_08-Clientes"" cli
                                                          ON pb.""REPEM03_Nro_Identificacion_Tributaria"" = cli.""REPEM08_NIT""
                                                LEFT JOIN ""Reporting-Emvarias_01-Municipios"" mun
                                                          ON pb.""REPEM03_Municipio""::text = mun.""REPEM01_Codigo""::text
                                                LEFT JOIN ""Reporting-Emvarias_10-Tickets_de_Servicio_Generados"" tct
                                                          ON pb.""REPEM03_Id"" = tct.""REPEM10_Id"" AND
                                                             pb.""REPEM03_Fecha_de_cancelacion"" IS NULL)
                SELECT ""C1_NUSD"",
                       ""C2_TIPO_ORIGEN"",
                       ""C3_NUSITIO_ORI"",
                       ""C4_NOMBRE_EMPRESA"",
                       ""C5_NIT_EMPRESA"",
                       ""C6_COD_DANE_ORI"",
                       ""C7_PLACA"",
                       ""C8_FECHA_INGRESO"",
                       ""C9_FECHA_SALIDA"",
                       ""C10_HORA_INGRESO"",
                       ""C11_HORA_SALIDA"",
                       COALESCE(""C12_TONELADAS"", 0::numeric) AS ""C12_TONELADAS"",
                       ""CE_ID_TICKET"",
                       ""CE_VALOR_TICKET_LEGACY"",
                       ""CE_VALOR_TON_TICKET_LEGACY"",
                       ""CE_TONELADAS_RECHAZADAS"",
                       ""CE_EXISTE_EN_F14"",
                       ""CE_NIT_EMPRESA"",
                       ""CE_NUAP"",
                       ""CE_FECHA_FILTRO""
                FROM combined_data;
            ");
            
            //Se crea la clave primaria
            migrationBuilder.Sql(@"
                CREATE UNIQUE INDEX ""idx_REPEM_Reporte_SUI_Recolecciones_F34_view_pk""
                ON ""REPEM_Reporte_SUI_Recolecciones_F34"" (""CE_ID_TICKET"", ""CE_NUAP"", ""C6_COD_DANE_ORI"");
            ");
            
            //Se crea el scheduled job
            migrationBuilder.Sql(@"
                SELECT cron.schedule(
                    'JOB_REPEM_Reportes_SUI_F14_F34',
                    '0 * * * *',
                    'REFRESH MATERIALIZED VIEW CONCURRENTLY REPEM_Reporte_SUI_Recolecciones_F14_con_Aditivos; REFRESH MATERIALIZED VIEW CONCURRENTLY REPEM_Reporte_SUI_Recolecciones_F34;'
                );
            ");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            //Se borra el scheduled job
            migrationBuilder.Sql(@"
                SELECT cron.unschedule('JOB_REPEM_Reportes_SUI_F14_F34');
            ");
            
            //Se borran las vistas materializadas
            migrationBuilder.Sql(@"
                drop materialized view ""REPEM_Reporte_SUI_Recolecciones_F14_con_Aditivos"";
                drop materialized view ""REPEM_Reporte_SUI_Recolecciones_F34"";
            ");
            
            //Se crean las vistas normales
            
            //F14 Aditivos
            migrationBuilder.Sql(@"
                create or replace view ""REPEM_Reporte_SUI_Recolecciones_F14_con_Aditivos""
                            (""C1_NUAP"", ""C2_TIPO_SITIO"", ""C3_NUSD"", ""C4_PLACA"", ""C5_FECHA"", ""C6_HORA"", ""C7_NUMICRO"", ""C8_TON_LIMP_URB"",
                             ""C9_TON_BARRIDO"", ""C10_TONRESNA"", ""C11_TONRECHAPR"", ""C12_TONRESAPR"", ""C13_SISTEMA_MEDICION"", ""C14_VLRPEAJ"",
                             ""CE_ID_TICKET"", ""CE_NOMBRE_AREA"", ""CE_RUTA_LARGA"", ""CE_TON_TOTAL"", ""CE_REL_COMPENSACION"",
                             ""CE_REL_COMPENSACION_ID_TICKET"")
                as
                SELECT f14.""C1_NUAP"",
                       f14.""C2_TIPO_SITIO"",
                       f14.""C3_NUSD""::character varying(10) AS ""C3_NUSD"",
                       f14.""C4_PLACA"",
                       f14.""C5_FECHA"",
                       f14.""C6_HORA"",
                       f14.""C7_NUMICRO"",
                       f14.""C8_TON_LIMP_URB""::numeric       AS ""C8_TON_LIMP_URB"",
                       f14.""C9_TON_BARRIDO"",
                       f14.""C10_TONRESNA"",
                       f14.""C11_TONRECHAPR"",
                       f14.""C12_TONRESAPR"",
                       f14.""C13_SISTEMA_MEDICION""::integer  AS ""C13_SISTEMA_MEDICION"",
                       f14.""C14_VLRPEAJ"",
                       f14.""CE_ID_TICKET"",
                       f14.""CE_NOMBRE_AREA"",
                       f14.""CE_RUTA_LARGA"",
                       f14.""CE_TON_TOTAL""::numeric          AS ""CE_TON_TOTAL"",
                       f14.""CE_REL_COMPENSACION"",
                       f14.""CE_REL_COMPENSACION_ID_TICKET""
                FROM ""REPEM_Reporte_SUI_Recolecciones_F14"" f14
                UNION ALL
                SELECT clu.""C1_NUAP"",
                       clu.""C2_TIPO_SITIO"",
                       clu.""C3_NUSD""::character varying(10) AS ""C3_NUSD"",
                       clu.""C4_PLACA"",
                       clu.""C5_FECHA"",
                       clu.""C6_HORA"",
                       clu.""C7_NUMICRO"",
                       clu.""C8_TON_LIMP_URB""::numeric       AS ""C8_TON_LIMP_URB"",
                       clu.""C9_TON_BARRIDO""::numeric        AS ""C9_TON_BARRIDO"",
                       clu.""C10_TONRESNA""::numeric          AS ""C10_TONRESNA"",
                       clu.""C11_TONRECHAPR""::numeric        AS ""C11_TONRECHAPR"",
                       clu.""C12_TONRESAPR""::numeric         AS ""C12_TONRESAPR"",
                       clu.""C13_SISTEMA_MEDICION"",
                       clu.""C14_VLRPEAJ""::numeric           AS ""C14_VLRPEAJ"",
                       NULL::bigint                         AS ""CE_ID_TICKET"",
                       'CLUS - CARGADO'::character varying  AS ""CE_NOMBRE_AREA"",
                       clu.""CE_RUTA_LARGA"",
                       clu.""CE_TON_TOTAL""::numeric          AS ""CE_TON_TOTAL"",
                       NULL::boolean                        AS ""CE_REL_COMPENSACION"",
                       NULL::bigint                         AS ""CE_REL_COMPENSACION_ID_TICKET""
                FROM ""Reporting-Emvarias_11-Reporte_F14_Adiciones_CLU"" clu
                UNION ALL
                SELECT apr.""C1_NUAP"",
                       apr.""C2_TIPO_SITIO"",
                       apr.""C3_NUSD""::character varying(10)           AS ""C3_NUSD"",
                       apr.""C4_PLACA"",
                       apr.""C5_FECHA"",
                       apr.""C6_HORA"",
                       apr.""C7_NUMICRO"",
                       apr.""C8_TON_LIMP_URB""::numeric                 AS ""C8_TON_LIMP_URB"",
                       apr.""C9_TON_BARRIDO""::numeric                  AS ""C9_TON_BARRIDO"",
                       apr.""C10_TONRESNA""::numeric                    AS ""C10_TONRESNA"",
                       apr.""C11_TONRECHAPR""::numeric                  AS ""C11_TONRECHAPR"",
                       apr.""C12_TONRESAPR""::numeric                   AS ""C12_TONRESAPR"",
                       apr.""C13_SISTEMA_MEDICION"",
                       apr.""C14_VLRPEAJ""::numeric                     AS ""C14_VLRPEAJ"",
                       NULL::bigint                                   AS ""CE_ID_TICKET"",
                       'APROVECHAMIENTO - CARGADO'::character varying AS ""CE_NOMBRE_AREA"",
                       apr.""CE_RUTA_LARGA"",
                       apr.""CE_TON_TOTAL""::numeric                    AS ""CE_TON_TOTAL"",
                       NULL::boolean                                  AS ""CE_REL_COMPENSACION"",
                       NULL::bigint                                   AS ""CE_REL_COMPENSACION_ID_TICKET""
                FROM ""Reporting-Emvarias_12-Reporte_F14_Adiciones_Aprovechamiento"" apr;
            ");
            
            //F34
            migrationBuilder.Sql(@"
                create or replace view ""REPEM_Reporte_SUI_Recolecciones_F34""
                            (""C1_NUSD"", ""C2_TIPO_ORIGEN"", ""C3_NUSITIO_ORI"", ""C4_NOMBRE_EMPRESA"", ""C5_NIT_EMPRESA"", ""C6_COD_DANE_ORI"",
                             ""C7_PLACA"", ""C8_FECHA_INGRESO"", ""C9_FECHA_SALIDA"", ""C10_HORA_INGRESO"", ""C11_HORA_SALIDA"", ""C12_TONELADAS"",
                             ""CE_ID_TICKET"", ""CE_VALOR_TICKET_LEGACY"", ""CE_VALOR_TON_TICKET_LEGACY"", ""CE_TONELADAS_RECHAZADAS"",
                             ""CE_EXISTE_EN_F14"", ""CE_NIT_EMPRESA"", ""CE_NUAP"", ""CE_FECHA_FILTRO"")
                as
                WITH combined_data AS (SELECT 720105237                                                                              AS ""C1_NUSD"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" = 'Others'::text THEN '4'::bigint
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUAP'::text THEN '1'::bigint
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUET'::text THEN '2'::bigint
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUECA'::text THEN '3'::bigint
                                                  ELSE NULL::bigint
                                                  END                                                                                AS ""C2_TIPO_ORIGEN"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" = 'Others'::text THEN NULL::bigint
                                                  ELSE r14.""C1_NUAP""
                                                  END                                                                                AS ""C3_NUSITIO_ORI"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                                                  ELSE cli.""REPEM08_Nombre_Completo""
                                                  END                                                                                AS ""C4_NOMBRE_EMPRESA"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::bigint
                                                  ELSE pb.""REPEM03_Nro_Identificacion_Tributaria""
                                                  END                                                                                AS ""C5_NIT_EMPRESA"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                                                  ELSE mun.""REPEM01_Codigo""
                                                  END                                                                                AS ""C6_COD_DANE_ORI"",
                                              pb.""REPEM03_Patente""                                                                   AS ""C7_PLACA"",
                                              pb.""REPEM03_Fecha_de_entrada""::date                                                    AS ""C8_FECHA_INGRESO"",
                                              pb.""REPEM03_Fecha_de_egreso""::date                                                     AS ""C9_FECHA_SALIDA"",
                                              pb.""REPEM03_Fecha_de_entrada""::time without time zone                                  AS ""C10_HORA_INGRESO"",
                                              pb.""REPEM03_Fecha_de_egreso""::time without time zone                                   AS ""C11_HORA_SALIDA"",
                                              r14.""C9_TON_BARRIDO"" + r14.""C10_TONRESNA"" -
                                              COALESCE(r14.""C11_TONRECHAPR"", 0::numeric)                                             AS ""C12_TONELADAS"",
                                              pb.""REPEM03_Id""                                                                        AS ""CE_ID_TICKET"",
                                              COALESCE(tct.""REPEM10_Valor"", 0::numeric)                                              AS ""CE_VALOR_TICKET_LEGACY"",
                                              COALESCE(tct.""REPEM10_Valor""::numeric / 1000::numeric, 0::numeric)                     AS ""CE_VALOR_TON_TICKET_LEGACY"",
                                              COALESCE(r14.""C11_TONRECHAPR"", 0::numeric)                                             AS ""CE_TONELADAS_RECHAZADAS"",
                                              true                                                                                   AS ""CE_EXISTE_EN_F14"",
                                              pb.""REPEM03_Nro_Identificacion_Tributaria""                                             AS ""CE_NIT_EMPRESA"",
                                              r14.""C1_NUAP""                                                                          AS ""CE_NUAP"",
                                              r14.""C5_FECHA""                                                                         AS ""CE_FECHA_FILTRO""
                                       FROM ""REPEM_Reporte_SUI_Recolecciones_F14"" r14
                                                JOIN ""Reporting-Emvarias_03-Pesaje_de_Balanza"" pb
                                                     ON pb.""REPEM03_Id"" = r14.""CE_ID_TICKET"" AND
                                                        pb.""REPEM03_Fecha_de_cancelacion"" IS NULL
                                                LEFT JOIN ""Reporting-Emvarias_08-Clientes"" cli
                                                          ON pb.""REPEM03_Nro_Identificacion_Tributaria"" = cli.""REPEM08_NIT""
                                                LEFT JOIN ""Reporting-Emvarias_01-Municipios"" mun
                                                          ON pb.""REPEM03_Municipio""::text = mun.""REPEM01_Codigo""::text
                                                LEFT JOIN ""Reporting-Emvarias_10-Tickets_de_Servicio_Generados"" tct
                                                          ON pb.""REPEM03_Id"" = tct.""REPEM10_Id"" AND
                                                             pb.""REPEM03_Fecha_de_cancelacion"" IS NULL
                                       UNION ALL
                                       SELECT 720105237                                                          AS ""C1_NUSD"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" = 'Others'::text THEN '4'::bigint
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUAP'::text THEN '1'::bigint
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUET'::text THEN '2'::bigint
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUECA'::text THEN '3'::bigint
                                                  ELSE NULL::bigint
                                                  END                                                            AS ""C2_TIPO_ORIGEN"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" = 'Others'::text THEN NULL::bigint
                                                  ELSE pb.""REPEM03_Nro_Unico_Area_Prestacion""
                                                  END                                                            AS ""C3_NUSITIO_ORI"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                                                  ELSE cli.""REPEM08_Nombre_Completo""
                                                  END                                                            AS ""C4_NOMBRE_EMPRESA"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::bigint
                                                  ELSE pb.""REPEM03_Nro_Identificacion_Tributaria""
                                                  END                                                            AS ""C5_NIT_EMPRESA"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                                                  ELSE mun.""REPEM01_Codigo""
                                                  END                                                            AS ""C6_COD_DANE_ORI"",
                                              pb.""REPEM03_Patente""                                               AS ""C7_PLACA"",
                                              pb.""REPEM03_Fecha_de_entrada""::date                                AS ""C8_FECHA_INGRESO"",
                                              pb.""REPEM03_Fecha_de_egreso""::date                                 AS ""C9_FECHA_SALIDA"",
                                              pb.""REPEM03_Fecha_de_entrada""::time without time zone              AS ""C10_HORA_INGRESO"",
                                              pb.""REPEM03_Fecha_de_egreso""::time without time zone               AS ""C11_HORA_SALIDA"",
                                              tct.""REPEM10_Valor""::numeric / 1000::numeric -
                                              COALESCE(tre.""Toneladas_Rechazadas"", 0::numeric)                   AS ""C12_TONELADAS"",
                                              pb.""REPEM03_Id""                                                    AS ""CE_ID_TICKET"",
                                              COALESCE(tct.""REPEM10_Valor"", 0::numeric)                          AS ""CE_VALOR_TICKET_LEGACY"",
                                              COALESCE(tct.""REPEM10_Valor""::numeric / 1000::numeric, 0::numeric) AS ""CE_VALOR_TON_TICKET_LEGACY"",
                                              COALESCE(tre.""Toneladas_Rechazadas"", 0::numeric)                   AS ""CE_TONELADAS_RECHAZADAS"",
                                              false                                                              AS ""CE_EXISTE_EN_F14"",
                                              pb.""REPEM03_Nro_Identificacion_Tributaria""                         AS ""CE_NIT_EMPRESA"",
                                              pb.""REPEM03_Nro_Unico_Area_Prestacion""                             AS ""CE_NUAP"",
                                              pb.""REPEM03_Fecha_de_entrada""::date                                AS ""CE_FECHA_FILTRO""
                                       FROM ""Reporting-Emvarias_03-Pesaje_de_Balanza"" pb
                                                LEFT JOIN ""Reporting-Emvarias_10-Tickets_de_Servicio_Generados"" tct
                                                          ON pb.""REPEM03_Id"" = tct.""REPEM10_Id""
                                                LEFT JOIN ""REPEM_Toneladas_Rechazos_Agrupadas_Por_Ticket"" tre
                                                          ON pb.""REPEM03_Id"" = tre.""Ticket_Asignable"" AND
                                                             pb.""REPEM03_Nro_Unico_Area_Prestacion"" = 440405001
                                                LEFT JOIN ""Reporting-Emvarias_08-Clientes"" cli
                                                          ON pb.""REPEM03_Nro_Identificacion_Tributaria"" = cli.""REPEM08_NIT""
                                                LEFT JOIN ""Reporting-Emvarias_01-Municipios"" mun
                                                          ON pb.""REPEM03_Municipio""::text = mun.""REPEM01_Codigo""::text
                                       WHERE pb.""REPEM03_Fecha_de_cancelacion"" IS NULL
                                         AND NOT (pb.""REPEM03_Id"" IN (SELECT DISTINCT rpm.""REPEM04_Id"" AS id
                                                                      FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                                                                               JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                                                                                    ON rpm.""REPEM04_RutaCodigo""::text =
                                                                                       mic.""REPEM07_Numero_de_Microruta""::text))
                                       UNION ALL
                                       SELECT 720105237                                                          AS ""C1_NUSD"",
                                              '3'::bigint                                                        AS ""C2_TIPO_ORIGEN"",
                                              trept.""Num_ECA""                                                    AS ""C3_NUSITIO_ORI"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                                                  ELSE cli.""REPEM08_Nombre_Completo""
                                                  END                                                            AS ""C4_NOMBRE_EMPRESA"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::bigint
                                                  ELSE pb.""REPEM03_Nro_Identificacion_Tributaria""
                                                  END                                                            AS ""C5_NIT_EMPRESA"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                                                  ELSE mun.""REPEM01_Codigo""
                                                  END                                                            AS ""C6_COD_DANE_ORI"",
                                              pb.""REPEM03_Patente""                                               AS ""C7_PLACA"",
                                              pb.""REPEM03_Fecha_de_entrada""::date                                AS ""C8_FECHA_INGRESO"",
                                              pb.""REPEM03_Fecha_de_egreso""::date                                 AS ""C9_FECHA_SALIDA"",
                                              pb.""REPEM03_Fecha_de_entrada""::time without time zone              AS ""C10_HORA_INGRESO"",
                                              pb.""REPEM03_Fecha_de_egreso""::time without time zone               AS ""C11_HORA_SALIDA"",
                                              COALESCE(trept.""Toneladas_Rechazadas""::numeric, 0::numeric)        AS ""C12_TONELADAS"",
                                              pb.""REPEM03_Id""                                                    AS ""CE_ID_TICKET"",
                                              COALESCE(tct.""REPEM10_Valor"", 0::numeric)                          AS ""CE_VALOR_TICKET_LEGACY"",
                                              COALESCE(tct.""REPEM10_Valor""::numeric / 1000::numeric, 0::numeric) AS ""CE_VALOR_TON_TICKET_LEGACY"",
                                              0                                                                  AS ""CE_TONELADAS_RECHAZADAS"",
                                              true                                                               AS ""CE_EXISTE_EN_F14"",
                                              pb.""REPEM03_Nro_Identificacion_Tributaria""                         AS ""CE_NIT_EMPRESA"",
                                              440405001                                                          AS ""CE_NUAP"",
                                              r14.""C5_FECHA""                                                     AS ""CE_FECHA_FILTRO""
                                       FROM ""REPEM_Reporte_SUI_Recolecciones_F14"" r14
                                                JOIN ""REPEM_Toneladas_Rechazos_Por_Ticket"" trept
                                                     ON r14.""CE_ID_TICKET"" = trept.""Id_Ticket"" AND trept.""NUAP"" = r14.""C1_NUAP""
                                                JOIN ""Reporting-Emvarias_03-Pesaje_de_Balanza"" pb
                                                     ON pb.""REPEM03_Id"" = r14.""CE_ID_TICKET"" AND
                                                        pb.""REPEM03_Fecha_de_cancelacion"" IS NULL
                                                LEFT JOIN ""Reporting-Emvarias_08-Clientes"" cli
                                                          ON pb.""REPEM03_Nro_Identificacion_Tributaria"" = cli.""REPEM08_NIT""
                                                LEFT JOIN ""Reporting-Emvarias_01-Municipios"" mun
                                                          ON pb.""REPEM03_Municipio""::text = mun.""REPEM01_Codigo""::text
                                                LEFT JOIN ""Reporting-Emvarias_10-Tickets_de_Servicio_Generados"" tct
                                                          ON pb.""REPEM03_Id"" = tct.""REPEM10_Id"" AND
                                                             pb.""REPEM03_Fecha_de_cancelacion"" IS NULL)
                SELECT ""C1_NUSD"",
                       ""C2_TIPO_ORIGEN"",
                       ""C3_NUSITIO_ORI"",
                       ""C4_NOMBRE_EMPRESA"",
                       ""C5_NIT_EMPRESA"",
                       ""C6_COD_DANE_ORI"",
                       ""C7_PLACA"",
                       ""C8_FECHA_INGRESO"",
                       ""C9_FECHA_SALIDA"",
                       ""C10_HORA_INGRESO"",
                       ""C11_HORA_SALIDA"",
                       COALESCE(""C12_TONELADAS"", 0::numeric) AS ""C12_TONELADAS"",
                       ""CE_ID_TICKET"",
                       ""CE_VALOR_TICKET_LEGACY"",
                       ""CE_VALOR_TON_TICKET_LEGACY"",
                       ""CE_TONELADAS_RECHAZADAS"",
                       ""CE_EXISTE_EN_F14"",
                       ""CE_NIT_EMPRESA"",
                       ""CE_NUAP"",
                       ""CE_FECHA_FILTRO""
                FROM combined_data;
            ");
        }
    }
}
