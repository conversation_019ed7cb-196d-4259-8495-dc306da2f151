using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Reports.Infrastructure.Data.EFCore.Migrations
{
    public partial class New_RESUL_FIX_F14_Tolls_Filter : Migration
    {
        
        private void RecreateSUIGenerationViews(MigrationBuilder migrationBuilder)
        {
            //Vista de Reporte 14
            migrationBuilder.Sql(@"
                    CREATE OR REPLACE VIEW ""REPEM_Reporte_SUI_Recolecciones_F14""
                    AS
                    WITH cte AS (SELECT rpm.""REPEM04_Id""                                     AS ""Id_Ticket"",
                                        mic.""REPEM07_NUAP""                                   AS ""NUAP"",
                                        CASE
                                            WHEN mic.""REPEM07_NUAP"" = 440405001 THEN (rpm.""REPEM04_PesoTotal_Toneladas""::numeric -
                                                                                      COALESCE(tr.""Toneladas_Descuento_Medellin"", 0::numeric)::numeric)::numeric
                                            WHEN mic.""REPEM07_NUAP"" = 440705360 AND
                                                 mic.""REPEM07_Porcentaje_No_Aforado""::numeric <> 0::numeric
                                                THEN tr.""Toneladas_Descuento_Medellin""
                                            ELSE tdha.""Toneladas_Resultantes""
                                            END                                              AS ""Calculo_Toneladas"",
                                        CASE
                                            WHEN mic.""REPEM07_Porcentaje_Barrido"" = 0 THEN 0::numeric
                                            ELSE (
                                                           CASE
                                                               WHEN mic.""REPEM07_NUAP"" = 440405001 THEN rpm.""REPEM04_PesoTotal_Toneladas"" -
                                                                                                        COALESCE(tr.""Toneladas_Descuento_Medellin"", 0::numeric)
                                                               WHEN mic.""REPEM07_NUAP"" = 440705360 AND
                                                                    mic.""REPEM07_Porcentaje_No_Aforado"" <> 0
                                                                   THEN tr.""Toneladas_Descuento_Medellin""
                                                               ELSE tdha.""Toneladas_Resultantes""
                                                               END::numeric * (mic.""REPEM07_Porcentaje_Barrido""::numeric /
                                                                                        100::numeric))::numeric(18, 3)
                                            END::numeric(18, 3)                              AS ""Calculo_Barrido"",
                                        CASE
                                            WHEN pe.* IS NULL THEN 0::numeric
                                            WHEN mic.""REPEM07_NUAP"" = 440405001 THEN (pe.""REPEM06_Valor"" * 2::numeric -
                                                                                      COALESCE(tr.""Peaje_Descuento_Medellin"", 0::numeric)::numeric)::numeric
                                            WHEN mic.""REPEM07_NUAP"" = 440705360 AND
                                                 mic.""REPEM07_Porcentaje_No_Aforado""::numeric <> 0::numeric
                                                THEN tr.""Peaje_Descuento_Medellin""
                                            ELSE (pe.""REPEM06_Valor"" * 2::numeric *
                                                  (COALESCE(dpm.""Porcentaje_Distribucion_Peaje"", 100::numeric) /
                                                   100::numeric)::numeric)::numeric
                                            END                                              AS ""Calculo_Peaje"",
                                        COALESCE(tdha.""Tipo_Compensacion"", 'ORIGINAL'::text) AS ""Tipo_Compensacion""
                                 FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                                          JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                                               ON rpm.""REPEM04_RutaCodigo""::text = mic.""REPEM07_Numero_de_Microruta""::text
                                          LEFT JOIN ""REPEM_Distribuciones_de_Microrutas"" dpm ON dpm.""NUAP"" = mic.""REPEM07_NUAP"" AND
                                                                                                dpm.""Año""::numeric =
                                                                                                EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"") AND
                                                                                                dpm.""Mes""::numeric =
                                                                                                EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje"")
                                          LEFT JOIN ""Reporting-Emvarias_06-Peajes"" pe ON pe.""REPEM06_Placa""::text = rpm.""REPEM04_Patente""::text
                                          LEFT JOIN ""REPEM_Descuento_Por_Ticket"" tr ON tr.""Id_Ticket"" = rpm.""REPEM04_Id""
                                          LEFT JOIN ""REPEM_Detalle_Compensacion_Tickets"" tdha
                                                    ON tdha.""Id_Ticket"" = rpm.""REPEM04_Id"" AND tdha.""NUAP"" = mic.""REPEM07_NUAP"" AND
                                                       tdha.""Tipo_Compensacion"" = 'ORIGINAL'::text),
                    aggregated_compensations AS (
                            SELECT
                                0 as ""Id_Ticket"",
                                ""Nro_Ticket_Compensacion"",
                                sum(""Suma_Agrupacion_Toneladas_Por_Compensar"") AS ""Suma_Agrupacion_Toneladas_Por_Compensar""
                            FROM ""REPEM_Tickets_Compensables""
                            GROUP BY ""Nro_Ticket_Compensacion""
                    )
                    SELECT mic.""REPEM07_NUAP""                                     AS ""C1_NUAP"",
                           1                                                      AS ""C2_TIPO_SITIO"",
                           720105237                                              AS ""C3_NUSD"",
                           rpm.""REPEM04_Patente""                                  AS ""C4_PLACA"",
                           rpm.""REPEM04_FechaHora_Pesaje""::date                   AS ""C5_FECHA"",
                           rpm.""REPEM04_FechaHora_Pesaje""::time without time zone AS ""C6_HORA"",
                           mic.""REPEM07_Numero_Ruta_Intendencia""                  AS ""C7_NUMICRO"",
                           0                                                      AS ""C8_TON_LIMP_URB"",
                           round(bs.""Calculo_Barrido"" + COALESCE(tde.""Toneladas_Descuento"", 0::numeric), 3) -
                           COALESCE(dbi.""Valor_A_Descontar"", 0::numeric)          AS ""C9_TON_BARRIDO"",
                           ((CASE
                               WHEN mic.""REPEM07_Numero_de_Microruta"" = 614001 THEN round(
                                       bs.""Calculo_Toneladas"" - COALESCE(rtc.""Suma_Agrupacion_Toneladas_Por_Compensar"", 0::numeric), 3)
                               ELSE round(bs.""Calculo_Toneladas"" - bs.""Calculo_Barrido"" - COALESCE(tde.""Toneladas_Descuento"", 0::numeric) -
                                          COALESCE(rtc.""Suma_Agrupacion_Toneladas_Por_Compensar"", 0::numeric), 3)
                           END)
                           + COALESCE(compred.""Toneladas_Ajuste"", 0::numeric))    AS ""C10_TONRESNA"",
                           COALESCE(tre.""Toneladas_Rechazadas"", 0::numeric)       AS ""C11_TONRECHAPR"",
                           CASE
                               WHEN mic.""REPEM07_Porcentaje_Residuos_Aprovechables""::numeric <> 0::numeric THEN (
                                   bs.""Calculo_Toneladas""::numeric *
                                   (mic.""REPEM07_Porcentaje_Residuos_Aprovechables""::numeric / 100::numeric))::numeric
                               ELSE 0::numeric
                               END                                                AS ""C12_TONRESAPR"",
                           '1'::text                                              AS ""C13_SISTEMA_MEDICION"",
                           bs.""Calculo_Peaje""                                     AS ""C14_VLRPEAJ"",
                           rpm.""REPEM04_Id""                                       AS ""CE_ID_TICKET"",
                           ap.""REPEM02_Nombre""                                    AS ""CE_NOMBRE_AREA"",
                           mic.""REPEM07_Ruta_Larga""                               AS ""CE_RUTA_LARGA"",
                           rpm.""REPEM04_PesoTotal_Toneladas""                      AS ""CE_TON_TOTAL"",
                           rtc.* IS NOT NULL                                      AS ""CE_REL_COMPENSACION"",
                           rtc.""Id_Ticket""                                        AS ""CE_REL_COMPENSACION_ID_TICKET"",
                           compred.* IS NOT NULL                                    AS ""CE_AJUSTE_DECIMAL""
                    FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                             JOIN cte bs ON bs.""Calculo_Toneladas"" IS NOT NULL AND rpm.""REPEM04_Id"" = bs.""Id_Ticket""
                             JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                                  ON rpm.""REPEM04_RutaCodigo""::text = mic.""REPEM07_Numero_de_Microruta""::text AND
                                     mic.""REPEM07_NUAP"" = bs.""NUAP""
                             JOIN ""Reporting-Emvarias_02-Areas_de_Aprovechamiento"" ap ON ap.""REPEM02_Codigo"" = mic.""REPEM07_NUAP""
                             LEFT JOIN ""REPEM_Descuento_Pesaje_Excepcional_Por_Ticket"" tde ON rpm.""REPEM04_Id"" = tde.""NroTicket"" AND
                                                                                              EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"") =
                                                                                              tde.""Año"" AND
                                                                                              EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje"") =
                                                                                              tde.""Mes"" AND
                                                                                              mic.""REPEM07_Numero_de_Microruta"" =
                                                                                              tde.""RutaCodigo"" AND
                                                                                              mic.""REPEM07_NUAP"" = tde.""NUAP""
                             LEFT JOIN ""REPEM_Toneladas_Rechazos_Agrupadas_Por_Ticket"" tre
                                       ON rpm.""REPEM04_Id"" = tre.""Ticket_Asignable"" AND mic.""REPEM07_NUAP"" = 440405001
                             LEFT JOIN aggregated_compensations rtc ON rpm.""REPEM04_Id"" = rtc.""Nro_Ticket_Compensacion""
                             LEFT JOIN ""REPEM_Descuento_de_Barrido_Itagui_Por_Ticket"" dbi ON dbi.""Id_Ticket_A_Descontar"" = rpm.""REPEM04_Id""
                             LEFT JOIN ""REPEM_Compensaciones_por_Redondeo"" compred 
                                        ON compred.""Nro_Ticket_Ajustable"" = rpm.""REPEM04_Id""
                                        AND compred.""NUAP"" = mic.""REPEM07_NUAP""
                    UNION ALL
                    (WITH cte_comp AS (SELECT rpm_1.""REPEM04_Id""                    AS ""Id_Ticket"",
                                              mic.""REPEM07_NUAP""                    AS ""NUAP"",
                                              mic.""REPEM07_Numero_Ruta_Intendencia"" AS ""Numero_Ruta_Indendencia"",
                                              mic.""REPEM07_Ruta_Larga""              AS ""Ruta_Larga"",
                                              ap.""REPEM02_Nombre""                   AS ""Area_Aprovechamiento"",
                                              CASE
                                                  WHEN mic.""REPEM07_NUAP"" = 440405001 THEN (
                                                      rpm_1.""REPEM04_PesoTotal_Toneladas""::numeric -
                                                      COALESCE(tr.""Toneladas_Descuento_Medellin"", 0::numeric)::numeric)::numeric
                                                  WHEN mic.""REPEM07_NUAP"" = 440705360 AND
                                                       mic.""REPEM07_Porcentaje_No_Aforado""::numeric <> 0::numeric
                                                      THEN tr.""Toneladas_Descuento_Medellin""
                                                  ELSE tdha.""Toneladas_Resultantes""
                                                  END                               AS ""Calculo_Toneladas"",
                                              CASE
                                                  WHEN mic.""REPEM07_Porcentaje_Barrido"" = 0 THEN 0::numeric
                                                  ELSE (
                                                                 CASE
                                                                     WHEN mic.""REPEM07_NUAP"" = 440405001 THEN
                                                                         rpm_1.""REPEM04_PesoTotal_Toneladas"" -
                                                                         COALESCE(tr.""Toneladas_Descuento_Medellin"", 0::numeric)
                                                                     WHEN mic.""REPEM07_NUAP"" = 440705360 AND
                                                                          mic.""REPEM07_Porcentaje_No_Aforado"" <> 0
                                                                         THEN tr.""Toneladas_Descuento_Medellin""
                                                                     ELSE tdha.""Toneladas_Resultantes""
                                                                     END::numeric *
                                                                 (mic.""REPEM07_Porcentaje_Barrido""::numeric /
                                                                  100::numeric))::numeric(18, 3)
                                                  END::numeric(18, 3)               AS ""Calculo_Barrido"",
                                              0                                     AS ""Calculo_Peaje"",
                                              tdha.""Tipo_Compensacion""
                                       FROM ""REPEM_Detalle_Compensacion_Tickets"" tdha
                                                JOIN ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm_1
                                                     ON tdha.""Id_Ticket"" = rpm_1.""REPEM04_Id""
                                                JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                                                     ON rpm_1.""REPEM04_RutaCodigo""::text = mic.""REPEM07_Numero_de_Microruta""::text AND
                                                        mic.""REPEM07_NUAP"" = tdha.""NUAP""
                                                JOIN ""Reporting-Emvarias_02-Areas_de_Aprovechamiento"" ap
                                                     ON ap.""REPEM02_Codigo"" = mic.""REPEM07_NUAP""
                                                LEFT JOIN ""REPEM_Distribuciones_de_Microrutas"" dpm ON dpm.""NUAP"" = mic.""REPEM07_NUAP"" AND
                                                                                                      dpm.""Año""::numeric =
                                                                                                      EXTRACT(year FROM rpm_1.""REPEM04_FechaHora_Pesaje"") AND
                                                                                                      dpm.""Mes""::numeric =
                                                                                                      EXTRACT(month FROM rpm_1.""REPEM04_FechaHora_Pesaje"")
                                                LEFT JOIN ""Reporting-Emvarias_06-Peajes"" pe ON pe.""REPEM06_Placa""::text = rpm_1.""REPEM04_Patente""::text
                                                LEFT JOIN ""REPEM_Descuento_Por_Ticket"" tr ON tr.""Id_Ticket"" = rpm_1.""REPEM04_Id""
                                       WHERE tdha.""Tipo_Compensacion"" = 'TOTAL'::text
                                          OR tdha.""Tipo_Compensacion"" = 'PARCIAL'::text)
                     SELECT bs.""NUAP""                                              AS ""C1_NUAP"",
                            1                                                      AS ""C2_TIPO_SITIO"",
                            720105237                                              AS ""C3_NUSD"",
                            rpm.""REPEM04_Patente""                                  AS ""C4_PLACA"",
                            rpm.""REPEM04_FechaHora_Pesaje""::date                   AS ""C5_FECHA"",
                            rpm.""REPEM04_FechaHora_Pesaje""::time without time zone AS ""C6_HORA"",
                            bs.""Numero_Ruta_Indendencia""                           AS ""C7_NUMICRO"",
                            0                                                      AS ""C8_TON_LIMP_URB"",
                            0                                                      AS ""C9_TON_BARRIDO"",
                            CASE
                                WHEN rtc.* IS NOT NULL THEN bs.""Calculo_Toneladas""::numeric
                                ELSE rpm.""REPEM04_PesoTotal_Toneladas""::numeric
                                END                                                AS ""C10_TONRESNA"",
                            COALESCE(tre.""Toneladas_Rechazadas"", 0::numeric)       AS ""C11_TONRECHAPR"",
                            CASE
                                WHEN mr.""REPEM07_Porcentaje_Residuos_Aprovechables""::numeric <> 0::numeric THEN (
                                    bs.""Calculo_Toneladas""::numeric *
                                    (mr.""REPEM07_Porcentaje_Residuos_Aprovechables""::numeric / 100::numeric))::numeric
                                ELSE 0::numeric
                                END                                                AS ""C12_TONRESAPR"",
                            '1'::text                                              AS ""C13_SISTEMA_MEDICION"",
                            bs.""Calculo_Peaje""                                     AS ""C14_VLRPEAJ"",
                            rtc.""Nro_Ticket_Compensacion""                          AS ""CE_ID_TICKET"",
                            bs.""Area_Aprovechamiento""                              AS ""CE_NOMBRE_AREA"",
                            bs.""Ruta_Larga""                                        AS ""CE_RUTA_LARGA"",
                            rtc.""Maximo_Toneladas_Compensables""                    AS ""CE_TON_TOTAL"",
                            rtc.* IS NOT NULL                                      AS ""CE_REL_COMPENSACION"",
                            CASE
                                WHEN rtc.* IS NOT NULL THEN rtc.""Id_Ticket""
                                ELSE bs.""Id_Ticket""
                                END                                                AS ""CE_REL_COMPENSACION_ID_TICKET"",
                            false                                                   AS ""CE_AJUSTE_DECIMAL""
                     FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                              JOIN cte_comp bs ON rpm.""REPEM04_Id"" = bs.""Id_Ticket""
                              JOIN ""Reporting-Emvarias_07-Microrutas"" mr
                                   ON mr.""REPEM07_Ruta_Larga"" = bs.""Ruta_Larga"" AND mr.""REPEM07_NUAP"" = bs.""NUAP""
                              JOIN ""REPEM_Tickets_Compensables"" rtc ON rpm.""REPEM04_Id"" = rtc.""Id_Ticket""
                              LEFT JOIN ""REPEM_Toneladas_Rechazos_Agrupadas_Por_Ticket"" tre
                                        ON rpm.""REPEM04_Id"" = tre.""Ticket_Asignable"" AND mr.""REPEM07_NUAP"" = 440405001);
            ");
        }
        
        private void RecreateSUIGenerationViewsChanges(MigrationBuilder migrationBuilder)
        {
            //Vista de Reporte 14
            migrationBuilder.Sql(@"
                    CREATE OR REPLACE VIEW ""REPEM_Reporte_SUI_Recolecciones_F14""
                    AS
                    WITH cte AS (SELECT rpm.""REPEM04_Id""                                     AS ""Id_Ticket"",
                                        mic.""REPEM07_NUAP""                                   AS ""NUAP"",
                                        CASE
                                            WHEN mic.""REPEM07_NUAP"" = 440405001 THEN (rpm.""REPEM04_PesoTotal_Toneladas""::numeric -
                                                                                      COALESCE(tr.""Toneladas_Descuento_Medellin"", 0::numeric)::numeric)::numeric
                                            WHEN mic.""REPEM07_NUAP"" = 440705360 AND
                                                 mic.""REPEM07_Porcentaje_No_Aforado""::numeric <> 0::numeric
                                                THEN tr.""Toneladas_Descuento_Medellin""
                                            ELSE tdha.""Toneladas_Resultantes""
                                            END                                              AS ""Calculo_Toneladas"",
                                        CASE
                                            WHEN mic.""REPEM07_Porcentaje_Barrido"" = 0 THEN 0::numeric
                                            ELSE (
                                                           CASE
                                                               WHEN mic.""REPEM07_NUAP"" = 440405001 THEN rpm.""REPEM04_PesoTotal_Toneladas"" -
                                                                                                        COALESCE(tr.""Toneladas_Descuento_Medellin"", 0::numeric)
                                                               WHEN mic.""REPEM07_NUAP"" = 440705360 AND
                                                                    mic.""REPEM07_Porcentaje_No_Aforado"" <> 0
                                                                   THEN tr.""Toneladas_Descuento_Medellin""
                                                               ELSE tdha.""Toneladas_Resultantes""
                                                               END::numeric * (mic.""REPEM07_Porcentaje_Barrido""::numeric /
                                                                                        100::numeric))::numeric(18, 3)
                                            END::numeric(18, 3)                              AS ""Calculo_Barrido"",
                                        CASE
                                            WHEN pe.* IS NULL THEN 0::numeric
                                            WHEN mic.""REPEM07_NUAP"" = 440405001 THEN (pe.""REPEM06_Valor"" * 2::numeric -
                                                                                      COALESCE(tr.""Peaje_Descuento_Medellin"", 0::numeric)::numeric)::numeric
                                            WHEN mic.""REPEM07_NUAP"" = 440705360 AND
                                                 mic.""REPEM07_Porcentaje_No_Aforado""::numeric <> 0::numeric
                                                THEN tr.""Peaje_Descuento_Medellin""
                                            ELSE (pe.""REPEM06_Valor"" * 2::numeric *
                                                  (COALESCE(dpm.""Porcentaje_Distribucion_Peaje"", 100::numeric) /
                                                   100::numeric)::numeric)::numeric
                                            END                                              AS ""Calculo_Peaje"",
                                        COALESCE(tdha.""Tipo_Compensacion"", 'ORIGINAL'::text) AS ""Tipo_Compensacion""
                                 FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                                          JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                                               ON rpm.""REPEM04_RutaCodigo""::text = mic.""REPEM07_Numero_de_Microruta""::text
                                          LEFT JOIN ""REPEM_Distribuciones_de_Microrutas"" dpm ON dpm.""NUAP"" = mic.""REPEM07_NUAP"" AND
                                                                                                dpm.""Año""::numeric =
                                                                                                EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"") AND
                                                                                                dpm.""Mes""::numeric =
                                                                                                EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje"")
                                          LEFT JOIN ""Reporting-Emvarias_06-Peajes"" pe ON pe.""REPEM06_Placa""::text = rpm.""REPEM04_Patente""::text AND 
                                                                                                EXTRACT(year FROM pe.""REPEM06_Fecha_Validez"") = 
                                                                                                EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"")
                                          LEFT JOIN ""REPEM_Descuento_Por_Ticket"" tr ON tr.""Id_Ticket"" = rpm.""REPEM04_Id""
                                          LEFT JOIN ""REPEM_Detalle_Compensacion_Tickets"" tdha
                                                    ON tdha.""Id_Ticket"" = rpm.""REPEM04_Id"" AND tdha.""NUAP"" = mic.""REPEM07_NUAP"" AND
                                                       tdha.""Tipo_Compensacion"" = 'ORIGINAL'::text),
                    aggregated_compensations AS (
                            SELECT
                                0 as ""Id_Ticket"",
                                ""Nro_Ticket_Compensacion"",
                                sum(""Suma_Agrupacion_Toneladas_Por_Compensar"") AS ""Suma_Agrupacion_Toneladas_Por_Compensar""
                            FROM ""REPEM_Tickets_Compensables""
                            GROUP BY ""Nro_Ticket_Compensacion""
                    )
                    SELECT mic.""REPEM07_NUAP""                                     AS ""C1_NUAP"",
                           1                                                      AS ""C2_TIPO_SITIO"",
                           720105237                                              AS ""C3_NUSD"",
                           rpm.""REPEM04_Patente""                                  AS ""C4_PLACA"",
                           rpm.""REPEM04_FechaHora_Pesaje""::date                   AS ""C5_FECHA"",
                           rpm.""REPEM04_FechaHora_Pesaje""::time without time zone AS ""C6_HORA"",
                           mic.""REPEM07_Numero_Ruta_Intendencia""                  AS ""C7_NUMICRO"",
                           0                                                      AS ""C8_TON_LIMP_URB"",
                           round(bs.""Calculo_Barrido"" + COALESCE(tde.""Toneladas_Descuento"", 0::numeric), 3) -
                           COALESCE(dbi.""Valor_A_Descontar"", 0::numeric)          AS ""C9_TON_BARRIDO"",
                           ((CASE
                               WHEN mic.""REPEM07_Numero_de_Microruta"" = 614001 THEN round(
                                       bs.""Calculo_Toneladas"" - COALESCE(rtc.""Suma_Agrupacion_Toneladas_Por_Compensar"", 0::numeric), 3)
                               ELSE round(bs.""Calculo_Toneladas"" - bs.""Calculo_Barrido"" - COALESCE(tde.""Toneladas_Descuento"", 0::numeric) -
                                          COALESCE(rtc.""Suma_Agrupacion_Toneladas_Por_Compensar"", 0::numeric), 3)
                           END)
                           + COALESCE(compred.""Toneladas_Ajuste"", 0::numeric))    AS ""C10_TONRESNA"",
                           COALESCE(tre.""Toneladas_Rechazadas"", 0::numeric)       AS ""C11_TONRECHAPR"",
                           CASE
                               WHEN mic.""REPEM07_Porcentaje_Residuos_Aprovechables""::numeric <> 0::numeric THEN (
                                   bs.""Calculo_Toneladas""::numeric *
                                   (mic.""REPEM07_Porcentaje_Residuos_Aprovechables""::numeric / 100::numeric))::numeric
                               ELSE 0::numeric
                               END                                                AS ""C12_TONRESAPR"",
                           '1'::text                                              AS ""C13_SISTEMA_MEDICION"",
                           bs.""Calculo_Peaje""                                     AS ""C14_VLRPEAJ"",
                           rpm.""REPEM04_Id""                                       AS ""CE_ID_TICKET"",
                           ap.""REPEM02_Nombre""                                    AS ""CE_NOMBRE_AREA"",
                           mic.""REPEM07_Ruta_Larga""                               AS ""CE_RUTA_LARGA"",
                           rpm.""REPEM04_PesoTotal_Toneladas""                      AS ""CE_TON_TOTAL"",
                           rtc.* IS NOT NULL                                      AS ""CE_REL_COMPENSACION"",
                           rtc.""Id_Ticket""                                        AS ""CE_REL_COMPENSACION_ID_TICKET"",
                           compred.* IS NOT NULL                                    AS ""CE_AJUSTE_DECIMAL""
                    FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                             JOIN cte bs ON bs.""Calculo_Toneladas"" IS NOT NULL AND rpm.""REPEM04_Id"" = bs.""Id_Ticket""
                             JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                                  ON rpm.""REPEM04_RutaCodigo""::text = mic.""REPEM07_Numero_de_Microruta""::text AND
                                     mic.""REPEM07_NUAP"" = bs.""NUAP""
                             JOIN ""Reporting-Emvarias_02-Areas_de_Aprovechamiento"" ap ON ap.""REPEM02_Codigo"" = mic.""REPEM07_NUAP""
                             LEFT JOIN ""REPEM_Descuento_Pesaje_Excepcional_Por_Ticket"" tde ON rpm.""REPEM04_Id"" = tde.""NroTicket"" AND
                                                                                              EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"") =
                                                                                              tde.""Año"" AND
                                                                                              EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje"") =
                                                                                              tde.""Mes"" AND
                                                                                              mic.""REPEM07_Numero_de_Microruta"" =
                                                                                              tde.""RutaCodigo"" AND
                                                                                              mic.""REPEM07_NUAP"" = tde.""NUAP""
                             LEFT JOIN ""REPEM_Toneladas_Rechazos_Agrupadas_Por_Ticket"" tre
                                       ON rpm.""REPEM04_Id"" = tre.""Ticket_Asignable"" AND mic.""REPEM07_NUAP"" = 440405001
                             LEFT JOIN aggregated_compensations rtc ON rpm.""REPEM04_Id"" = rtc.""Nro_Ticket_Compensacion""
                             LEFT JOIN ""REPEM_Descuento_de_Barrido_Itagui_Por_Ticket"" dbi ON dbi.""Id_Ticket_A_Descontar"" = rpm.""REPEM04_Id""
                             LEFT JOIN ""REPEM_Compensaciones_por_Redondeo"" compred 
                                        ON compred.""Nro_Ticket_Ajustable"" = rpm.""REPEM04_Id""
                                        AND compred.""NUAP"" = mic.""REPEM07_NUAP""
                    UNION ALL
                    (WITH cte_comp AS (SELECT rpm_1.""REPEM04_Id""                    AS ""Id_Ticket"",
                                              mic.""REPEM07_NUAP""                    AS ""NUAP"",
                                              mic.""REPEM07_Numero_Ruta_Intendencia"" AS ""Numero_Ruta_Indendencia"",
                                              mic.""REPEM07_Ruta_Larga""              AS ""Ruta_Larga"",
                                              ap.""REPEM02_Nombre""                   AS ""Area_Aprovechamiento"",
                                              CASE
                                                  WHEN mic.""REPEM07_NUAP"" = 440405001 THEN (
                                                      rpm_1.""REPEM04_PesoTotal_Toneladas""::numeric -
                                                      COALESCE(tr.""Toneladas_Descuento_Medellin"", 0::numeric)::numeric)::numeric
                                                  WHEN mic.""REPEM07_NUAP"" = 440705360 AND
                                                       mic.""REPEM07_Porcentaje_No_Aforado""::numeric <> 0::numeric
                                                      THEN tr.""Toneladas_Descuento_Medellin""
                                                  ELSE tdha.""Toneladas_Resultantes""
                                                  END                               AS ""Calculo_Toneladas"",
                                              CASE
                                                  WHEN mic.""REPEM07_Porcentaje_Barrido"" = 0 THEN 0::numeric
                                                  ELSE (
                                                                 CASE
                                                                     WHEN mic.""REPEM07_NUAP"" = 440405001 THEN
                                                                         rpm_1.""REPEM04_PesoTotal_Toneladas"" -
                                                                         COALESCE(tr.""Toneladas_Descuento_Medellin"", 0::numeric)
                                                                     WHEN mic.""REPEM07_NUAP"" = 440705360 AND
                                                                          mic.""REPEM07_Porcentaje_No_Aforado"" <> 0
                                                                         THEN tr.""Toneladas_Descuento_Medellin""
                                                                     ELSE tdha.""Toneladas_Resultantes""
                                                                     END::numeric *
                                                                 (mic.""REPEM07_Porcentaje_Barrido""::numeric /
                                                                  100::numeric))::numeric(18, 3)
                                                  END::numeric(18, 3)               AS ""Calculo_Barrido"",
                                              0                                     AS ""Calculo_Peaje"",
                                              tdha.""Tipo_Compensacion""
                                       FROM ""REPEM_Detalle_Compensacion_Tickets"" tdha
                                                JOIN ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm_1
                                                     ON tdha.""Id_Ticket"" = rpm_1.""REPEM04_Id""
                                                JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                                                     ON rpm_1.""REPEM04_RutaCodigo""::text = mic.""REPEM07_Numero_de_Microruta""::text AND
                                                        mic.""REPEM07_NUAP"" = tdha.""NUAP""
                                                JOIN ""Reporting-Emvarias_02-Areas_de_Aprovechamiento"" ap
                                                     ON ap.""REPEM02_Codigo"" = mic.""REPEM07_NUAP""
                                                LEFT JOIN ""REPEM_Distribuciones_de_Microrutas"" dpm ON dpm.""NUAP"" = mic.""REPEM07_NUAP"" AND
                                                                                                      dpm.""Año""::numeric =
                                                                                                      EXTRACT(year FROM rpm_1.""REPEM04_FechaHora_Pesaje"") AND
                                                                                                      dpm.""Mes""::numeric =
                                                                                                      EXTRACT(month FROM rpm_1.""REPEM04_FechaHora_Pesaje"")
                                                LEFT JOIN ""Reporting-Emvarias_06-Peajes"" pe ON pe.""REPEM06_Placa""::text = rpm_1.""REPEM04_Patente""::text AND
                                                                                                EXTRACT(year FROM pe.""REPEM06_Fecha_Validez"") = 
                                                                                                EXTRACT(year FROM rpm_1.""REPEM04_FechaHora_Pesaje"")
                                                LEFT JOIN ""REPEM_Descuento_Por_Ticket"" tr ON tr.""Id_Ticket"" = rpm_1.""REPEM04_Id""
                                       WHERE tdha.""Tipo_Compensacion"" = 'TOTAL'::text
                                          OR tdha.""Tipo_Compensacion"" = 'PARCIAL'::text)
                     SELECT bs.""NUAP""                                              AS ""C1_NUAP"",
                            1                                                      AS ""C2_TIPO_SITIO"",
                            720105237                                              AS ""C3_NUSD"",
                            rpm.""REPEM04_Patente""                                  AS ""C4_PLACA"",
                            rpm.""REPEM04_FechaHora_Pesaje""::date                   AS ""C5_FECHA"",
                            rpm.""REPEM04_FechaHora_Pesaje""::time without time zone AS ""C6_HORA"",
                            bs.""Numero_Ruta_Indendencia""                           AS ""C7_NUMICRO"",
                            0                                                      AS ""C8_TON_LIMP_URB"",
                            0                                                      AS ""C9_TON_BARRIDO"",
                            CASE
                                WHEN rtc.* IS NOT NULL THEN bs.""Calculo_Toneladas""::numeric
                                ELSE rpm.""REPEM04_PesoTotal_Toneladas""::numeric
                                END                                                AS ""C10_TONRESNA"",
                            COALESCE(tre.""Toneladas_Rechazadas"", 0::numeric)       AS ""C11_TONRECHAPR"",
                            CASE
                                WHEN mr.""REPEM07_Porcentaje_Residuos_Aprovechables""::numeric <> 0::numeric THEN (
                                    bs.""Calculo_Toneladas""::numeric *
                                    (mr.""REPEM07_Porcentaje_Residuos_Aprovechables""::numeric / 100::numeric))::numeric
                                ELSE 0::numeric
                                END                                                AS ""C12_TONRESAPR"",
                            '1'::text                                              AS ""C13_SISTEMA_MEDICION"",
                            bs.""Calculo_Peaje""                                     AS ""C14_VLRPEAJ"",
                            rtc.""Nro_Ticket_Compensacion""                          AS ""CE_ID_TICKET"",
                            bs.""Area_Aprovechamiento""                              AS ""CE_NOMBRE_AREA"",
                            bs.""Ruta_Larga""                                        AS ""CE_RUTA_LARGA"",
                            rtc.""Maximo_Toneladas_Compensables""                    AS ""CE_TON_TOTAL"",
                            rtc.* IS NOT NULL                                      AS ""CE_REL_COMPENSACION"",
                            CASE
                                WHEN rtc.* IS NOT NULL THEN rtc.""Id_Ticket""
                                ELSE bs.""Id_Ticket""
                                END                                                AS ""CE_REL_COMPENSACION_ID_TICKET"",
                            false                                                   AS ""CE_AJUSTE_DECIMAL""
                     FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                              JOIN cte_comp bs ON rpm.""REPEM04_Id"" = bs.""Id_Ticket""
                              JOIN ""Reporting-Emvarias_07-Microrutas"" mr
                                   ON mr.""REPEM07_Ruta_Larga"" = bs.""Ruta_Larga"" AND mr.""REPEM07_NUAP"" = bs.""NUAP""
                              JOIN ""REPEM_Tickets_Compensables"" rtc ON rpm.""REPEM04_Id"" = rtc.""Id_Ticket""
                              LEFT JOIN ""REPEM_Toneladas_Rechazos_Agrupadas_Por_Ticket"" tre
                                        ON rpm.""REPEM04_Id"" = tre.""Ticket_Asignable"" AND mr.""REPEM07_NUAP"" = 440405001);
            ");
        }
        
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            RecreateSUIGenerationViewsChanges(migrationBuilder);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            RecreateSUIGenerationViews(migrationBuilder);
        }
    }
}
