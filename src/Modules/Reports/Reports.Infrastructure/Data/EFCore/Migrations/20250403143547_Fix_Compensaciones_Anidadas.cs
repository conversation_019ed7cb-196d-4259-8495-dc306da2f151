using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Reports.Infrastructure.Data.EFCore.Migrations
{
    public partial class Fix_Compensaciones_Anidadas : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            //Vista de Compensaciones modificada
            migrationBuilder.Sql(@"
                create or replace view ""REPEM_Compensaciones_por_Redondeo""
            (""Nro_Ticket_Ajustable"", ""Fecha_Pesaje"", ""NUAP"", ""Area_Aprovechamiento"", ""Peso_Ticket"", ""Toneladas_Ajuste"",
             ""Mes"", ""Año"")
            as
            WITH parent_dct AS MATERIALIZED (SELECT ""REPEM_Detalle_Compensacion_Tickets"".""Id_Ticket"",
                                                    ""REPEM_Detalle_Compensacion_Tickets"".""NUAP"",
                                                    ""REPEM_Detalle_Compensacion_Tickets"".""<PERSON><PERSON>_<PERSON><PERSON>_Pesaje"",
                                                    ""REPEM_Detalle_Compensacion_Tickets"".""Toneladas_Resultantes"",
                                                    ""REPEM_Detalle_Compensacion_Tickets"".""Tipo_Compensacion""
                                             FROM ""REPEM_Detalle_Compensacion_Tickets""
                                             WHERE ""REPEM_Detalle_Compensacion_Tickets"".""Tipo_Compensacion"" <> 'ORIGINAL'::text),
                 total_compensations AS (SELECT parent_dct.""Id_Ticket"",
                                 parent_dct.""NUAP"",
                                 parent_dct.""Fecha_Hora_Pesaje"",
                                 parent_dct.""Toneladas_Resultantes"",
                                 parent_dct.""Tipo_Compensacion""
                          FROM parent_dct
                          WHERE parent_dct.""Tipo_Compensacion"" = 'TOTAL'::text),
                 tickets_compensables AS (SELECT ""REPEM_Tickets_Compensables"".""Id_Ticket"",
                                 ""REPEM_Tickets_Compensables"".""Suma_Agrupacion_Toneladas_Por_Compensar"",
                                 ""REPEM_Tickets_Compensables"".""Nro_Ticket_Compensacion"",
                                 ""REPEM_Tickets_Compensables"".""Maximo_Toneladas_Compensables""
                          FROM ""REPEM_Tickets_Compensables""),
                 cte AS (WITH exclusions AS (SELECT parent_dct.""Id_Ticket"",
                                                    parent_dct.""NUAP"",
                                                    parent_dct.""Fecha_Hora_Pesaje"",
                                                    parent_dct.""Toneladas_Resultantes"",
                                                    parent_dct.""Tipo_Compensacion""
                                             FROM parent_dct
                                             WHERE parent_dct.""Tipo_Compensacion"" = 'PARCIAL'::text),
                              posibilities AS (SELECT rpm.""REPEM04_Id""               AS ""Nro_Ticket_Ajustable"",
                                                      rpm.""REPEM04_FechaHora_Pesaje"" AS ""Fecha_Pesaje"",
                                                      dct.""NUAP"",
                                                      dct.""Toneladas_Resultantes""    AS ""Peso_Ticket"",
                                                      rpm.""REPEM04_PesoTotal_Toneladas"",
                                                      dpm.""Toneladas_Desviacion""     AS ""Toneladas_Ajuste"",
                                                      ap.""REPEM02_Nombre""            AS ""Area_Aprovechamiento""
                                               FROM ""REPEM_Detalle_Compensacion_Tickets"" dct
                                                        JOIN ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                                                             ON dct.""Id_Ticket"" = rpm.""REPEM04_Id""
                                                        JOIN ""Reporting-Emvarias_03-Pesaje_de_Balanza"" pb
                                                             ON pb.""REPEM03_Id"" = rpm.""REPEM04_Id""
                                                        JOIN ""Reporting-Emvarias_02-Areas_de_Aprovechamiento"" ap
                                                             ON ap.""REPEM02_Codigo"" = dct.""NUAP""
                                                        JOIN ""REPEM_Distribuciones_de_Microrutas"" dpm ON dpm.""NUAP"" = dct.""NUAP"" AND
                                                                                                         dpm.""Año""::numeric =
                                                                                                         EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"") AND
                                                                                                         dpm.""Mes""::numeric =
                                                                                                         EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje"") AND
                                                                                                         dct.""Toneladas_Resultantes"" >=
                                                                                                         dpm.""Toneladas_Desviacion""
                                               WHERE pb.""REPEM03_Fecha_de_cancelacion"" IS NULL
                                                 AND NOT (rpm.""REPEM04_Id"" IN (SELECT exclusions.""Id_Ticket""
                                                                               FROM exclusions))
                                                 AND rpm.""REPEM04_RutaCodigo""::text <> '614001'::text),
                              ranked_posibilities AS (SELECT p.""Nro_Ticket_Ajustable"",
                                                             p.""Fecha_Pesaje"",
                                                             p.""NUAP"",
                                                             p.""Peso_Ticket"",
                                                             p.""REPEM04_PesoTotal_Toneladas"",
                                                             p.""Toneladas_Ajuste"",
                                                             p.""Area_Aprovechamiento"",
                                                             row_number()
                                                             OVER (PARTITION BY p.""Toneladas_Ajuste"", p.""Nro_Ticket_Ajustable"" ORDER BY p.""REPEM04_PesoTotal_Toneladas"" DESC) AS rn
                                                      FROM posibilities p
                                                      ORDER BY p.""REPEM04_PesoTotal_Toneladas"" DESC)
                         SELECT DISTINCT ON (rp.""NUAP"", (EXTRACT(year FROM rp.""Fecha_Pesaje"")), (EXTRACT(month FROM rp.""Fecha_Pesaje""))) rp.""Nro_Ticket_Ajustable"",
                                                                                                                                         rp.""Fecha_Pesaje"",
                                                                                                                                         EXTRACT(year FROM rp.""Fecha_Pesaje"")  AS ""Año"",
                                                                                                                                         EXTRACT(month FROM rp.""Fecha_Pesaje"") AS ""Mes"",
                                                                                                                                         rp.""NUAP"",
                                                                                                                                         rp.""Peso_Ticket"",
                                                                                                                                         rp.""REPEM04_PesoTotal_Toneladas"",
                                                                                                                                         rp.""Toneladas_Ajuste"",
                                                                                                                                         rp.""Area_Aprovechamiento""
                         FROM ranked_posibilities rp
                         WHERE rp.rn = 1
                         ORDER BY rp.""NUAP"", (EXTRACT(year FROM rp.""Fecha_Pesaje"")), (EXTRACT(month FROM rp.""Fecha_Pesaje"")),
                                  rp.""REPEM04_PesoTotal_Toneladas"" DESC),
                 to_substract
                     AS (SELECT COALESCE(tc.""Nro_Ticket_Compensacion"", cte.""Nro_Ticket_Ajustable"") AS ""Nro_Ticket_Ajustable"",
                                cte.""Fecha_Pesaje"",
                                440405001                                                          AS ""NUAP"",
                                'Medellín'::character varying(20)                                  AS ""Area_Aprovechamiento"",
                                cte.""Mes"",
                                cte.""Año"",
                                sum(cte.""Toneladas_Ajuste"")                                        AS ""Toneladas_Ajuste""
                         FROM cte
                                  LEFT JOIN tickets_compensables tc ON tc.""Id_Ticket"" = cte.""Nro_Ticket_Ajustable"" AND
                                                       (cte.""Nro_Ticket_Ajustable"" IN (SELECT total_compensations.""Id_Ticket""
                                                                                       FROM total_compensations))
                         GROUP BY cte.""Mes"", cte.""Año"", cte.""Nro_Ticket_Ajustable"", cte.""Fecha_Pesaje"",
                                  tc.""Nro_Ticket_Compensacion"")
            SELECT cte.""Nro_Ticket_Ajustable"",
                   cte.""Fecha_Pesaje"",
                   cte.""NUAP"",
                   cte.""Area_Aprovechamiento"",
                   cte.""Peso_Ticket""::numeric(18, 2) AS ""Peso_Ticket"",
                   dm.""Toneladas_Desviacion""         AS ""Toneladas_Ajuste"",
                   cte.""Mes"",
                   cte.""Año""
            FROM cte
                     JOIN ""REPEM_Distribuciones_de_Microrutas"" dm
                          ON dm.""NUAP"" = cte.""NUAP"" AND dm.""Año""::numeric = EXTRACT(year FROM cte.""Fecha_Pesaje"") AND
                             dm.""Mes""::numeric = EXTRACT(month FROM cte.""Fecha_Pesaje"")
            UNION ALL
            SELECT ts.""Nro_Ticket_Ajustable"",
                   ts.""Fecha_Pesaje"",
                   ts.""NUAP"",
                   ts.""Area_Aprovechamiento"",
                   0::numeric(18, 2)       AS ""Peso_Ticket"",
                   - ts.""Toneladas_Ajuste"" AS ""Toneladas_Ajuste"",
                   ts.""Mes"",
                   ts.""Año""
            FROM to_substract ts;
            ");
            
            //Añadir nuevo indice
            migrationBuilder.Sql(@"
                CREATE INDEX IDX_REPEM04_FechaHora_Pesaje_RutaCodigo_PesoTotal_Toneladas
                ON ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" (""REPEM04_FechaHora_Pesaje"", ""REPEM04_RutaCodigo"", ""REPEM04_PesoTotal_Toneladas"" DESC);
            ");

            //Vista de tickets compensables modificada
            migrationBuilder.Sql(@"
            create or replace view ""REPEM_Tickets_Compensables""
                        (""Id_Ticket"", ""Suma_Agrupacion_Toneladas_Por_Compensar"", ""Nro_Ticket_Compensacion"",
                         ""Maximo_Toneladas_Compensables"") as
            WITH cte AS (SELECT td.""Id_Ticket"",
                                td.""Fecha_Hora_Pesaje""::date    AS ""Fecha_Pesaje"",
                                sum(td.""Toneladas_Resultantes"") AS ""Suma_Toneladas_Agrupadas""
                         FROM ""REPEM_Detalle_Compensacion_Tickets"" td
                         WHERE td.""Tipo_Compensacion"" = 'PARCIAL'::text
                            OR td.""Tipo_Compensacion"" = 'TOTAL'::text
                         GROUP BY (td.""Fecha_Hora_Pesaje""::date), td.""Id_Ticket""),
                 ranked_rpm AS (SELECT rpm2.""REPEM04_Id"",
                                       rpm2.""REPEM04_FechaHora_Pesaje""::date                                                                        AS ""RPM_Fecha_Pesaje"",
                                       rpm2.""REPEM04_PesoTotal_Toneladas"",
                                       rank()
                                       OVER (PARTITION BY (rpm2.""REPEM04_FechaHora_Pesaje""::date) ORDER BY rpm2.""REPEM04_PesoTotal_Toneladas"" DESC) AS rank_peso
                                FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm2
                                WHERE rpm2.""REPEM04_RutaCodigo""::text = ANY
                                      (ARRAY ['615618'::text, '618219'::text, '618119'::text, '618319'::text]))
            SELECT cte.""Id_Ticket"",
                   cte.""Suma_Toneladas_Agrupadas""           AS ""Suma_Agrupacion_Toneladas_Por_Compensar"",
                   ranked_rpm.""REPEM04_Id""                  AS ""Nro_Ticket_Compensacion"",
                   ranked_rpm.""REPEM04_PesoTotal_Toneladas"" AS ""Maximo_Toneladas_Compensables""
            FROM cte
                     LEFT JOIN ranked_rpm ON ranked_rpm.""RPM_Fecha_Pesaje"" = cte.""Fecha_Pesaje"" AND ranked_rpm.rank_peso = 1 AND
                                             ranked_rpm.""REPEM04_PesoTotal_Toneladas"" >= cte.""Suma_Toneladas_Agrupadas"";
            ");

            //F14 nuevo, parte de compensaciones
            migrationBuilder.Sql(@"
            create or replace view ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones""
                        (""C1_NUAP"", ""C2_TIPO_SITIO"", ""C3_NUSD"", ""C4_PLACA"", ""C5_FECHA"", ""C6_HORA"", ""C7_NUMICRO"", ""C8_TON_LIMP_URB"",
                         ""C9_TON_BARRIDO"", ""C10_TONRESNA"", ""C11_TONRECHAPR"", ""C12_TONRESAPR"", ""C13_SISTEMA_MEDICION"", ""C14_VLRPEAJ"",
                         ""CE_ID_TICKET"", ""CE_NOMBRE_AREA"", ""CE_RUTA_LARGA"", ""CE_TON_TOTAL"", ""CE_REL_COMPENSACION"",
                         ""CE_REL_COMPENSACION_ID_TICKET"", ""CE_AJUSTE_DECIMAL"")
            as
            WITH cte_comp AS (SELECT rpm_1.""REPEM04_Id""                    AS ""Id_Ticket"",
                                     mic.""REPEM07_NUAP""                    AS ""NUAP"",
                                     mic.""REPEM07_Numero_Ruta_Intendencia"" AS ""Numero_Ruta_Indendencia"",
                                     mic.""REPEM07_Ruta_Larga""              AS ""Ruta_Larga"",
                                     ap.""REPEM02_Nombre""                   AS ""Area_Aprovechamiento"",
                                     CASE
                                         WHEN mic.""REPEM07_NUAP"" = 440405001 THEN rpm_1.""REPEM04_PesoTotal_Toneladas""::numeric -
                                                                                  COALESCE(tr.""Toneladas_Descuento_Medellin"", 0::numeric)
                                         WHEN mic.""REPEM07_NUAP"" = 440705360 AND mic.""REPEM07_Porcentaje_No_Aforado""::numeric <> 0::numeric
                                             THEN tr.""Toneladas_Descuento_Medellin""
                                         ELSE tdha.""Toneladas_Resultantes""
                                         END                               AS ""Calculo_Toneladas"",
                                     CASE
                                         WHEN mic.""REPEM07_Porcentaje_Barrido"" = 0 THEN 0::numeric
                                         ELSE (
                                             CASE
                                                 WHEN mic.""REPEM07_NUAP"" = 440405001 THEN rpm_1.""REPEM04_PesoTotal_Toneladas"" -
                                                                                          COALESCE(tr.""Toneladas_Descuento_Medellin"", 0::numeric)
                                                 WHEN mic.""REPEM07_NUAP"" = 440705360 AND mic.""REPEM07_Porcentaje_No_Aforado"" <> 0
                                                     THEN tr.""Toneladas_Descuento_Medellin""
                                                 ELSE tdha.""Toneladas_Resultantes""
                                                 END * (mic.""REPEM07_Porcentaje_Barrido""::numeric / 100::numeric))::numeric(18, 3)
                                         END::numeric(18, 3)               AS ""Calculo_Barrido"",
                                     0                                     AS ""Calculo_Peaje"",
                                     tdha.""Tipo_Compensacion""
                              FROM ""REPEM_Detalle_Compensacion_Tickets"" tdha
                                       JOIN ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm_1
                                            ON tdha.""Id_Ticket"" = rpm_1.""REPEM04_Id""
                                       JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                                            ON rpm_1.""REPEM04_RutaCodigo""::text = mic.""REPEM07_Numero_de_Microruta""::text AND
                                               mic.""REPEM07_NUAP"" = tdha.""NUAP""
                                       JOIN ""Reporting-Emvarias_02-Areas_de_Aprovechamiento"" ap
                                            ON ap.""REPEM02_Codigo"" = mic.""REPEM07_NUAP""
                                       LEFT JOIN ""REPEM_Distribuciones_de_Microrutas"" dpm ON dpm.""NUAP"" = mic.""REPEM07_NUAP"" AND
                                                                                             dpm.""Año""::numeric =
                                                                                             EXTRACT(year FROM rpm_1.""REPEM04_FechaHora_Pesaje"") AND
                                                                                             dpm.""Mes""::numeric =
                                                                                             EXTRACT(month FROM rpm_1.""REPEM04_FechaHora_Pesaje"")
                                       LEFT JOIN ""Reporting-Emvarias_06-Peajes"" pe
                                                 ON pe.""REPEM06_Placa""::text = rpm_1.""REPEM04_Patente""::text AND
                                                    EXTRACT(year FROM pe.""REPEM06_Fecha_Validez"") =
                                                    EXTRACT(year FROM rpm_1.""REPEM04_FechaHora_Pesaje"")
                                       LEFT JOIN ""REPEM_Descuento_Por_Ticket"" tr ON tr.""Id_Ticket"" = rpm_1.""REPEM04_Id""
                              WHERE tdha.""Tipo_Compensacion"" = 'TOTAL'::text
                                 OR tdha.""Tipo_Compensacion"" = 'PARCIAL'::text)
            SELECT bs.""NUAP""                                              AS ""C1_NUAP"",
                   1                                                      AS ""C2_TIPO_SITIO"",
                   720105237                                              AS ""C3_NUSD"",
                   rpm.""REPEM04_Patente""                                  AS ""C4_PLACA"",
                   rpm.""REPEM04_FechaHora_Pesaje""::date                   AS ""C5_FECHA"",
                   rpm.""REPEM04_FechaHora_Pesaje""::time without time zone AS ""C6_HORA"",
                   bs.""Numero_Ruta_Indendencia""                           AS ""C7_NUMICRO"",
                   0                                                      AS ""C8_TON_LIMP_URB"",
                   0                                                      AS ""C9_TON_BARRIDO"",
                   CASE
                       WHEN rtc.* IS NOT NULL THEN bs.""Calculo_Toneladas"" + COALESCE(cred.""Toneladas_Ajuste"", 0::numeric)
                       ELSE rpm.""REPEM04_PesoTotal_Toneladas""::numeric
                       END                                                AS ""C10_TONRESNA"",
                   COALESCE(tre.""Toneladas_Rechazadas"", 0::numeric)       AS ""C11_TONRECHAPR"",
                   CASE
                       WHEN mr.""REPEM07_Porcentaje_Residuos_Aprovechables""::numeric <> 0::numeric THEN bs.""Calculo_Toneladas"" *
                                                                                                       (mr.""REPEM07_Porcentaje_Residuos_Aprovechables""::numeric / 100::numeric)
                       ELSE 0::numeric
                       END                                                AS ""C12_TONRESAPR"",
                   '1'::text                                              AS ""C13_SISTEMA_MEDICION"",
                   bs.""Calculo_Peaje""                                     AS ""C14_VLRPEAJ"",
                   rtc.""Nro_Ticket_Compensacion""                          AS ""CE_ID_TICKET"",
                   bs.""Area_Aprovechamiento""                              AS ""CE_NOMBRE_AREA"",
                   bs.""Ruta_Larga""                                        AS ""CE_RUTA_LARGA"",
                   rtc.""Maximo_Toneladas_Compensables""                    AS ""CE_TON_TOTAL"",
                   rtc.* IS NOT NULL                                      AS ""CE_REL_COMPENSACION"",
                   CASE
                       WHEN rtc.* IS NOT NULL THEN rtc.""Id_Ticket""
                       ELSE bs.""Id_Ticket""
                       END                                                AS ""CE_REL_COMPENSACION_ID_TICKET"",
                   false                                                  AS ""CE_AJUSTE_DECIMAL""
            FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                     JOIN cte_comp bs ON rpm.""REPEM04_Id"" = bs.""Id_Ticket""
                     JOIN ""Reporting-Emvarias_07-Microrutas"" mr
                          ON mr.""REPEM07_Ruta_Larga"" = bs.""Ruta_Larga"" AND mr.""REPEM07_NUAP"" = bs.""NUAP""
                     JOIN ""REPEM_Tickets_Compensables"" rtc ON rpm.""REPEM04_Id"" = rtc.""Id_Ticket""
                     LEFT JOIN ""REPEM_Toneladas_Rechazos_Agrupadas_Por_Ticket"" tre
                               ON rpm.""REPEM04_Id"" = tre.""Ticket_Asignable"" AND mr.""REPEM07_NUAP"" = 440405001
                     LEFT JOIN ""REPEM_Compensaciones_por_Redondeo"" cred
                               ON rpm.""REPEM04_Id"" = cred.""Nro_Ticket_Ajustable"" AND mr.""REPEM07_NUAP"" = cred.""NUAP"";
            ");

            //F14 unificado
            migrationBuilder.Sql(@"
            create or replace view ""REPEM_Reporte_SUI_Recolecciones_F14""
                        (""C1_NUAP"", ""C2_TIPO_SITIO"", ""C3_NUSD"", ""C4_PLACA"", ""C5_FECHA"", ""C6_HORA"", ""C7_NUMICRO"", ""C8_TON_LIMP_URB"",
                         ""C9_TON_BARRIDO"", ""C10_TONRESNA"", ""C11_TONRECHAPR"", ""C12_TONRESAPR"", ""C13_SISTEMA_MEDICION"", ""C14_VLRPEAJ"",
                         ""CE_ID_TICKET"", ""CE_NOMBRE_AREA"", ""CE_RUTA_LARGA"", ""CE_TON_TOTAL"", ""CE_REL_COMPENSACION"",
                         ""CE_REL_COMPENSACION_ID_TICKET"", ""CE_AJUSTE_DECIMAL"")
            as
            WITH cte AS (SELECT rpm.""REPEM04_Id""                                     AS ""Id_Ticket"",
                                mic.""REPEM07_NUAP""                                   AS ""NUAP"",
                                CASE
                                    WHEN mic.""REPEM07_NUAP"" = 440405001 THEN rpm.""REPEM04_PesoTotal_Toneladas""::numeric -
                                                                             COALESCE(tr.""Toneladas_Descuento_Medellin"", 0::numeric)
                                    WHEN mic.""REPEM07_NUAP"" = 440705360 AND mic.""REPEM07_Porcentaje_No_Aforado""::numeric <> 0::numeric
                                        THEN tr.""Toneladas_Descuento_Medellin""
                                    ELSE tdha.""Toneladas_Resultantes""
                                    END                                              AS ""Calculo_Toneladas"",
                                CASE
                                    WHEN mic.""REPEM07_Porcentaje_Barrido"" = 0 THEN 0::numeric
                                    ELSE (
                                        CASE
                                            WHEN mic.""REPEM07_NUAP"" = 440405001 THEN rpm.""REPEM04_PesoTotal_Toneladas"" -
                                                                                     COALESCE(tr.""Toneladas_Descuento_Medellin"", 0::numeric)
                                            WHEN mic.""REPEM07_NUAP"" = 440705360 AND mic.""REPEM07_Porcentaje_No_Aforado"" <> 0
                                                THEN tr.""Toneladas_Descuento_Medellin""
                                            ELSE tdha.""Toneladas_Resultantes""
                                            END * (mic.""REPEM07_Porcentaje_Barrido""::numeric / 100::numeric))::numeric(18, 3)
                                    END::numeric(18, 3)                              AS ""Calculo_Barrido"",
                                CASE
                                    WHEN pe.* IS NULL THEN 0::numeric
                                    WHEN mic.""REPEM07_NUAP"" = 440405001 THEN (pe.""REPEM06_Valor"" * 2::numeric::double precision -
                                                                              COALESCE(tr.""Peaje_Descuento_Medellin"", 0::numeric)::double precision)::numeric
                                    WHEN mic.""REPEM07_NUAP"" = 440705360 AND mic.""REPEM07_Porcentaje_No_Aforado""::numeric <> 0::numeric
                                        THEN tr.""Peaje_Descuento_Medellin""
                                    ELSE (pe.""REPEM06_Valor"" * 2::numeric::double precision *
                                          (COALESCE(dpm.""Porcentaje_Distribucion_Peaje"", 100::numeric) /
                                           100::numeric)::double precision)::numeric
                                    END                                              AS ""Calculo_Peaje"",
                                COALESCE(tdha.""Tipo_Compensacion"", 'ORIGINAL'::text) AS ""Tipo_Compensacion""
                         FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                                  JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                                       ON rpm.""REPEM04_RutaCodigo""::text = mic.""REPEM07_Numero_de_Microruta""::text
                                  LEFT JOIN ""REPEM_Distribuciones_de_Microrutas"" dpm ON dpm.""NUAP"" = mic.""REPEM07_NUAP"" AND
                                                                                        dpm.""Año""::numeric =
                                                                                        EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"") AND
                                                                                        dpm.""Mes""::numeric =
                                                                                        EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje"")
                                  LEFT JOIN ""Reporting-Emvarias_06-Peajes"" pe
                                            ON pe.""REPEM06_Placa""::text = rpm.""REPEM04_Patente""::text AND
                                               EXTRACT(year FROM pe.""REPEM06_Fecha_Validez"") =
                                               EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"")
                                  LEFT JOIN ""REPEM_Descuento_Por_Ticket"" tr ON tr.""Id_Ticket"" = rpm.""REPEM04_Id""
                                  LEFT JOIN ""REPEM_Detalle_Compensacion_Tickets"" tdha
                                            ON tdha.""Id_Ticket"" = rpm.""REPEM04_Id"" AND tdha.""NUAP"" = mic.""REPEM07_NUAP"" AND
                                               tdha.""Tipo_Compensacion"" = 'ORIGINAL'::text),
                 aggregated_compensations AS (SELECT 0                                                                           AS ""Id_Ticket"",
                                                     ""REPEM_Tickets_Compensables"".""Nro_Ticket_Compensacion"",
                                                     sum(""REPEM_Tickets_Compensables"".""Suma_Agrupacion_Toneladas_Por_Compensar"") AS ""Suma_Agrupacion_Toneladas_Por_Compensar""
                                              FROM ""REPEM_Tickets_Compensables""
                                              GROUP BY ""REPEM_Tickets_Compensables"".""Nro_Ticket_Compensacion"")
            SELECT mic.""REPEM07_NUAP""                                         AS ""C1_NUAP"",
                   1                                                          AS ""C2_TIPO_SITIO"",
                   720105237                                                  AS ""C3_NUSD"",
                   rpm.""REPEM04_Patente""                                      AS ""C4_PLACA"",
                   rpm.""REPEM04_FechaHora_Pesaje""::date                       AS ""C5_FECHA"",
                   rpm.""REPEM04_FechaHora_Pesaje""::time without time zone     AS ""C6_HORA"",
                   mic.""REPEM07_Numero_Ruta_Intendencia""                      AS ""C7_NUMICRO"",
                   0                                                          AS ""C8_TON_LIMP_URB"",
                   round(bs.""Calculo_Barrido"" + COALESCE(tde.""Toneladas_Descuento"", 0::numeric), 3) -
                   COALESCE(dbi.""Valor_A_Descontar"", 0::numeric)              AS ""C9_TON_BARRIDO"",
                   CASE
                       WHEN mic.""REPEM07_Numero_de_Microruta"" = 614001 THEN round(
                               bs.""Calculo_Toneladas"" - COALESCE(rtc.""Suma_Agrupacion_Toneladas_Por_Compensar"", 0::numeric), 3)
                       ELSE round(bs.""Calculo_Toneladas"" - bs.""Calculo_Barrido"" - COALESCE(tde.""Toneladas_Descuento"", 0::numeric) -
                                  COALESCE(rtc.""Suma_Agrupacion_Toneladas_Por_Compensar"", 0::numeric), 3)
                       END + COALESCE(compred.""Toneladas_Ajuste"", 0::numeric) AS ""C10_TONRESNA"",
                   COALESCE(tre.""Toneladas_Rechazadas"", 0::numeric)           AS ""C11_TONRECHAPR"",
                   CASE
                       WHEN mic.""REPEM07_Porcentaje_Residuos_Aprovechables""::numeric <> 0::numeric THEN bs.""Calculo_Toneladas"" *
                                                                                                        (mic.""REPEM07_Porcentaje_Residuos_Aprovechables""::numeric /
                                                                                                         100::numeric)
                       ELSE 0::numeric
                       END                                                    AS ""C12_TONRESAPR"",
                   '1'::text                                                  AS ""C13_SISTEMA_MEDICION"",
                   bs.""Calculo_Peaje""                                         AS ""C14_VLRPEAJ"",
                   rpm.""REPEM04_Id""                                           AS ""CE_ID_TICKET"",
                   ap.""REPEM02_Nombre""                                        AS ""CE_NOMBRE_AREA"",
                   mic.""REPEM07_Ruta_Larga""                                   AS ""CE_RUTA_LARGA"",
                   rpm.""REPEM04_PesoTotal_Toneladas""                          AS ""CE_TON_TOTAL"",
                   rtc.* IS NOT NULL                                          AS ""CE_REL_COMPENSACION"",
                   rtc.""Id_Ticket""                                            AS ""CE_REL_COMPENSACION_ID_TICKET"",
                   compred.* IS NOT NULL                                      AS ""CE_AJUSTE_DECIMAL""
            FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                     JOIN cte bs ON bs.""Calculo_Toneladas"" IS NOT NULL AND rpm.""REPEM04_Id"" = bs.""Id_Ticket""
                     JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                          ON rpm.""REPEM04_RutaCodigo""::text = mic.""REPEM07_Numero_de_Microruta""::text AND
                             mic.""REPEM07_NUAP"" = bs.""NUAP""
                     JOIN ""Reporting-Emvarias_02-Areas_de_Aprovechamiento"" ap ON ap.""REPEM02_Codigo"" = mic.""REPEM07_NUAP""
                     LEFT JOIN ""REPEM_Descuento_Pesaje_Excepcional_Por_Ticket"" tde ON rpm.""REPEM04_Id"" = tde.""NroTicket"" AND
                                                                                      EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"") =
                                                                                      tde.""Año"" AND
                                                                                      EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje"") =
                                                                                      tde.""Mes"" AND
                                                                                      mic.""REPEM07_Numero_de_Microruta"" =
                                                                                      tde.""RutaCodigo"" AND
                                                                                      mic.""REPEM07_NUAP"" = tde.""NUAP""
                     LEFT JOIN ""REPEM_Toneladas_Rechazos_Agrupadas_Por_Ticket"" tre
                               ON rpm.""REPEM04_Id"" = tre.""Ticket_Asignable"" AND mic.""REPEM07_NUAP"" = 440405001
                     LEFT JOIN aggregated_compensations rtc ON rpm.""REPEM04_Id"" = rtc.""Nro_Ticket_Compensacion""
                     LEFT JOIN ""REPEM_Descuento_de_Barrido_Itagui_Por_Ticket"" dbi ON dbi.""Id_Ticket_A_Descontar"" = rpm.""REPEM04_Id""
                     LEFT JOIN ""REPEM_Compensaciones_por_Redondeo"" compred
                               ON compred.""Nro_Ticket_Ajustable"" = rpm.""REPEM04_Id"" AND compred.""NUAP"" = mic.""REPEM07_NUAP""
            UNION ALL
            SELECT ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C1_NUAP"",
                   ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C2_TIPO_SITIO"",
                   ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C3_NUSD"",
                   ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C4_PLACA"",
                   ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C5_FECHA"",
                   ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C6_HORA"",
                   ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C7_NUMICRO"",
                   ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C8_TON_LIMP_URB"",
                   ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C9_TON_BARRIDO"",
                   ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C10_TONRESNA"",
                   ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C11_TONRECHAPR"",
                   ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C12_TONRESAPR"",
                   ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C13_SISTEMA_MEDICION"",
                   ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C14_VLRPEAJ"",
                   ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""CE_ID_TICKET"",
                   ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""CE_NOMBRE_AREA"",
                   ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""CE_RUTA_LARGA"",
                   ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""CE_TON_TOTAL"",
                   ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""CE_REL_COMPENSACION"",
                   ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""CE_REL_COMPENSACION_ID_TICKET"",
                   ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""CE_AJUSTE_DECIMAL""
            FROM ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"";
            ");
            
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            //Vista de Compensaciones
            migrationBuilder.Sql(@"
                create or replace view ""REPEM_Compensaciones_por_Redondeo""
                            (""Nro_Ticket_Ajustable"", ""Fecha_Pesaje"", ""NUAP"", ""Area_Aprovechamiento"", ""Peso_Ticket"", ""Toneladas_Ajuste"",
                             ""Mes"", ""Año"")
                as
                WITH cte AS (WITH posibilities AS (SELECT rpm.""REPEM04_Id""               AS ""Nro_Ticket_Ajustable"",
                                                          rpm.""REPEM04_FechaHora_Pesaje"" AS ""Fecha_Pesaje"",
                                                          dct.""NUAP"",
                                                          dct.""Tipo_Compensacion"",
                                                          dct.""Toneladas_Resultantes""    AS ""Peso_Ticket"",
                                                          rpm.""REPEM04_PesoTotal_Toneladas"",
                                                          dpm.""Toneladas_Desviacion""     AS ""Toneladas_Ajuste"",
                                                          ap.""REPEM02_Nombre""            AS ""Area_Aprovechamiento""
                                                   FROM ""REPEM_Detalle_Compensacion_Tickets"" dct
                                                            JOIN ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                                                                 ON dct.""Id_Ticket"" = rpm.""REPEM04_Id""
                                                            JOIN ""Reporting-Emvarias_03-Pesaje_de_Balanza"" pb
                                                                 ON pb.""REPEM03_Id"" = rpm.""REPEM04_Id""
                                                            JOIN ""Reporting-Emvarias_02-Areas_de_Aprovechamiento"" ap
                                                                 ON ap.""REPEM02_Codigo"" = dct.""NUAP""
                                                            JOIN ""REPEM_Distribuciones_de_Microrutas"" dpm ON dpm.""NUAP"" = dct.""NUAP"" AND
                                                                                                             dpm.""Año""::numeric =
                                                                                                             EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"") AND
                                                                                                             dpm.""Mes""::numeric =
                                                                                                             EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje"") AND
                                                                                                             dct.""Toneladas_Resultantes"" >=
                                                                                                             dpm.""Toneladas_Desviacion""
                                                   WHERE pb.""REPEM03_Fecha_de_cancelacion"" IS NULL
                                                     AND dct.""Tipo_Compensacion"" = 'ORIGINAL'::text
                                                     AND rpm.""REPEM04_RutaCodigo""::text <> '614001'::text),
                                  ranked_posibilities AS (SELECT p.""Nro_Ticket_Ajustable"",
                                                                 p.""Fecha_Pesaje"",
                                                                 p.""NUAP"",
                                                                 p.""Tipo_Compensacion"",
                                                                 p.""Peso_Ticket"",
                                                                 p.""REPEM04_PesoTotal_Toneladas"",
                                                                 p.""Toneladas_Ajuste"",
                                                                 p.""Area_Aprovechamiento"",
                                                                 row_number()
                                                                 OVER (PARTITION BY p.""Toneladas_Ajuste"", p.""Nro_Ticket_Ajustable"" ORDER BY p.""REPEM04_PesoTotal_Toneladas"" DESC) AS rn
                                                          FROM posibilities p
                                                          ORDER BY p.""REPEM04_PesoTotal_Toneladas"" DESC)
                             SELECT DISTINCT ON (rp.""NUAP"", (EXTRACT(year FROM rp.""Fecha_Pesaje"")), (EXTRACT(month FROM rp.""Fecha_Pesaje""))) rp.""Nro_Ticket_Ajustable"",
                                                                                                                                             rp.""Fecha_Pesaje"",
                                                                                                                                             EXTRACT(year FROM rp.""Fecha_Pesaje"")  AS ""Año"",
                                                                                                                                             EXTRACT(month FROM rp.""Fecha_Pesaje"") AS ""Mes"",
                                                                                                                                             rp.""NUAP"",
                                                                                                                                             rp.""Tipo_Compensacion"",
                                                                                                                                             rp.""Peso_Ticket"",
                                                                                                                                             rp.""REPEM04_PesoTotal_Toneladas"",
                                                                                                                                             rp.""Toneladas_Ajuste"",
                                                                                                                                             rp.""Area_Aprovechamiento""
                             FROM ranked_posibilities rp
                             WHERE rp.rn = 1
                             ORDER BY rp.""NUAP"", (EXTRACT(year FROM rp.""Fecha_Pesaje"")), (EXTRACT(month FROM rp.""Fecha_Pesaje"")),
                                      rp.""REPEM04_PesoTotal_Toneladas"" DESC),
                     to_substract AS (SELECT cte.""Nro_Ticket_Ajustable"",
                                             cte.""Fecha_Pesaje"",
                                             440405001                         AS ""NUAP"",
                                             'Medellín'::character varying(20) AS ""Area_Aprovechamiento"",
                                             cte.""Mes"",
                                             cte.""Año"",
                                             sum(cte.""Toneladas_Ajuste"")       AS ""Toneladas_Ajuste""
                                      FROM cte
                                      GROUP BY cte.""Mes"", cte.""Año"", cte.""Nro_Ticket_Ajustable"", cte.""Fecha_Pesaje"")
                SELECT cte.""Nro_Ticket_Ajustable"",
                       cte.""Fecha_Pesaje"",
                       cte.""NUAP"",
                       cte.""Area_Aprovechamiento"",
                       cte.""Peso_Ticket""::numeric(18, 2) AS ""Peso_Ticket"",
                       dm.""Toneladas_Desviacion""         AS ""Toneladas_Ajuste"",
                       cte.""Mes"",
                       cte.""Año""
                FROM cte
                         JOIN ""REPEM_Distribuciones_de_Microrutas"" dm
                              ON dm.""NUAP"" = cte.""NUAP"" AND dm.""Año""::numeric = EXTRACT(year FROM cte.""Fecha_Pesaje"") AND
                                 dm.""Mes""::numeric = EXTRACT(month FROM cte.""Fecha_Pesaje"")
                UNION ALL
                SELECT ts.""Nro_Ticket_Ajustable"",
                       ts.""Fecha_Pesaje"",
                       ts.""NUAP"",
                       ts.""Area_Aprovechamiento"",
                       0::numeric(18, 2)       AS ""Peso_Ticket"",
                       - ts.""Toneladas_Ajuste"" AS ""Toneladas_Ajuste"",
                       ts.""Mes"",
                       ts.""Año""
                FROM to_substract ts;
            ");
            
            //Eliminacion de Indice
            migrationBuilder.Sql(@"
                DROP INDEX IF EXISTS ""IDX_REPEM04_FechaHora_Pesaje_RutaCodigo_PesoTotal_Toneladas"";
            ");
            
            //Vista de tickets compensables antigua
            migrationBuilder.Sql(@"
            create or replace view ""REPEM_Tickets_Compensables""
                        (""Id_Ticket"", ""Suma_Agrupacion_Toneladas_Por_Compensar"", ""Nro_Ticket_Compensacion"",
                         ""Maximo_Toneladas_Compensables"") as
            WITH cte AS (SELECT td.""Id_Ticket"",
                                td.""Fecha_Hora_Pesaje""::date    AS ""Fecha_Pesaje"",
                                sum(td.""Toneladas_Resultantes"") AS ""Suma_Toneladas_Agrupadas""
                         FROM ""REPEM_Detalle_Compensacion_Tickets"" td
                         WHERE td.""Tipo_Compensacion"" = 'PARCIAL'::text
                            OR td.""Tipo_Compensacion"" = 'TOTAL'::text
                         GROUP BY (td.""Fecha_Hora_Pesaje""::date), td.""Id_Ticket"")
            SELECT cte.""Id_Ticket"",
                   cte.""Suma_Toneladas_Agrupadas""    AS ""Suma_Agrupacion_Toneladas_Por_Compensar"",
                   rpm.""REPEM04_Id""                  AS ""Nro_Ticket_Compensacion"",
                   rpm.""REPEM04_PesoTotal_Toneladas"" AS ""Maximo_Toneladas_Compensables""
            FROM cte
                     LEFT JOIN ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                               ON rpm.""REPEM04_Id"" = ((SELECT rpm2.""REPEM04_Id""
                                                       FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm2
                                                       WHERE rpm2.""REPEM04_FechaHora_Pesaje""::date = cte.""Fecha_Pesaje""
                                                         AND (rpm2.""REPEM04_RutaCodigo""::text = ANY
                                                              (ARRAY ['615618'::character varying::text, '618219'::character varying::text, '618119'::character varying::text, '618319'::character varying::text]))
                                                         AND rpm2.""REPEM04_PesoTotal_Toneladas"" >= cte.""Suma_Toneladas_Agrupadas""
                                                       ORDER BY rpm2.""REPEM04_PesoTotal_Toneladas"" DESC
                                                       LIMIT 1));
            ");

            //F14 unificado antiguo
            migrationBuilder.Sql(@"
            create or replace view ""REPEM_Reporte_SUI_Recolecciones_F14""
                        (""C1_NUAP"", ""C2_TIPO_SITIO"", ""C3_NUSD"", ""C4_PLACA"", ""C5_FECHA"", ""C6_HORA"", ""C7_NUMICRO"", ""C8_TON_LIMP_URB"",
                         ""C9_TON_BARRIDO"", ""C10_TONRESNA"", ""C11_TONRECHAPR"", ""C12_TONRESAPR"", ""C13_SISTEMA_MEDICION"", ""C14_VLRPEAJ"",
                         ""CE_ID_TICKET"", ""CE_NOMBRE_AREA"", ""CE_RUTA_LARGA"", ""CE_TON_TOTAL"", ""CE_REL_COMPENSACION"",
                         ""CE_REL_COMPENSACION_ID_TICKET"", ""CE_AJUSTE_DECIMAL"")
            as
            WITH cte AS (SELECT rpm.""REPEM04_Id""                                     AS ""Id_Ticket"",
                                mic.""REPEM07_NUAP""                                   AS ""NUAP"",
                                CASE
                                    WHEN mic.""REPEM07_NUAP"" = 440405001 THEN rpm.""REPEM04_PesoTotal_Toneladas""::numeric -
                                                                             COALESCE(tr.""Toneladas_Descuento_Medellin"", 0::numeric)
                                    WHEN mic.""REPEM07_NUAP"" = 440705360 AND mic.""REPEM07_Porcentaje_No_Aforado""::numeric <> 0::numeric
                                        THEN tr.""Toneladas_Descuento_Medellin""
                                    ELSE tdha.""Toneladas_Resultantes""
                                    END                                              AS ""Calculo_Toneladas"",
                                CASE
                                    WHEN mic.""REPEM07_Porcentaje_Barrido"" = 0 THEN 0::numeric
                                    ELSE (
                                        CASE
                                            WHEN mic.""REPEM07_NUAP"" = 440405001 THEN rpm.""REPEM04_PesoTotal_Toneladas"" -
                                                                                     COALESCE(tr.""Toneladas_Descuento_Medellin"", 0::numeric)
                                            WHEN mic.""REPEM07_NUAP"" = 440705360 AND mic.""REPEM07_Porcentaje_No_Aforado"" <> 0
                                                THEN tr.""Toneladas_Descuento_Medellin""
                                            ELSE tdha.""Toneladas_Resultantes""
                                            END * (mic.""REPEM07_Porcentaje_Barrido""::numeric / 100::numeric))::numeric(18, 3)
                                    END::numeric(18, 3)                              AS ""Calculo_Barrido"",
                                CASE
                                    WHEN pe.* IS NULL THEN 0::numeric
                                    WHEN mic.""REPEM07_NUAP"" = 440405001 THEN (pe.""REPEM06_Valor"" * 2::numeric::double precision -
                                                                              COALESCE(tr.""Peaje_Descuento_Medellin"", 0::numeric)::double precision)::numeric
                                    WHEN mic.""REPEM07_NUAP"" = 440705360 AND mic.""REPEM07_Porcentaje_No_Aforado""::numeric <> 0::numeric
                                        THEN tr.""Peaje_Descuento_Medellin""
                                    ELSE (pe.""REPEM06_Valor"" * 2::numeric::double precision *
                                          (COALESCE(dpm.""Porcentaje_Distribucion_Peaje"", 100::numeric) /
                                           100::numeric)::double precision)::numeric
                                    END                                              AS ""Calculo_Peaje"",
                                COALESCE(tdha.""Tipo_Compensacion"", 'ORIGINAL'::text) AS ""Tipo_Compensacion""
                         FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                                  JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                                       ON rpm.""REPEM04_RutaCodigo""::text = mic.""REPEM07_Numero_de_Microruta""::text
                                  LEFT JOIN ""REPEM_Distribuciones_de_Microrutas"" dpm ON dpm.""NUAP"" = mic.""REPEM07_NUAP"" AND
                                                                                        dpm.""Año""::numeric =
                                                                                        EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"") AND
                                                                                        dpm.""Mes""::numeric =
                                                                                        EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje"")
                                  LEFT JOIN ""Reporting-Emvarias_06-Peajes"" pe
                                            ON pe.""REPEM06_Placa""::text = rpm.""REPEM04_Patente""::text AND
                                               EXTRACT(year FROM pe.""REPEM06_Fecha_Validez"") =
                                               EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"")
                                  LEFT JOIN ""REPEM_Descuento_Por_Ticket"" tr ON tr.""Id_Ticket"" = rpm.""REPEM04_Id""
                                  LEFT JOIN ""REPEM_Detalle_Compensacion_Tickets"" tdha
                                            ON tdha.""Id_Ticket"" = rpm.""REPEM04_Id"" AND tdha.""NUAP"" = mic.""REPEM07_NUAP"" AND
                                               tdha.""Tipo_Compensacion"" = 'ORIGINAL'::text),
                 aggregated_compensations AS (SELECT 0                                                                           AS ""Id_Ticket"",
                                                     ""REPEM_Tickets_Compensables"".""Nro_Ticket_Compensacion"",
                                                     sum(""REPEM_Tickets_Compensables"".""Suma_Agrupacion_Toneladas_Por_Compensar"") AS ""Suma_Agrupacion_Toneladas_Por_Compensar""
                                              FROM ""REPEM_Tickets_Compensables""
                                              GROUP BY ""REPEM_Tickets_Compensables"".""Nro_Ticket_Compensacion"")
            SELECT mic.""REPEM07_NUAP""                                         AS ""C1_NUAP"",
                   1                                                          AS ""C2_TIPO_SITIO"",
                   720105237                                                  AS ""C3_NUSD"",
                   rpm.""REPEM04_Patente""                                      AS ""C4_PLACA"",
                   rpm.""REPEM04_FechaHora_Pesaje""::date                       AS ""C5_FECHA"",
                   rpm.""REPEM04_FechaHora_Pesaje""::time without time zone     AS ""C6_HORA"",
                   mic.""REPEM07_Numero_Ruta_Intendencia""                      AS ""C7_NUMICRO"",
                   0                                                          AS ""C8_TON_LIMP_URB"",
                   round(bs.""Calculo_Barrido"" + COALESCE(tde.""Toneladas_Descuento"", 0::numeric), 3) -
                   COALESCE(dbi.""Valor_A_Descontar"", 0::numeric)              AS ""C9_TON_BARRIDO"",
                   CASE
                       WHEN mic.""REPEM07_Numero_de_Microruta"" = 614001 THEN round(
                               bs.""Calculo_Toneladas"" - COALESCE(rtc.""Suma_Agrupacion_Toneladas_Por_Compensar"", 0::numeric), 3)
                       ELSE round(bs.""Calculo_Toneladas"" - bs.""Calculo_Barrido"" - COALESCE(tde.""Toneladas_Descuento"", 0::numeric) -
                                  COALESCE(rtc.""Suma_Agrupacion_Toneladas_Por_Compensar"", 0::numeric), 3)
                       END + COALESCE(compred.""Toneladas_Ajuste"", 0::numeric) AS ""C10_TONRESNA"",
                   COALESCE(tre.""Toneladas_Rechazadas"", 0::numeric)           AS ""C11_TONRECHAPR"",
                   CASE
                       WHEN mic.""REPEM07_Porcentaje_Residuos_Aprovechables""::numeric <> 0::numeric THEN bs.""Calculo_Toneladas"" *
                                                                                                        (mic.""REPEM07_Porcentaje_Residuos_Aprovechables""::numeric /
                                                                                                         100::numeric)
                       ELSE 0::numeric
                       END                                                    AS ""C12_TONRESAPR"",
                   '1'::text                                                  AS ""C13_SISTEMA_MEDICION"",
                   bs.""Calculo_Peaje""                                         AS ""C14_VLRPEAJ"",
                   rpm.""REPEM04_Id""                                           AS ""CE_ID_TICKET"",
                   ap.""REPEM02_Nombre""                                        AS ""CE_NOMBRE_AREA"",
                   mic.""REPEM07_Ruta_Larga""                                   AS ""CE_RUTA_LARGA"",
                   rpm.""REPEM04_PesoTotal_Toneladas""                          AS ""CE_TON_TOTAL"",
                   rtc.* IS NOT NULL                                          AS ""CE_REL_COMPENSACION"",
                   rtc.""Id_Ticket""                                            AS ""CE_REL_COMPENSACION_ID_TICKET"",
                   compred.* IS NOT NULL                                      AS ""CE_AJUSTE_DECIMAL""
            FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                     JOIN cte bs ON bs.""Calculo_Toneladas"" IS NOT NULL AND rpm.""REPEM04_Id"" = bs.""Id_Ticket""
                     JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                          ON rpm.""REPEM04_RutaCodigo""::text = mic.""REPEM07_Numero_de_Microruta""::text AND
                             mic.""REPEM07_NUAP"" = bs.""NUAP""
                     JOIN ""Reporting-Emvarias_02-Areas_de_Aprovechamiento"" ap ON ap.""REPEM02_Codigo"" = mic.""REPEM07_NUAP""
                     LEFT JOIN ""REPEM_Descuento_Pesaje_Excepcional_Por_Ticket"" tde ON rpm.""REPEM04_Id"" = tde.""NroTicket"" AND
                                                                                      EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"") =
                                                                                      tde.""Año"" AND
                                                                                      EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje"") =
                                                                                      tde.""Mes"" AND
                                                                                      mic.""REPEM07_Numero_de_Microruta"" =
                                                                                      tde.""RutaCodigo"" AND
                                                                                      mic.""REPEM07_NUAP"" = tde.""NUAP""
                     LEFT JOIN ""REPEM_Toneladas_Rechazos_Agrupadas_Por_Ticket"" tre
                               ON rpm.""REPEM04_Id"" = tre.""Ticket_Asignable"" AND mic.""REPEM07_NUAP"" = 440405001
                     LEFT JOIN aggregated_compensations rtc ON rpm.""REPEM04_Id"" = rtc.""Nro_Ticket_Compensacion""
                     LEFT JOIN ""REPEM_Descuento_de_Barrido_Itagui_Por_Ticket"" dbi ON dbi.""Id_Ticket_A_Descontar"" = rpm.""REPEM04_Id""
                     LEFT JOIN ""REPEM_Compensaciones_por_Redondeo"" compred
                               ON compred.""Nro_Ticket_Ajustable"" = rpm.""REPEM04_Id"" AND compred.""NUAP"" = mic.""REPEM07_NUAP""
            UNION ALL
            (WITH cte_comp AS (SELECT rpm_1.""REPEM04_Id""                    AS ""Id_Ticket"",
                                      mic.""REPEM07_NUAP""                    AS ""NUAP"",
                                      mic.""REPEM07_Numero_Ruta_Intendencia"" AS ""Numero_Ruta_Indendencia"",
                                      mic.""REPEM07_Ruta_Larga""              AS ""Ruta_Larga"",
                                      ap.""REPEM02_Nombre""                   AS ""Area_Aprovechamiento"",
                                      CASE
                                          WHEN mic.""REPEM07_NUAP"" = 440405001 THEN rpm_1.""REPEM04_PesoTotal_Toneladas""::numeric -
                                                                                   COALESCE(tr.""Toneladas_Descuento_Medellin"", 0::numeric)
                                          WHEN mic.""REPEM07_NUAP"" = 440705360 AND mic.""REPEM07_Porcentaje_No_Aforado""::numeric <> 0::numeric
                                              THEN tr.""Toneladas_Descuento_Medellin""
                                          ELSE tdha.""Toneladas_Resultantes""
                                          END                               AS ""Calculo_Toneladas"",
                                      CASE
                                          WHEN mic.""REPEM07_Porcentaje_Barrido"" = 0 THEN 0::numeric
                                          ELSE (
                                              CASE
                                                  WHEN mic.""REPEM07_NUAP"" = 440405001 THEN rpm_1.""REPEM04_PesoTotal_Toneladas"" -
                                                                                           COALESCE(tr.""Toneladas_Descuento_Medellin"", 0::numeric)
                                                  WHEN mic.""REPEM07_NUAP"" = 440705360 AND mic.""REPEM07_Porcentaje_No_Aforado"" <> 0
                                                      THEN tr.""Toneladas_Descuento_Medellin""
                                                  ELSE tdha.""Toneladas_Resultantes""
                                                  END * (mic.""REPEM07_Porcentaje_Barrido""::numeric / 100::numeric))::numeric(18, 3)
                                          END::numeric(18, 3)               AS ""Calculo_Barrido"",
                                      0                                     AS ""Calculo_Peaje"",
                                      tdha.""Tipo_Compensacion""
                               FROM ""REPEM_Detalle_Compensacion_Tickets"" tdha
                                        JOIN ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm_1
                                             ON tdha.""Id_Ticket"" = rpm_1.""REPEM04_Id""
                                        JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                                             ON rpm_1.""REPEM04_RutaCodigo""::text = mic.""REPEM07_Numero_de_Microruta""::text AND
                                                mic.""REPEM07_NUAP"" = tdha.""NUAP""
                                        JOIN ""Reporting-Emvarias_02-Areas_de_Aprovechamiento"" ap
                                             ON ap.""REPEM02_Codigo"" = mic.""REPEM07_NUAP""
                                        LEFT JOIN ""REPEM_Distribuciones_de_Microrutas"" dpm ON dpm.""NUAP"" = mic.""REPEM07_NUAP"" AND
                                                                                              dpm.""Año""::numeric =
                                                                                              EXTRACT(year FROM rpm_1.""REPEM04_FechaHora_Pesaje"") AND
                                                                                              dpm.""Mes""::numeric =
                                                                                              EXTRACT(month FROM rpm_1.""REPEM04_FechaHora_Pesaje"")
                                        LEFT JOIN ""Reporting-Emvarias_06-Peajes"" pe
                                                  ON pe.""REPEM06_Placa""::text = rpm_1.""REPEM04_Patente""::text AND
                                                     EXTRACT(year FROM pe.""REPEM06_Fecha_Validez"") =
                                                     EXTRACT(year FROM rpm_1.""REPEM04_FechaHora_Pesaje"")
                                        LEFT JOIN ""REPEM_Descuento_Por_Ticket"" tr ON tr.""Id_Ticket"" = rpm_1.""REPEM04_Id""
                               WHERE tdha.""Tipo_Compensacion"" = 'TOTAL'::text
                                  OR tdha.""Tipo_Compensacion"" = 'PARCIAL'::text)
             SELECT bs.""NUAP""                                              AS ""C1_NUAP"",
                    1                                                      AS ""C2_TIPO_SITIO"",
                    720105237                                              AS ""C3_NUSD"",
                    rpm.""REPEM04_Patente""                                  AS ""C4_PLACA"",
                    rpm.""REPEM04_FechaHora_Pesaje""::date                   AS ""C5_FECHA"",
                    rpm.""REPEM04_FechaHora_Pesaje""::time without time zone AS ""C6_HORA"",
                    bs.""Numero_Ruta_Indendencia""                           AS ""C7_NUMICRO"",
                    0                                                      AS ""C8_TON_LIMP_URB"",
                    0                                                      AS ""C9_TON_BARRIDO"",
                    CASE
                        WHEN rtc.* IS NOT NULL THEN bs.""Calculo_Toneladas""
                        ELSE rpm.""REPEM04_PesoTotal_Toneladas""::numeric
                        END                                                AS ""C10_TONRESNA"",
                    COALESCE(tre.""Toneladas_Rechazadas"", 0::numeric)       AS ""C11_TONRECHAPR"",
                    CASE
                        WHEN mr.""REPEM07_Porcentaje_Residuos_Aprovechables""::numeric <> 0::numeric THEN bs.""Calculo_Toneladas"" *
                                                                                                        (mr.""REPEM07_Porcentaje_Residuos_Aprovechables""::numeric / 100::numeric)
                        ELSE 0::numeric
                        END                                                AS ""C12_TONRESAPR"",
                    '1'::text                                              AS ""C13_SISTEMA_MEDICION"",
                    bs.""Calculo_Peaje""                                     AS ""C14_VLRPEAJ"",
                    rtc.""Nro_Ticket_Compensacion""                          AS ""CE_ID_TICKET"",
                    bs.""Area_Aprovechamiento""                              AS ""CE_NOMBRE_AREA"",
                    bs.""Ruta_Larga""                                        AS ""CE_RUTA_LARGA"",
                    rtc.""Maximo_Toneladas_Compensables""                    AS ""CE_TON_TOTAL"",
                    rtc.* IS NOT NULL                                      AS ""CE_REL_COMPENSACION"",
                    CASE
                        WHEN rtc.* IS NOT NULL THEN rtc.""Id_Ticket""
                        ELSE bs.""Id_Ticket""
                        END                                                AS ""CE_REL_COMPENSACION_ID_TICKET"",
                    false                                                  AS ""CE_AJUSTE_DECIMAL""
             FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                      JOIN cte_comp bs ON rpm.""REPEM04_Id"" = bs.""Id_Ticket""
                      JOIN ""Reporting-Emvarias_07-Microrutas"" mr
                           ON mr.""REPEM07_Ruta_Larga"" = bs.""Ruta_Larga"" AND mr.""REPEM07_NUAP"" = bs.""NUAP""
                      JOIN ""REPEM_Tickets_Compensables"" rtc ON rpm.""REPEM04_Id"" = rtc.""Id_Ticket""
                      LEFT JOIN ""REPEM_Toneladas_Rechazos_Agrupadas_Por_Ticket"" tre
                                ON rpm.""REPEM04_Id"" = tre.""Ticket_Asignable"" AND mr.""REPEM07_NUAP"" = 440405001);
            ");

            //Eliminacion de vista de F14 nuevo, parte de compensaciones (delete REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones)
            migrationBuilder.Sql(@"
                DROP VIEW IF EXISTS ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"";
            ");
        }
    }
}
