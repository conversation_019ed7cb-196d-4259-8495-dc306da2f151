using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Reports.Infrastructure.Data.EFCore.Migrations
{
    public partial class Fix_Adicion_de_Rechazos_en_Compensaciones : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"
                create or replace view ""REPEM_Detalle_Compensacion_Tickets""
                (""Id_Ticket"", ""NUAP"", ""Fecha_Hora_Pesaje"", ""Toneladas_Resultantes"", ""Tipo_Compensacion"") as
                WITH dtc_rows AS (
                SELECT
                        dtc.*,
                        row_number() OVER (
                          PARTITION BY dtc.""Id_Ticket""
                          ORDER BY dtc.""Fecha_Hora_Pesaje"" DESC
                        ) AS RowNum
                    FROM ""REPEM_Dependencia_Toneladas_A_Compensar"" dtc
                 ),
                 cte as (
                    SELECT
                      dtc_rows.""Id_Ticket"",
                      dtc_rows.""NUAP"",
                      dtc_rows.""Numero_de_Microruta"",
                      dtc_rows.""Numero_Ruta_Intendencia"",
                      dtc_rows.""Patente"",
                      dtc_rows.""Toneladas_Distribuidas"",
                      dtc_rows.""PesoTotal_Toneladas"",
                      dtc_rows.""Porcentaje_No_Aforado"",
                      dtc_rows.""Porcentaje_Limpieza_Urbana"",
                      dtc_rows.""Porcentaje_Barrido"",
                      dtc_rows.""Porcentaje_Residuos_Aprovechables"",
                      dtc_rows.""Fecha_Hora_Pesaje"",
                      CASE
                        WHEN dtc_rows.RowNum = 1
                          THEN dtc_rows.""Sumatoria_Acumulada""
                               + COALESCE(rch.""Toneladas_Rechazadas"", 0)
                        ELSE dtc_rows.""Sumatoria_Acumulada""
                      END AS ""Sumatoria_Acumulada"",
                      (dtc_rows.RowNum = 1
                        AND rch.""Toneladas_Rechazadas"" IS NOT NULL
                      ) AS ""Joined"",
                      CASE WHEN dtc_rows.RowNum = 1
                           THEN rch.""Toneladas_Rechazadas""
                      END AS ""Valor_Rechazo"",
                      CASE
                        WHEN dtc_rows.""Sumatoria_Acumulada"" < dtc_rows.""PesoTotal_Toneladas""
                          THEN 0::numeric
                        ELSE dtc_rows.""Sumatoria_Acumulada"" - dtc_rows.""PesoTotal_Toneladas""::numeric
                      END AS ""Exceso_Acumulado"",

                      dtc_rows.RowNum
                    FROM dtc_rows
                    LEFT JOIN ""REPEM_Toneladas_Rechazos_Agrupadas_Por_Ticket"" rch
                      ON rch.""Ticket_Asignable"" = dtc_rows.""Id_Ticket""
                         AND dtc_rows.RowNum = 1
                    ORDER BY dtc_rows.RowNum
                 ),
                 cnt AS (SELECT cte.""Id_Ticket"",
                                count(cte.""Id_Ticket"") AS ""Cantidad""
                         FROM cte
                         GROUP BY cte.""Id_Ticket""),
                 rst AS (SELECT DISTINCT ON (cte.""Id_Ticket"") cte.""Id_Ticket"",
                                                              cte.""PesoTotal_Toneladas"" -
                                                              max(cte.""Sumatoria_Acumulada"") OVER (PARTITION BY cte.""Id_Ticket"") AS ""Resto_Disponible""
                         FROM cte
                                  JOIN cnt ON cnt.""Id_Ticket"" = cte.""Id_Ticket""
                         WHERE cte.""Exceso_Acumulado"" = 0::numeric
                           AND cnt.""Cantidad"" > 1
                         ORDER BY cte.""Id_Ticket"", cte.""Toneladas_Distribuidas"" DESC),
                 sprst AS (SELECT DISTINCT ON (cte.""Id_Ticket"") cte.""Id_Ticket"",
                                                                cte.""Toneladas_Distribuidas"" - cte.""PesoTotal_Toneladas""::numeric AS ""Resto_Disponible""
                           FROM cte
                                    JOIN cnt ON cnt.""Id_Ticket"" = cte.""Id_Ticket""
                           WHERE cte.""Exceso_Acumulado"" > 0::numeric
                             AND cnt.""Cantidad"" = 1
                           ORDER BY cte.""Id_Ticket"", cte.""Toneladas_Distribuidas"" DESC),
                 pcomp AS (SELECT cte.""Id_Ticket"",
                                  cte.""NUAP"",
                                  cte.""Numero_de_Microruta"",
                                  cte.""Numero_Ruta_Intendencia"",
                                  cte.""Patente"",
                                  cte.""Toneladas_Distribuidas"",
                                  cte.""PesoTotal_Toneladas"",
                                  cte.""Porcentaje_No_Aforado"",
                                  cte.""Porcentaje_Limpieza_Urbana"",
                                  cte.""Porcentaje_Barrido"",
                                  cte.""Porcentaje_Residuos_Aprovechables"",
                                  cte.""Fecha_Hora_Pesaje"",
                                  cte.""Sumatoria_Acumulada"",
                                  cte.""Exceso_Acumulado"",
                                  cte.""Toneladas_Distribuidas"" - rst.""Resto_Disponible"" AS ""Toneladas_Resultantes""
                           FROM cte
                                    JOIN (SELECT icte.""Id_Ticket"",
                                                 max(icte.""Toneladas_Distribuidas"") AS max_toneladas
                                          FROM cte icte
                                          GROUP BY icte.""Id_Ticket"") sub
                                         ON cte.""Id_Ticket"" = sub.""Id_Ticket"" AND cte.""Toneladas_Distribuidas"" = sub.max_toneladas
                                    JOIN rst ON rst.""Id_Ticket"" = cte.""Id_Ticket""
                           WHERE cte.""Exceso_Acumulado"" > 0::numeric
                           UNION ALL
                           SELECT cte.""Id_Ticket"",
                                  cte.""NUAP"",
                                  cte.""Numero_de_Microruta"",
                                  cte.""Numero_Ruta_Intendencia"",
                                  cte.""Patente"",
                                  cte.""Toneladas_Distribuidas"",
                                  cte.""PesoTotal_Toneladas"",
                                  cte.""Porcentaje_No_Aforado"",
                                  cte.""Porcentaje_Limpieza_Urbana"",
                                  cte.""Porcentaje_Barrido"",
                                  cte.""Porcentaje_Residuos_Aprovechables"",
                                  cte.""Fecha_Hora_Pesaje"",
                                  cte.""Sumatoria_Acumulada"",
                                  cte.""Exceso_Acumulado"",
                                  sprst.""Resto_Disponible"" AS ""Toneladas_Resultantes""
                           FROM cte
                                    JOIN sprst ON sprst.""Id_Ticket"" = cte.""Id_Ticket"")
            SELECT cte.""Id_Ticket"",
                   cte.""NUAP"",
                   cte.""Fecha_Hora_Pesaje"",
                   cte.""Toneladas_Distribuidas"" - COALESCE(pcomp.""Toneladas_Resultantes"", 0::numeric) AS ""Toneladas_Resultantes"",
                   CASE
                       WHEN cte.""Exceso_Acumulado"" = 0::numeric OR pcomp.* IS NOT NULL THEN 'ORIGINAL'::text
                       ELSE 'TOTAL'::text
                       END                                                                            AS ""Tipo_Compensacion""
            FROM cte
                     LEFT JOIN pcomp ON pcomp.""Id_Ticket"" = cte.""Id_Ticket"" AND cte.""NUAP"" = pcomp.""NUAP""
            UNION ALL
            SELECT pcomp.""Id_Ticket"",
                   pcomp.""NUAP"",
                   pcomp.""Fecha_Hora_Pesaje"",
                   pcomp.""Toneladas_Resultantes"",
                   'PARCIAL'::text AS ""Tipo_Compensacion""
            FROM pcomp;
            "); 
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"
                create or replace view ""REPEM_Detalle_Compensacion_Tickets""
                (""Id_Ticket"", ""NUAP"", ""Fecha_Hora_Pesaje"", ""Toneladas_Resultantes"", ""Tipo_Compensacion"") as
                WITH cte AS (SELECT dtc.""Id_Ticket"",
                                    dtc.""NUAP"",
                                    dtc.""Numero_de_Microruta"",
                                    dtc.""Numero_Ruta_Intendencia"",
                                    dtc.""Patente"",
                                    dtc.""Toneladas_Distribuidas"",
                                    dtc.""PesoTotal_Toneladas"",
                                    dtc.""Porcentaje_No_Aforado"",
                                    dtc.""Porcentaje_Limpieza_Urbana"",
                                    dtc.""Porcentaje_Barrido"",
                                    dtc.""Porcentaje_Residuos_Aprovechables"",
                                    dtc.""Fecha_Hora_Pesaje"",
                                    dtc.""Sumatoria_Acumulada"",
                                    CASE
                                        WHEN dtc.""Sumatoria_Acumulada"" < dtc.""PesoTotal_Toneladas"" THEN 0::numeric
                                        ELSE dtc.""Sumatoria_Acumulada"" - dtc.""PesoTotal_Toneladas""::numeric
                                        END AS ""Exceso_Acumulado""
                             FROM ""REPEM_Dependencia_Toneladas_A_Compensar"" dtc),
                     cnt AS (SELECT cte.""Id_Ticket"",
                                    count(cte.""Id_Ticket"") AS ""Cantidad""
                             FROM cte
                             GROUP BY cte.""Id_Ticket""),
                     rst AS (SELECT DISTINCT ON (cte.""Id_Ticket"") cte.""Id_Ticket"",
                                                                  cte.""PesoTotal_Toneladas"" -
                                                                  max(cte.""Sumatoria_Acumulada"") OVER (PARTITION BY cte.""Id_Ticket"") AS ""Resto_Disponible""
                             FROM cte
                                      JOIN cnt ON cnt.""Id_Ticket"" = cte.""Id_Ticket""
                             WHERE cte.""Exceso_Acumulado"" = 0::numeric
                               AND cnt.""Cantidad"" > 1
                             ORDER BY cte.""Id_Ticket"", cte.""Toneladas_Distribuidas"" DESC),
                     sprst AS (SELECT DISTINCT ON (cte.""Id_Ticket"") cte.""Id_Ticket"",
                                                                    cte.""Toneladas_Distribuidas"" - cte.""PesoTotal_Toneladas""::numeric AS ""Resto_Disponible""
                               FROM cte
                                        JOIN cnt ON cnt.""Id_Ticket"" = cte.""Id_Ticket""
                               WHERE cte.""Exceso_Acumulado"" > 0::numeric
                                 AND cnt.""Cantidad"" = 1
                               ORDER BY cte.""Id_Ticket"", cte.""Toneladas_Distribuidas"" DESC),
                     pcomp AS (SELECT cte.""Id_Ticket"",
                                      cte.""NUAP"",
                                      cte.""Numero_de_Microruta"",
                                      cte.""Numero_Ruta_Intendencia"",
                                      cte.""Patente"",
                                      cte.""Toneladas_Distribuidas"",
                                      cte.""PesoTotal_Toneladas"",
                                      cte.""Porcentaje_No_Aforado"",
                                      cte.""Porcentaje_Limpieza_Urbana"",
                                      cte.""Porcentaje_Barrido"",
                                      cte.""Porcentaje_Residuos_Aprovechables"",
                                      cte.""Fecha_Hora_Pesaje"",
                                      cte.""Sumatoria_Acumulada"",
                                      cte.""Exceso_Acumulado"",
                                      cte.""Toneladas_Distribuidas"" - rst.""Resto_Disponible"" AS ""Toneladas_Resultantes""
                               FROM cte
                                        JOIN (SELECT icte.""Id_Ticket"",
                                                     max(icte.""Toneladas_Distribuidas"") AS max_toneladas
                                              FROM cte icte
                                              GROUP BY icte.""Id_Ticket"") sub
                                             ON cte.""Id_Ticket"" = sub.""Id_Ticket"" AND cte.""Toneladas_Distribuidas"" = sub.max_toneladas
                                        JOIN rst ON rst.""Id_Ticket"" = cte.""Id_Ticket""
                               WHERE cte.""Exceso_Acumulado"" > 0::numeric
                               UNION ALL
                               SELECT cte.""Id_Ticket"",
                                      cte.""NUAP"",
                                      cte.""Numero_de_Microruta"",
                                      cte.""Numero_Ruta_Intendencia"",
                                      cte.""Patente"",
                                      cte.""Toneladas_Distribuidas"",
                                      cte.""PesoTotal_Toneladas"",
                                      cte.""Porcentaje_No_Aforado"",
                                      cte.""Porcentaje_Limpieza_Urbana"",
                                      cte.""Porcentaje_Barrido"",
                                      cte.""Porcentaje_Residuos_Aprovechables"",
                                      cte.""Fecha_Hora_Pesaje"",
                                      cte.""Sumatoria_Acumulada"",
                                      cte.""Exceso_Acumulado"",
                                      sprst.""Resto_Disponible"" AS ""Toneladas_Resultantes""
                               FROM cte
                                        JOIN sprst ON sprst.""Id_Ticket"" = cte.""Id_Ticket"")
                SELECT cte.""Id_Ticket"",
                       cte.""NUAP"",
                       cte.""Fecha_Hora_Pesaje"",
                       cte.""Toneladas_Distribuidas"" - COALESCE(pcomp.""Toneladas_Resultantes"", 0::numeric) AS ""Toneladas_Resultantes"",
                       CASE
                           WHEN cte.""Exceso_Acumulado"" = 0::numeric OR pcomp.* IS NOT NULL THEN 'ORIGINAL'::text
                           ELSE 'TOTAL'::text
                           END                                                                            AS ""Tipo_Compensacion""
                FROM cte
                         LEFT JOIN pcomp ON pcomp.""Id_Ticket"" = cte.""Id_Ticket"" AND cte.""NUAP"" = pcomp.""NUAP""
                UNION ALL
                SELECT pcomp.""Id_Ticket"",
                       pcomp.""NUAP"",
                       pcomp.""Fecha_Hora_Pesaje"",
                       pcomp.""Toneladas_Resultantes"",
                       'PARCIAL'::text AS ""Tipo_Compensacion""
                FROM pcomp;
            ");
        }
    }
}
