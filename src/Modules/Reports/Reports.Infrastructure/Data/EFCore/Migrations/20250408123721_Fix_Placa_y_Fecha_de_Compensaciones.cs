using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Reports.Infrastructure.Data.EFCore.Migrations
{
    public partial class Fix_Placa_y_Fecha_de_Compensaciones : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"
                create or replace view ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones""
                            (""C1_NUAP"", ""C2_TIPO_SITIO"", ""C3_NUSD"", ""C4_PLACA"", ""C5_FECHA"", ""C6_HORA"", ""C7_NUMICRO"", ""C8_TON_LIMP_URB"",
                             ""C9_TON_BARRIDO"", ""C10_TONRESNA"", ""C11_TONRECHAPR"", ""C12_TONRESAPR"", ""C13_SISTEMA_MEDICION"", ""C14_VLRPEAJ"",
                             ""CE_ID_TICKET"", ""CE_NOMBRE_AREA"", ""CE_RUTA_LARGA"", ""CE_TON_TOTAL"", ""CE_REL_COMPENSACION"",
                             ""CE_REL_COMPENSACION_ID_TICKET"", ""CE_AJUSTE_DECIMAL"")
                as
                WITH cte_comp AS (
                    SELECT
                        tdha.""Id_Ticket"",
                        tdha.""NUAP"",
                        tdha.""Toneladas_Resultantes"",
                        tdha.""Tipo_Compensacion"",
                        tdha.""Tipo_Compensacion"",
                        rpm_1.""REPEM04_PesoTotal_Toneladas"",
                        mic.""REPEM07_Numero_Ruta_Intendencia"" AS ""Numero_Ruta_Indendencia"",
                        mic.""REPEM07_Ruta_Larga"",
                        mic.""REPEM07_Porcentaje_No_Aforado"",
                        mic.""REPEM07_Porcentaje_Barrido"",
                        mic.""REPEM07_Porcentaje_Residuos_Aprovechables"",
                        ap.""REPEM02_Nombre""                   AS ""Area_Aprovechamiento"",
                        tr.""Toneladas_Descuento_Medellin""
                    FROM ""REPEM_Detalle_Compensacion_Tickets"" tdha
                    JOIN ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm_1
                        ON tdha.""Id_Ticket"" = rpm_1.""REPEM04_Id""
                    JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                        ON rpm_1.""REPEM04_RutaCodigo""::text = mic.""REPEM07_Numero_de_Microruta""::text
                        AND mic.""REPEM07_NUAP"" = tdha.""NUAP""
                    JOIN ""Reporting-Emvarias_02-Areas_de_Aprovechamiento"" ap
                        ON ap.""REPEM02_Codigo"" = mic.""REPEM07_NUAP""
                    LEFT JOIN ""REPEM_Descuento_Por_Ticket"" tr ON tr.""Id_Ticket"" = rpm_1.""REPEM04_Id""
                    WHERE tdha.""Tipo_Compensacion"" IN ('TOTAL', 'PARCIAL')
                ),
                calculated_comp AS (
                    SELECT
                        c.*,
                        CASE
                            WHEN c.""NUAP"" = 440405001 THEN c.""REPEM04_PesoTotal_Toneladas""::numeric - COALESCE(c.""Toneladas_Descuento_Medellin"", 0::numeric)
                            WHEN c.""NUAP"" = 440705360 AND c.""REPEM07_Porcentaje_No_Aforado""::numeric <> 0::numeric THEN COALESCE(c.""Toneladas_Descuento_Medellin"", 0::numeric)
                            ELSE c.""Toneladas_Resultantes""
                        END AS ""Calculo_Toneladas""
                    FROM cte_comp c
                )
                SELECT
                    cc.""NUAP""                                                     AS ""C1_NUAP"",
                    1                                                             AS ""C2_TIPO_SITIO"",
                    720105237                                                     AS ""C3_NUSD"",
                    compensation.""REPEM04_Patente""                                AS ""C4_PLACA"",
                    compensation.""REPEM04_FechaHora_Pesaje""::date                 AS ""C5_FECHA"",
                    compensation.""REPEM04_FechaHora_Pesaje""::time without time zone AS ""C6_HORA"",
                    cc.""Numero_Ruta_Indendencia""                                  AS ""C7_NUMICRO"",
                    0                                                             AS ""C8_TON_LIMP_URB"",
                    0                                                             AS ""C9_TON_BARRIDO"",
                    cc.""Calculo_Toneladas"" + COALESCE(cred.""Toneladas_Ajuste"", 0::numeric) AS ""C10_TONRESNA"",
                    COALESCE(tre.""Toneladas_Rechazadas"", 0::numeric)              AS ""C11_TONRECHAPR"",
                    CASE
                        WHEN cc.""REPEM07_Porcentaje_Residuos_Aprovechables""::numeric <> 0::numeric
                        THEN cc.""Calculo_Toneladas"" * (cc.""REPEM07_Porcentaje_Residuos_Aprovechables""::numeric / 100::numeric)
                        ELSE 0::numeric
                    END                                                           AS ""C12_TONRESAPR"",
                    '1'::text                                                     AS ""C13_SISTEMA_MEDICION"",
                    0                                                             AS ""C14_VLRPEAJ"",
                    rtc.""Nro_Ticket_Compensacion""                                 AS ""CE_ID_TICKET"",
                    cc.""Area_Aprovechamiento""                                     AS ""CE_NOMBRE_AREA"",
                    cc.""REPEM07_Ruta_Larga""                                       AS ""CE_RUTA_LARGA"",
                    rtc.""Maximo_Toneladas_Compensables""                           AS ""CE_TON_TOTAL"",
                    TRUE                                                          AS ""CE_REL_COMPENSACION"",
                    rtc.""Id_Ticket""                                               AS ""CE_REL_COMPENSACION_ID_TICKET"",
                    false                                                         AS ""CE_AJUSTE_DECIMAL""
                FROM calculated_comp cc
                JOIN ""REPEM_Tickets_Compensables"" rtc ON cc.""Id_Ticket"" = rtc.""Id_Ticket""
                JOIN ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" compensation
                    ON compensation.""REPEM04_Id"" = rtc.""Nro_Ticket_Compensacion""
                LEFT JOIN ""REPEM_Toneladas_Rechazos_Agrupadas_Por_Ticket"" tre
                    ON cc.""Id_Ticket"" = tre.""Ticket_Asignable"" AND cc.""NUAP"" = 440405001
                LEFT JOIN ""REPEM_Compensaciones_por_Redondeo"" cred
                    ON cc.""Id_Ticket"" = cred.""Nro_Ticket_Ajustable"" AND cc.""NUAP"" = cred.""NUAP"";
            ");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"
                create or replace view ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones""
                            (""C1_NUAP"", ""C2_TIPO_SITIO"", ""C3_NUSD"", ""C4_PLACA"", ""C5_FECHA"", ""C6_HORA"", ""C7_NUMICRO"", ""C8_TON_LIMP_URB"",
                             ""C9_TON_BARRIDO"", ""C10_TONRESNA"", ""C11_TONRECHAPR"", ""C12_TONRESAPR"", ""C13_SISTEMA_MEDICION"", ""C14_VLRPEAJ"",
                             ""CE_ID_TICKET"", ""CE_NOMBRE_AREA"", ""CE_RUTA_LARGA"", ""CE_TON_TOTAL"", ""CE_REL_COMPENSACION"",
                             ""CE_REL_COMPENSACION_ID_TICKET"", ""CE_AJUSTE_DECIMAL"")
                as
                WITH cte_comp AS (SELECT rpm_1.""REPEM04_Id""                    AS ""Id_Ticket"",
                                         mic.""REPEM07_NUAP""                    AS ""NUAP"",
                                         mic.""REPEM07_Numero_Ruta_Intendencia"" AS ""Numero_Ruta_Indendencia"",
                                         mic.""REPEM07_Ruta_Larga""              AS ""Ruta_Larga"",
                                         ap.""REPEM02_Nombre""                   AS ""Area_Aprovechamiento"",
                                         CASE
                                             WHEN mic.""REPEM07_NUAP"" = 440405001 THEN rpm_1.""REPEM04_PesoTotal_Toneladas""::numeric -
                                                                                      COALESCE(tr.""Toneladas_Descuento_Medellin"", 0::numeric)
                                             WHEN mic.""REPEM07_NUAP"" = 440705360 AND mic.""REPEM07_Porcentaje_No_Aforado""::numeric <> 0::numeric
                                                 THEN tr.""Toneladas_Descuento_Medellin""
                                             ELSE tdha.""Toneladas_Resultantes""
                                             END                               AS ""Calculo_Toneladas"",
                                         CASE
                                             WHEN mic.""REPEM07_Porcentaje_Barrido"" = 0 THEN 0::numeric
                                             ELSE (
                                                 CASE
                                                     WHEN mic.""REPEM07_NUAP"" = 440405001 THEN rpm_1.""REPEM04_PesoTotal_Toneladas"" -
                                                                                              COALESCE(tr.""Toneladas_Descuento_Medellin"", 0::numeric)
                                                     WHEN mic.""REPEM07_NUAP"" = 440705360 AND mic.""REPEM07_Porcentaje_No_Aforado"" <> 0
                                                         THEN tr.""Toneladas_Descuento_Medellin""
                                                     ELSE tdha.""Toneladas_Resultantes""
                                                     END * (mic.""REPEM07_Porcentaje_Barrido""::numeric / 100::numeric))::numeric(18, 3)
                                             END::numeric(18, 3)               AS ""Calculo_Barrido"",
                                         0                                     AS ""Calculo_Peaje"",
                                         tdha.""Tipo_Compensacion""
                                  FROM ""REPEM_Detalle_Compensacion_Tickets"" tdha
                                           JOIN ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm_1
                                                ON tdha.""Id_Ticket"" = rpm_1.""REPEM04_Id""
                                           JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                                                ON rpm_1.""REPEM04_RutaCodigo""::text = mic.""REPEM07_Numero_de_Microruta""::text AND
                                                   mic.""REPEM07_NUAP"" = tdha.""NUAP""
                                           JOIN ""Reporting-Emvarias_02-Areas_de_Aprovechamiento"" ap
                                                ON ap.""REPEM02_Codigo"" = mic.""REPEM07_NUAP""
                                           LEFT JOIN ""REPEM_Distribuciones_de_Microrutas"" dpm ON dpm.""NUAP"" = mic.""REPEM07_NUAP"" AND
                                                                                                 dpm.""Año""::numeric =
                                                                                                 EXTRACT(year FROM rpm_1.""REPEM04_FechaHora_Pesaje"") AND
                                                                                                 dpm.""Mes""::numeric =
                                                                                                 EXTRACT(month FROM rpm_1.""REPEM04_FechaHora_Pesaje"")
                                           LEFT JOIN ""Reporting-Emvarias_06-Peajes"" pe
                                                     ON pe.""REPEM06_Placa""::text = rpm_1.""REPEM04_Patente""::text AND
                                                        EXTRACT(year FROM pe.""REPEM06_Fecha_Validez"") =
                                                        EXTRACT(year FROM rpm_1.""REPEM04_FechaHora_Pesaje"")
                                           LEFT JOIN ""REPEM_Descuento_Por_Ticket"" tr ON tr.""Id_Ticket"" = rpm_1.""REPEM04_Id""
                                  WHERE tdha.""Tipo_Compensacion"" = 'TOTAL'::text
                                     OR tdha.""Tipo_Compensacion"" = 'PARCIAL'::text)
                SELECT bs.""NUAP""                                              AS ""C1_NUAP"",
                       1                                                      AS ""C2_TIPO_SITIO"",
                       720105237                                              AS ""C3_NUSD"",
                       rpm.""REPEM04_Patente""                                  AS ""C4_PLACA"",
                       rpm.""REPEM04_FechaHora_Pesaje""::date                   AS ""C5_FECHA"",
                       rpm.""REPEM04_FechaHora_Pesaje""::time without time zone AS ""C6_HORA"",
                       bs.""Numero_Ruta_Indendencia""                           AS ""C7_NUMICRO"",
                       0                                                      AS ""C8_TON_LIMP_URB"",
                       0                                                      AS ""C9_TON_BARRIDO"",
                       CASE
                           WHEN rtc.* IS NOT NULL THEN bs.""Calculo_Toneladas"" + COALESCE(cred.""Toneladas_Ajuste"", 0::numeric)
                           ELSE rpm.""REPEM04_PesoTotal_Toneladas""::numeric
                           END                                                AS ""C10_TONRESNA"",
                       COALESCE(tre.""Toneladas_Rechazadas"", 0::numeric)       AS ""C11_TONRECHAPR"",
                       CASE
                           WHEN mr.""REPEM07_Porcentaje_Residuos_Aprovechables""::numeric <> 0::numeric THEN bs.""Calculo_Toneladas"" *
                                                                                                           (mr.""REPEM07_Porcentaje_Residuos_Aprovechables""::numeric / 100::numeric)
                           ELSE 0::numeric
                           END                                                AS ""C12_TONRESAPR"",
                       '1'::text                                              AS ""C13_SISTEMA_MEDICION"",
                       bs.""Calculo_Peaje""                                     AS ""C14_VLRPEAJ"",
                       rtc.""Nro_Ticket_Compensacion""                          AS ""CE_ID_TICKET"",
                       bs.""Area_Aprovechamiento""                              AS ""CE_NOMBRE_AREA"",
                       bs.""Ruta_Larga""                                        AS ""CE_RUTA_LARGA"",
                       rtc.""Maximo_Toneladas_Compensables""                    AS ""CE_TON_TOTAL"",
                       rtc.* IS NOT NULL                                      AS ""CE_REL_COMPENSACION"",
                       CASE
                           WHEN rtc.* IS NOT NULL THEN rtc.""Id_Ticket""
                           ELSE bs.""Id_Ticket""
                           END                                                AS ""CE_REL_COMPENSACION_ID_TICKET"",
                       false                                                  AS ""CE_AJUSTE_DECIMAL""
                FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                         JOIN cte_comp bs ON rpm.""REPEM04_Id"" = bs.""Id_Ticket""
                         JOIN ""Reporting-Emvarias_07-Microrutas"" mr
                              ON mr.""REPEM07_Ruta_Larga"" = bs.""Ruta_Larga"" AND mr.""REPEM07_NUAP"" = bs.""NUAP""
                         JOIN ""REPEM_Tickets_Compensables"" rtc ON rpm.""REPEM04_Id"" = rtc.""Id_Ticket""
                         LEFT JOIN ""REPEM_Toneladas_Rechazos_Agrupadas_Por_Ticket"" tre
                                   ON rpm.""REPEM04_Id"" = tre.""Ticket_Asignable"" AND mr.""REPEM07_NUAP"" = 440405001
                         LEFT JOIN ""REPEM_Compensaciones_por_Redondeo"" cred
                                   ON rpm.""REPEM04_Id"" = cred.""Nro_Ticket_Ajustable"" AND mr.""REPEM07_NUAP"" = cred.""NUAP"";
            ");
        }
    }
}
