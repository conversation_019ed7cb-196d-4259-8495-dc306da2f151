using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Reports.Infrastructure.Data.EFCore.Migrations
{
    public partial class New_RSUI_Tables_And_Drop_Old_Recollection_Table : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"
                DROP VIEW public.""View_Reporte_Formato_14"";
                DROP VIEW public.""View_Reporte_Formato_34"";"
            );
            
            migrationBuilder.DropTable(
                name: "Reporting-Emvarias_04-Recoleccion_por_Microruta");

            migrationBuilder.DropPrimaryKey(
                name: "Reporting-Emvarias_07-Microrutas_key",
                table: "Reporting-Emvarias_07-Microrutas");

            migrationBuilder.DropPrimaryKey(
                name: "Reporting-Emvarias_05-Recoleccion_Vehicular_key",
                table: "Reporting-Emvarias_05-Recoleccion_Vehicular");

            migrationBuilder.DropColumn(
                name: "REPEM07_Ruta_de_Higiene",
                table: "Reporting-Emvarias_07-Microrutas");

            migrationBuilder.DropColumn(
                name: "REPEM05_Id",
                table: "Reporting-Emvarias_05-Recoleccion_Vehicular");

            migrationBuilder.DropColumn(
                name: "REPEM05_Codigo_DANE",
                table: "Reporting-Emvarias_05-Recoleccion_Vehicular");

            migrationBuilder.DropColumn(
                name: "REPEM05_Empresa",
                table: "Reporting-Emvarias_05-Recoleccion_Vehicular");

            migrationBuilder.DropColumn(
                name: "REPEM05_FechaHora_Recoleccion",
                table: "Reporting-Emvarias_05-Recoleccion_Vehicular");

            migrationBuilder.DropColumn(
                name: "REPEM05_NIT",
                table: "Reporting-Emvarias_05-Recoleccion_Vehicular");

            migrationBuilder.DropColumn(
                name: "REPEM05_Patente",
                table: "Reporting-Emvarias_05-Recoleccion_Vehicular");

            migrationBuilder.AddColumn<long>(
                name: "REPEM07_Numero_Ruta_Intendencia",
                table: "Reporting-Emvarias_07-Microrutas",
                type: "bigint",
                nullable: false,
                defaultValue: 0L);

            migrationBuilder.AddColumn<int>(
                name: "REPEM07_Porcentaje_Barrido",
                table: "Reporting-Emvarias_07-Microrutas",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "REPEM07_Porcentaje_Limpieza_Urbana",
                table: "Reporting-Emvarias_07-Microrutas",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "REPEM07_Porcentaje_No_Aforado",
                table: "Reporting-Emvarias_07-Microrutas",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "REPEM07_Porcentaje_Residuos_Aprovechables",
                table: "Reporting-Emvarias_07-Microrutas",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "REPEM07_Ruta_Larga",
                table: "Reporting-Emvarias_07-Microrutas",
                type: "char(7)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AlterColumn<string>(
                name: "REPEM06_Codigo_Tipo_Vehiculo",
                table: "Reporting-Emvarias_06-Peajes",
                type: "character varying(50)",
                maxLength: 50,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(10)",
                oldMaxLength: 10);

            migrationBuilder.AddColumn<DateTime>(
                name: "REPEM06_Fecha_Validez",
                table: "Reporting-Emvarias_06-Peajes",
                type: "timestamp without time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "REPEM06_Placa",
                table: "Reporting-Emvarias_06-Peajes",
                type: "character varying(6)",
                maxLength: 6,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<int>(
                name: "REPEM05_Año",
                table: "Reporting-Emvarias_05-Recoleccion_Vehicular",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "REPEM05_Mes",
                table: "Reporting-Emvarias_05-Recoleccion_Vehicular",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AlterColumn<string>(
                name: "REPEM03_Tipo_de_carga",
                table: "Reporting-Emvarias_03-Pesaje_de_Balanza",
                type: "char(1)",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "text");

            migrationBuilder.AlterColumn<string>(
                name: "REPEM03_Patente",
                table: "Reporting-Emvarias_03-Pesaje_de_Balanza",
                type: "character varying(6)",
                maxLength: 6,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "text");

            migrationBuilder.AlterColumn<string>(
                name: "REPEM03_Municipio",
                table: "Reporting-Emvarias_03-Pesaje_de_Balanza",
                type: "character varying(5)",
                maxLength: 5,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "text");

            migrationBuilder.AlterColumn<string>(
                name: "REPEM02_Nombre",
                table: "Reporting-Emvarias_02-Areas_de_Aprovechamiento",
                type: "character varying(20)",
                maxLength: 20,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "text");

            migrationBuilder.AlterColumn<string>(
                name: "REPEM01_Provincia",
                table: "Reporting-Emvarias_01-Municipios",
                type: "character varying(50)",
                maxLength: 50,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "text");

            migrationBuilder.AlterColumn<string>(
                name: "REPEM01_Nombre",
                table: "Reporting-Emvarias_01-Municipios",
                type: "character varying(50)",
                maxLength: 50,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "text");

            migrationBuilder.AlterColumn<string>(
                name: "REPEM01_Departamento",
                table: "Reporting-Emvarias_01-Municipios",
                type: "character varying(50)",
                maxLength: 50,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "text");

            migrationBuilder.AlterColumn<string>(
                name: "REPEM01_Codigo",
                table: "Reporting-Emvarias_01-Municipios",
                type: "character varying(5)",
                maxLength: 5,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "text");

            migrationBuilder.AddPrimaryKey(
                name: "Reporting-Emvarias_07-Microrutas_key",
                table: "Reporting-Emvarias_07-Microrutas",
                columns: new[] { "REPEM07_Numero_de_Microruta", "REPEM07_NUAP" });

            migrationBuilder.AddPrimaryKey(
                name: "Reporting-Emvarias_05-Recoleccion_Vehicular_key",
                table: "Reporting-Emvarias_05-Recoleccion_Vehicular",
                columns: new[] { "REPEM05_NUAP", "REPEM05_Año", "REPEM05_Mes" });

            migrationBuilder.CreateTable(
                name: "Reporting-Emvarias_09-Rechazos",
                columns: table => new
                {
                    REPEM09_Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    REPEM09_Placa = table.Column<string>(type: "character varying(6)", maxLength: 6, nullable: false),
                    REPEM09_Fecha_Corta = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    REPEM09_Ruta_Larga = table.Column<string>(type: "char(7)", nullable: false),
                    REPEM09_ECA = table.Column<long>(type: "bigint", nullable: false),
                    REPEM09_Toneladas = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Reporting-Emvarias_09-Rechazos", x => x.REPEM09_Id);
                });

            migrationBuilder.CreateTable(
                name: "Reporting-Emvarias_10-Tickets_de_Servicio_Generados",
                columns: table => new
                {
                    REPEM10_Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    REPEM10_Valor = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("Reporting-Emvarias_10-Tickets_de_Servicio_Generados_key", x => x.REPEM10_Id);
                });

            migrationBuilder.CreateTable(
                name: "Reporting-Emvarias_11-Reporte_F14_Adiciones_CLU",
                columns: table => new
                {
                    C1_NUAP = table.Column<long>(type: "bigint", nullable: false),
                    C2_TIPO_SITIO = table.Column<int>(type: "integer", nullable: false),
                    C3_NUSD = table.Column<string>(type: "text", nullable: false),
                    C4_PLACA = table.Column<string>(type: "text", nullable: false),
                    C5_FECHA = table.Column<DateOnly>(type: "date", nullable: false),
                    C6_HORA = table.Column<TimeOnly>(type: "time without time zone", nullable: false),
                    C7_NUMICRO = table.Column<long>(type: "bigint", nullable: false),
                    C8_TON_LIMP_URB = table.Column<double>(type: "double precision", nullable: false),
                    C9_TON_BARRIDO = table.Column<double>(type: "double precision", nullable: false),
                    C10_TONRESNA = table.Column<double>(type: "double precision", nullable: false),
                    C11_TONRECHAPR = table.Column<double>(type: "double precision", nullable: false),
                    C12_TONRESAPR = table.Column<double>(type: "double precision", nullable: false),
                    C13_SISTEMA_MEDICION = table.Column<int>(type: "integer", nullable: false),
                    C14_VLRPEAJ = table.Column<double>(type: "double precision", nullable: false),
                    CE_ID_TICKET = table.Column<long>(type: "bigint", nullable: false),
                    CE_NOMBRE_AREA = table.Column<string>(type: "text", nullable: false),
                    CE_RUTA_LARGA = table.Column<string>(type: "text", nullable: false),
                    CE_TON_TOTAL = table.Column<double>(type: "double precision", nullable: false),
                    CE_REL_COMPENSACION = table.Column<bool>(type: "boolean", nullable: false),
                    CE_REL_COMPENSACION_ID_TICKET = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "Reporting-Emvarias_12-Reporte_F14_Adiciones_Aprovechamiento",
                columns: table => new
                {
                    C1_NUAP = table.Column<long>(type: "bigint", nullable: false),
                    C2_TIPO_SITIO = table.Column<int>(type: "integer", nullable: false),
                    C3_NUSD = table.Column<string>(type: "text", nullable: false),
                    C4_PLACA = table.Column<string>(type: "text", nullable: false),
                    C5_FECHA = table.Column<DateOnly>(type: "date", nullable: false),
                    C6_HORA = table.Column<TimeOnly>(type: "time without time zone", nullable: false),
                    C7_NUMICRO = table.Column<long>(type: "bigint", nullable: false),
                    C8_TON_LIMP_URB = table.Column<double>(type: "double precision", nullable: false),
                    C9_TON_BARRIDO = table.Column<double>(type: "double precision", nullable: false),
                    C10_TONRESNA = table.Column<double>(type: "double precision", nullable: false),
                    C11_TONRECHAPR = table.Column<double>(type: "double precision", nullable: false),
                    C12_TONRESAPR = table.Column<double>(type: "double precision", nullable: false),
                    C13_SISTEMA_MEDICION = table.Column<int>(type: "integer", nullable: false),
                    C14_VLRPEAJ = table.Column<double>(type: "double precision", nullable: false),
                    CE_ID_TICKET = table.Column<long>(type: "bigint", nullable: false),
                    CE_NOMBRE_AREA = table.Column<string>(type: "text", nullable: false),
                    CE_RUTA_LARGA = table.Column<string>(type: "text", nullable: false),
                    CE_TON_TOTAL = table.Column<double>(type: "double precision", nullable: false),
                    CE_REL_COMPENSACION = table.Column<bool>(type: "boolean", nullable: false),
                    CE_REL_COMPENSACION_ID_TICKET = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Reporting-Emvarias_09-Rechazos");

            migrationBuilder.DropTable(
                name: "Reporting-Emvarias_10-Tickets_de_Servicio_Generados");

            migrationBuilder.DropTable(
                name: "Reporting-Emvarias_11-Reporte_F14_Adiciones_CLU");

            migrationBuilder.DropTable(
                name: "Reporting-Emvarias_12-Reporte_F14_Adiciones_Aprovechamiento");

            migrationBuilder.DropPrimaryKey(
                name: "Reporting-Emvarias_07-Microrutas_key",
                table: "Reporting-Emvarias_07-Microrutas");

            migrationBuilder.DropPrimaryKey(
                name: "Reporting-Emvarias_05-Recoleccion_Vehicular_key",
                table: "Reporting-Emvarias_05-Recoleccion_Vehicular");

            migrationBuilder.DropColumn(
                name: "REPEM07_Numero_Ruta_Intendencia",
                table: "Reporting-Emvarias_07-Microrutas");

            migrationBuilder.DropColumn(
                name: "REPEM07_Porcentaje_Barrido",
                table: "Reporting-Emvarias_07-Microrutas");

            migrationBuilder.DropColumn(
                name: "REPEM07_Porcentaje_Limpieza_Urbana",
                table: "Reporting-Emvarias_07-Microrutas");

            migrationBuilder.DropColumn(
                name: "REPEM07_Porcentaje_No_Aforado",
                table: "Reporting-Emvarias_07-Microrutas");

            migrationBuilder.DropColumn(
                name: "REPEM07_Porcentaje_Residuos_Aprovechables",
                table: "Reporting-Emvarias_07-Microrutas");

            migrationBuilder.DropColumn(
                name: "REPEM07_Ruta_Larga",
                table: "Reporting-Emvarias_07-Microrutas");

            migrationBuilder.DropColumn(
                name: "REPEM06_Fecha_Validez",
                table: "Reporting-Emvarias_06-Peajes");

            migrationBuilder.DropColumn(
                name: "REPEM06_Placa",
                table: "Reporting-Emvarias_06-Peajes");

            migrationBuilder.DropColumn(
                name: "REPEM05_Año",
                table: "Reporting-Emvarias_05-Recoleccion_Vehicular");

            migrationBuilder.DropColumn(
                name: "REPEM05_Mes",
                table: "Reporting-Emvarias_05-Recoleccion_Vehicular");

            migrationBuilder.AddColumn<string>(
                name: "REPEM07_Ruta_de_Higiene",
                table: "Reporting-Emvarias_07-Microrutas",
                type: "character varying(20)",
                maxLength: 20,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AlterColumn<string>(
                name: "REPEM06_Codigo_Tipo_Vehiculo",
                table: "Reporting-Emvarias_06-Peajes",
                type: "character varying(10)",
                maxLength: 10,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(50)",
                oldMaxLength: 50);

            migrationBuilder.AddColumn<string>(
                name: "REPEM05_Id",
                table: "Reporting-Emvarias_05-Recoleccion_Vehicular",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "REPEM05_Codigo_DANE",
                table: "Reporting-Emvarias_05-Recoleccion_Vehicular",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "REPEM05_Empresa",
                table: "Reporting-Emvarias_05-Recoleccion_Vehicular",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<DateTime>(
                name: "REPEM05_FechaHora_Recoleccion",
                table: "Reporting-Emvarias_05-Recoleccion_Vehicular",
                type: "timestamp without time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<long>(
                name: "REPEM05_NIT",
                table: "Reporting-Emvarias_05-Recoleccion_Vehicular",
                type: "bigint",
                nullable: false,
                defaultValue: 0L);

            migrationBuilder.AddColumn<string>(
                name: "REPEM05_Patente",
                table: "Reporting-Emvarias_05-Recoleccion_Vehicular",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AlterColumn<string>(
                name: "REPEM03_Tipo_de_carga",
                table: "Reporting-Emvarias_03-Pesaje_de_Balanza",
                type: "text",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "char(1)");

            migrationBuilder.AlterColumn<string>(
                name: "REPEM03_Patente",
                table: "Reporting-Emvarias_03-Pesaje_de_Balanza",
                type: "text",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(6)",
                oldMaxLength: 6);

            migrationBuilder.AlterColumn<string>(
                name: "REPEM03_Municipio",
                table: "Reporting-Emvarias_03-Pesaje_de_Balanza",
                type: "text",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(5)",
                oldMaxLength: 5);

            migrationBuilder.AlterColumn<string>(
                name: "REPEM02_Nombre",
                table: "Reporting-Emvarias_02-Areas_de_Aprovechamiento",
                type: "text",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(20)",
                oldMaxLength: 20);

            migrationBuilder.AlterColumn<string>(
                name: "REPEM01_Provincia",
                table: "Reporting-Emvarias_01-Municipios",
                type: "text",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(50)",
                oldMaxLength: 50);

            migrationBuilder.AlterColumn<string>(
                name: "REPEM01_Nombre",
                table: "Reporting-Emvarias_01-Municipios",
                type: "text",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(50)",
                oldMaxLength: 50);

            migrationBuilder.AlterColumn<string>(
                name: "REPEM01_Departamento",
                table: "Reporting-Emvarias_01-Municipios",
                type: "text",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(50)",
                oldMaxLength: 50);

            migrationBuilder.AlterColumn<string>(
                name: "REPEM01_Codigo",
                table: "Reporting-Emvarias_01-Municipios",
                type: "text",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(5)",
                oldMaxLength: 5);

            migrationBuilder.AddPrimaryKey(
                name: "Reporting-Emvarias_07-Microrutas_key",
                table: "Reporting-Emvarias_07-Microrutas",
                columns: new[] { "REPEM07_Numero_de_Microruta", "REPEM07_Ruta_de_Higiene" });

            migrationBuilder.AddPrimaryKey(
                name: "Reporting-Emvarias_05-Recoleccion_Vehicular_key",
                table: "Reporting-Emvarias_05-Recoleccion_Vehicular",
                column: "REPEM05_Id");

            migrationBuilder.CreateTable(
                name: "Reporting-Emvarias_04-Recoleccion_por_Microruta",
                columns: table => new
                {
                    REPEM04_Id = table.Column<string>(type: "text", nullable: false),
                    REPEM04_FechaHora_Recoleccion = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    REPEM04_Patente = table.Column<string>(type: "text", nullable: false),
                    REPEM04_Microruta = table.Column<string>(type: "text", nullable: false),
                    REPEM04_NUAP = table.Column<long>(type: "bigint", nullable: false),
                    REPEM04_TonResAprob = table.Column<double>(type: "double precision", precision: 18, scale: 2, nullable: false),
                    REPEM04_TonRechazo = table.Column<double>(type: "double precision", precision: 18, scale: 2, nullable: false),
                    REPEM04_TonBarrido = table.Column<double>(type: "double precision", precision: 18, scale: 2, nullable: false),
                    REPEM04_Valor_peaje = table.Column<double>(type: "double precision", precision: 18, scale: 2, nullable: false),
                    REPEM04_TonLimUrb = table.Column<double>(type: "double precision", precision: 18, scale: 2, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("Reporting-Emvarias_04-Recoleccion_por_Microruta_key", x => x.REPEM04_Id);
                });
        }
    }
}
