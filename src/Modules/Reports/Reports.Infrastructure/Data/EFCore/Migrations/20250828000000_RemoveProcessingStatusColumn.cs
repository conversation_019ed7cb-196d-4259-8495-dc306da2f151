using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Reports.Infrastructure.Data.EFCore.Migrations
{
    public partial class RemoveProcessingStatusColumn : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_UrbetrackSync_ProcessingStatus_Status",
                table: "Reporting-Emvarias_14-Sincronizacion_Urbetrack");

            migrationBuilder.DropCheckConstraint(
                name: "CK_ProcessingStatus_ValidValues",
                table: "Reporting-Emvarias_14-Sincronizacion_Urbetrack");

            migrationBuilder.DropColumn(
                name: "REPEM14_Estado_Procesamiento",
                table: "Reporting-Emvarias_14-Sincronizacion_Urbetrack");

            migrationBuilder.CreateIndex(
                name: "IX_UrbetrackSync_Status",
                table: "Reporting-Emvarias_14-Sincronizacion_Urbetrack",
                column: "REPEM14_Estado");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_UrbetrackSync_Status",
                table: "Reporting-Emvarias_14-Sincronizacion_Urbetrack");

            migrationBuilder.AddColumn<string>(
                name: "REPEM14_Estado_Procesamiento",
                table: "Reporting-Emvarias_14-Sincronizacion_Urbetrack",
                type: "text",
                nullable: false,
                defaultValue: "Available");

            migrationBuilder.AddCheckConstraint(
                name: "CK_ProcessingStatus_ValidValues",
                table: "Reporting-Emvarias_14-Sincronizacion_Urbetrack",
                sql: "\"REPEM14_Estado_Procesamiento\" IN ('Available', 'Locked', 'Processing')");

            migrationBuilder.CreateIndex(
                name: "IX_UrbetrackSync_ProcessingStatus_Status",
                table: "Reporting-Emvarias_14-Sincronizacion_Urbetrack",
                columns: new[] { "REPEM14_Estado_Procesamiento", "REPEM14_Estado" });
        }
    }
}
