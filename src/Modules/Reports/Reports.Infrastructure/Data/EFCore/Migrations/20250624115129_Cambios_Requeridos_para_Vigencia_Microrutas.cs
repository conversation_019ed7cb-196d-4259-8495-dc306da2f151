using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Reports.Infrastructure.Data.EFCore.Migrations
{
    public partial class Cambios_Requeridos_para_Vigencia_Microrutas : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "Reporting-Emvarias_07-Microrutas_key",
                table: "Reporting-Emvarias_07-Microrutas");

            migrationBuilder.AddColumn<DateTime>(
                name: "REPEM07_Fecha_Inicio_Vigencia",
                table: "Reporting-Emvarias_07-Microrutas",
                type: "timestamp without time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<DateTime>(
                name: "REPEM07_Fecha_Fin_Vigencia",
                table: "Reporting-Emvarias_07-Microrutas",
                type: "timestamp without time zone",
                nullable: true);

            migrationBuilder.AddPrimaryKey(
                name: "Reporting-Emvarias_07-Microrutas_key",
                table: "Reporting-Emvarias_07-Microrutas",
                columns: new[] { "REPEM07_Numero_de_Microruta", "REPEM07_NUAP", "REPEM07_Fecha_Inicio_Vigencia" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "Reporting-Emvarias_07-Microrutas_key",
                table: "Reporting-Emvarias_07-Microrutas");

            migrationBuilder.DropColumn(
                name: "REPEM07_Fecha_Inicio_Vigencia",
                table: "Reporting-Emvarias_07-Microrutas");

            migrationBuilder.DropColumn(
                name: "REPEM07_Fecha_Fin_Vigencia",
                table: "Reporting-Emvarias_07-Microrutas");

            migrationBuilder.AddPrimaryKey(
                name: "Reporting-Emvarias_07-Microrutas_key",
                table: "Reporting-Emvarias_07-Microrutas",
                columns: new[] { "REPEM07_Numero_de_Microruta", "REPEM07_NUAP" });
        }
    }
}
