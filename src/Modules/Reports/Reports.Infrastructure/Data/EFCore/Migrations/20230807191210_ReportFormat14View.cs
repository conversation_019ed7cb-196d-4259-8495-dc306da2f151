using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Reports.Infrastructure.Data.EFCore.Migrations
{
    public partial class ReportFormat14View : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"CREATE OR REPLACE VIEW public.""View_Reporte_Formato_14""
                AS
                SELECT
                    pb.""REPEM03_Nro_Unico_Area_Prestacion"" as ""NUAP"",
                    pb.""REPEM03_Tipo_de_origen"" as ""OriginType"",
                    720105237 as ""DepositPlace"",
                    pb.""REPEM03_Patente"" as ""LicensePlate"",
                    pb.""REPEM03_Fecha_de_entrada""::DATE as ""EntryDate"",
                    pb.""REPEM03_Fecha_de_entrada""::TIME as ""EntryTime"",
                    mr.""REPEM04_Microruta"" as ""Microroute"",
                    mr.""REPEM04_TonLimUrb"" as ""UrbanCleaningTons"",
                    mr.""REPEM04_TonBarrido"" as ""SweepingTons"",
                    (mr.""REPEM04_TonLimUrb"" + mr.""REPEM04_TonBarrido"") as ""NonRecyclableTons"",
                    mr.""REPEM04_TonRechazo"" as ""RejectedTons"",
                    mr.""REPEM04_TonResAprob"" as ""RecyclableTons"",
                    1 as ""MeasuringUnit"",
                    mr.""REPEM04_Valor_peaje"" as ""Toll""
                FROM public.""Reporting-Emvarias_03-Pesaje_de_Balanza"" pb
                INNER JOIN public.""Reporting-Emvarias_04-Recoleccion_por_Microruta"" mr
                    ON mr.""REPEM04_NUAP"" = pb.""REPEM03_Nro_Unico_Area_Prestacion""
                        AND mr.""REPEM04_Patente"" = pb.""REPEM03_Patente""
                        AND mr.""REPEM04_FechaHora_Recoleccion""
                            BETWEEN pb.""REPEM03_Fecha_de_entrada""
                                AND pb.""REPEM03_Fecha_de_egreso""   
            ");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"DROP VIEW public.""Reporte_Formato_14""");
        }
    }
}
