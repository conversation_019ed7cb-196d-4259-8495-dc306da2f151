using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Reports.Infrastructure.Data.EFCore.Migrations
{
    public partial class RSUI_Fix_Performance_F34 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"
                DROP VIEW IF EXISTS ""REPEM_Reporte_SUI_Recolecciones_F34"";
            ");
            
            migrationBuilder.Sql(@"
                CREATE OR REPLACE VIEW ""REPEM_Reporte_SUI_Recolecciones_F34""
                AS
                WITH f14dep AS (SELECT 720105237                                                               AS ""C1_NUSD"",
                       CASE
                           WHEN pb.""REPEM03_Tipo_de_origen"" = 'Others'::text THEN '4'::bigint
                           WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUAP'::text THEN '1'::bigint
                           WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUET'::text THEN '2'::bigint
                           WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUECA'::text THEN '3'::bigint
                           ELSE NULL::bigint
                           END                                                                 AS ""C2_TIPO_ORIGEN"",
                       CASE
                           WHEN pb.""REPEM03_Tipo_de_origen"" = 'Others'::text THEN NULL::bigint
                           ELSE r14.""C1_NUAP""
                           END                                                                 AS ""C3_NUSITIO_ORI"",
                       CASE
                           WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                           ELSE cli.""REPEM08_Nombre_Completo""
                           END                                                                 AS ""C4_NOMBRE_EMPRESA"",
                       CASE
                           WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::bigint
                           ELSE pb.""REPEM03_Nro_Identificacion_Tributaria""
                           END                                                                 AS ""C5_NIT_EMPRESA"",
                       CASE
                           WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                           ELSE mun.""REPEM01_Codigo""
                           END                                                                 AS ""C6_COD_DANE_ORI"",
                       pb.""REPEM03_Patente""                                                    AS ""C7_PLACA"",
                       pb.""REPEM03_Fecha_de_entrada""::date                                     AS ""C8_FECHA_INGRESO"",
                       pb.""REPEM03_Fecha_de_egreso""::date                                      AS ""C9_FECHA_SALIDA"",
                       pb.""REPEM03_Fecha_de_entrada""::time without time zone                   AS ""C10_HORA_INGRESO"",
                       pb.""REPEM03_Fecha_de_egreso""::time without time zone                    AS ""C11_HORA_SALIDA"",
                       (r14.""C9_TON_BARRIDO""::double precision + r14.""C10_TONRESNA"" -
                        COALESCE(r14.""C11_TONRECHAPR"", 0::numeric)::double precision)::numeric AS ""C12_TONELADAS"",
                       pb.""REPEM03_Id""                                                         AS ""CE_ID_TICKET"",
                       COALESCE(tct.""REPEM10_Valor"", 0::numeric)                               AS ""CE_VALOR_TICKET_LEGACY"",
                       COALESCE(tct.""REPEM10_Valor""::numeric / 1000::numeric, 0::numeric)      AS ""CE_VALOR_TON_TICKET_LEGACY"",
                       COALESCE(r14.""C11_TONRECHAPR"", 0::numeric)                              AS ""CE_TONELADAS_RECHAZADAS"",
                       true                                                                    AS ""CE_EXISTE_EN_F14"",
                       pb.""REPEM03_Nro_Identificacion_Tributaria""                              AS ""CE_NIT_EMPRESA"",
                       r14.""C1_NUAP""                                                           AS ""CE_NUAP""
                FROM ""REPEM_Reporte_SUI_Recolecciones_F14"" r14
                         JOIN ""Reporting-Emvarias_03-Pesaje_de_Balanza"" pb
                              ON pb.""REPEM03_Id"" = r14.""CE_ID_TICKET"" AND pb.""REPEM03_Fecha_de_cancelacion"" IS NULL
                         LEFT JOIN ""Reporting-Emvarias_08-Clientes"" cli
                                   ON pb.""REPEM03_Nro_Identificacion_Tributaria"" = cli.""REPEM08_NIT""
                         LEFT JOIN ""Reporting-Emvarias_01-Municipios"" mun
                                   ON pb.""REPEM03_Municipio""::text = mun.""REPEM01_Codigo""::text
                         LEFT JOIN ""Reporting-Emvarias_10-Tickets_de_Servicio_Generados"" tct
                                   ON pb.""REPEM03_Id"" = tct.""REPEM10_Id"" AND pb.""REPEM03_Fecha_de_cancelacion"" IS NULL),
                     base AS (SELECT f14dep.""C1_NUSD"",
                                     f14dep.""C2_TIPO_ORIGEN"",
                                     f14dep.""C3_NUSITIO_ORI"",
                                     f14dep.""C4_NOMBRE_EMPRESA"",
                                     f14dep.""C5_NIT_EMPRESA"",
                                     f14dep.""C6_COD_DANE_ORI"",
                                     f14dep.""C7_PLACA"",
                                     f14dep.""C8_FECHA_INGRESO"",
                                     f14dep.""C9_FECHA_SALIDA"",
                                     f14dep.""C10_HORA_INGRESO"",
                                     f14dep.""C11_HORA_SALIDA"",
                                     f14dep.""C12_TONELADAS"",
                                     f14dep.""CE_ID_TICKET"",
                                     f14dep.""CE_VALOR_TICKET_LEGACY"",
                                     f14dep.""CE_VALOR_TON_TICKET_LEGACY"",
                                     f14dep.""CE_TONELADAS_RECHAZADAS"",
                                     f14dep.""CE_EXISTE_EN_F14"",
                                     f14dep.""CE_NIT_EMPRESA"",
                                     f14dep.""CE_NUAP""
                              FROM f14dep
                              UNION ALL
                              SELECT 720105237                                                                                       AS ""C1_NUSD"",
                                     CASE
                                         WHEN pb.""REPEM03_Tipo_de_origen"" = 'Others'::text THEN '4'::bigint
                                         WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUAP'::text THEN '1'::bigint
                                         WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUET'::text THEN '2'::bigint
                                         WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUECA'::text THEN '3'::bigint
                                         ELSE NULL::bigint
                                         END                                                                                         AS ""C2_TIPO_ORIGEN"",
                                     CASE
                                         WHEN pb.""REPEM03_Tipo_de_origen"" = 'Others'::text THEN NULL::bigint
                                         ELSE pb.""REPEM03_Nro_Unico_Area_Prestacion""
                                         END                                                                                         AS ""C3_NUSITIO_ORI"",
                                     CASE
                                         WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                                         ELSE cli.""REPEM08_Nombre_Completo""
                                         END                                                                                         AS ""C4_NOMBRE_EMPRESA"",
                                     CASE
                                         WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::bigint
                                         ELSE pb.""REPEM03_Nro_Identificacion_Tributaria""
                                         END                                                                                         AS ""C5_NIT_EMPRESA"",
                                     CASE
                                         WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                                         ELSE mun.""REPEM01_Codigo""
                                         END                                                                                         AS ""C6_COD_DANE_ORI"",
                                     pb.""REPEM03_Patente""                                                                            AS ""C7_PLACA"",
                                     pb.""REPEM03_Fecha_de_entrada""::date                                                             AS ""C8_FECHA_INGRESO"",
                                     pb.""REPEM03_Fecha_de_egreso""::date                                                              AS ""C9_FECHA_SALIDA"",
                                     pb.""REPEM03_Fecha_de_entrada""::time without time zone                                           AS ""C10_HORA_INGRESO"",
                                     pb.""REPEM03_Fecha_de_egreso""::time without time zone                                            AS ""C11_HORA_SALIDA"",
                                     tct.""REPEM10_Valor""::numeric / 1000::numeric -
                                     COALESCE(tre.""Toneladas_Rechazadas"", 0::numeric)                                                AS ""C12_TONELADAS"",
                                     pb.""REPEM03_Id""                                                                                 AS ""CE_ID_TICKET"",
                                     COALESCE(tct.""REPEM10_Valor"", 0::numeric)                                                       AS ""CE_VALOR_TICKET_LEGACY"",
                                     COALESCE(tct.""REPEM10_Valor""::numeric / 1000::numeric, 0::numeric)                              AS ""CE_VALOR_TON_TICKET_LEGACY"",
                                     COALESCE(tre.""Toneladas_Rechazadas"", 0::numeric)                                                AS ""CE_TONELADAS_RECHAZADAS"",
                                     false                                                                                           AS ""CE_EXISTE_EN_F14"",
                                     pb.""REPEM03_Nro_Identificacion_Tributaria""                                                      AS ""CE_NIT_EMPRESA"",
                                     ap.""REPEM02_Codigo""                                                                             AS ""CE_NUAP""
                              FROM ""Reporting-Emvarias_10-Tickets_de_Servicio_Generados"" tct
                                       JOIN ""Reporting-Emvarias_03-Pesaje_de_Balanza"" pb
                                            ON pb.""REPEM03_Id"" = tct.""REPEM10_Id"" AND pb.""REPEM03_Fecha_de_cancelacion"" IS NULL
                                       LEFT JOIN ""REPEM_Toneladas_Rechazos_Agrupadas_Por_Ticket"" tre
                                                 ON (pb.""REPEM03_Id"" = tre.""Ticket_Asignable"" OR
                                                     tre.""Ticket_Asignable"" = tct.""REPEM10_Id"") AND
                                                    pb.""REPEM03_Nro_Unico_Area_Prestacion"" = 440405001
                                       LEFT JOIN ""Reporting-Emvarias_08-Clientes"" cli
                                                 ON pb.""REPEM03_Nro_Identificacion_Tributaria"" = cli.""REPEM08_NIT""
                                       LEFT JOIN ""Reporting-Emvarias_01-Municipios"" mun
                                                 ON pb.""REPEM03_Municipio""::text = mun.""REPEM01_Codigo""::text
                                       LEFT JOIN ""Reporting-Emvarias_02-Areas_de_Aprovechamiento"" ap
                                                 ON ap.""REPEM02_Nombre""::text = mun.""REPEM01_Nombre""::text
                              WHERE tct.""REPEM10_Id"" NOT IN (SELECT ""CE_ID_TICKET"" FROM f14dep)),
                     rch AS (SELECT bs.""C1_NUSD"",
                                    3                            AS ""C2_TIPO_ORIGEN"",
                                    trept.""Num_ECA""              AS ""C3_NUSITIO_ORI"",
                                    bs.""C4_NOMBRE_EMPRESA"",
                                    bs.""C5_NIT_EMPRESA"",
                                    bs.""C6_COD_DANE_ORI"",
                                    bs.""C7_PLACA"",
                                    bs.""C8_FECHA_INGRESO"",
                                    bs.""C9_FECHA_SALIDA"",
                                    bs.""C10_HORA_INGRESO"",
                                    bs.""C11_HORA_SALIDA"",
                                    trept.""Toneladas_Rechazadas"" AS ""C12_TONELADAS"",
                                    bs.""CE_ID_TICKET"",
                                    bs.""CE_VALOR_TICKET_LEGACY"",
                                    bs.""CE_VALOR_TON_TICKET_LEGACY"",
                                    0                            AS ""CE_TONELADAS_RECHAZADAS"",
                                    bs.""CE_EXISTE_EN_F14"",
                                    bs.""CE_NIT_EMPRESA"",
                                    trept.""NUAP""                 AS ""CE_NUAP""
                             FROM base bs
                                      JOIN ""REPEM_Toneladas_Rechazos_Por_Ticket"" trept
                                           ON bs.""CE_ID_TICKET"" = trept.""Id_Ticket"" AND trept.""NUAP"" = bs.""CE_NUAP""
                                      LEFT JOIN ""Reporting-Emvarias_08-Clientes"" cli ON bs.""CE_NIT_EMPRESA"" = cli.""REPEM08_NIT"")
                SELECT base.""C1_NUSD"",
                       base.""C2_TIPO_ORIGEN"",
                       base.""C3_NUSITIO_ORI"",
                       base.""C4_NOMBRE_EMPRESA"",
                       base.""C5_NIT_EMPRESA"",
                       base.""C6_COD_DANE_ORI"",
                       base.""C7_PLACA"",
                       base.""C8_FECHA_INGRESO"",
                       base.""C9_FECHA_SALIDA"",
                       base.""C10_HORA_INGRESO"",
                       base.""C11_HORA_SALIDA"",
                       base.""C12_TONELADAS"",
                       base.""CE_ID_TICKET"",
                       base.""CE_VALOR_TICKET_LEGACY"",
                       base.""CE_VALOR_TON_TICKET_LEGACY"",
                       base.""CE_TONELADAS_RECHAZADAS"",
                       base.""CE_EXISTE_EN_F14"",
                       base.""CE_NIT_EMPRESA"",
                       base.""CE_NUAP""
                FROM base
                UNION ALL
                SELECT rch.""C1_NUSD"",
                       rch.""C2_TIPO_ORIGEN"",
                       rch.""C3_NUSITIO_ORI"",
                       rch.""C4_NOMBRE_EMPRESA"",
                       rch.""C5_NIT_EMPRESA"",
                       rch.""C6_COD_DANE_ORI"",
                       rch.""C7_PLACA"",
                       rch.""C8_FECHA_INGRESO"",
                       rch.""C9_FECHA_SALIDA"",
                       rch.""C10_HORA_INGRESO"",
                       rch.""C11_HORA_SALIDA"",
                       rch.""C12_TONELADAS"",
                       rch.""CE_ID_TICKET"",
                       rch.""CE_VALOR_TICKET_LEGACY"",
                       rch.""CE_VALOR_TON_TICKET_LEGACY"",
                       rch.""CE_TONELADAS_RECHAZADAS"",
                       rch.""CE_EXISTE_EN_F14"",
                       rch.""CE_NIT_EMPRESA"",
                       440405001 AS ""CE_NUAP""
                FROM rch;
            ");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"
                DROP VIEW IF EXISTS ""REPEM_Reporte_SUI_Recolecciones_F34"";
            ");
            
            migrationBuilder.Sql(@"
                CREATE OR REPLACE VIEW ""REPEM_Reporte_SUI_Recolecciones_F34""
                AS WITH f14dep AS (
                         SELECT 720105237 AS ""C1_NUSD"",
                                CASE
                                    WHEN pb.""REPEM03_Tipo_de_origen"" = 'Others'::text THEN '4'::bigint
                                    WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUAP'::text THEN '1'::bigint
                                    WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUET'::text THEN '2'::bigint
                                    WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUECA'::text THEN '3'::bigint
                                    ELSE NULL::bigint
                                END AS ""C2_TIPO_ORIGEN"",
                                CASE
                                    WHEN pb.""REPEM03_Tipo_de_origen"" = 'Others'::text THEN NULL::bigint
                                    ELSE r14.""C1_NUAP""
                                END AS ""C3_NUSITIO_ORI"",
                                CASE
                                    WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                                    ELSE cli.""REPEM08_Nombre_Completo""
                                END AS ""C4_NOMBRE_EMPRESA"",
                                CASE
                                    WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::bigint
                                    ELSE pb.""REPEM03_Nro_Identificacion_Tributaria""
                                END AS ""C5_NIT_EMPRESA"",
                                CASE
                                    WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                                    ELSE mun.""REPEM01_Codigo""
                                END AS ""C6_COD_DANE_ORI"",
                            pb.""REPEM03_Patente"" AS ""C7_PLACA"",
                            pb.""REPEM03_Fecha_de_entrada""::date AS ""C8_FECHA_INGRESO"",
                            pb.""REPEM03_Fecha_de_egreso""::date AS ""C9_FECHA_SALIDA"",
                            pb.""REPEM03_Fecha_de_entrada""::time without time zone AS ""C10_HORA_INGRESO"",
                            pb.""REPEM03_Fecha_de_egreso""::time without time zone AS ""C11_HORA_SALIDA"",
                            (
                                r14.""C9_TON_BARRIDO""
                                + r14.""C10_TONRESNA""
                                - COALESCE(r14.""C11_TONRECHAPR"", 0::numeric)::double precision)::numeric AS ""C12_TONELADAS"",
                            pb.""REPEM03_Id"" AS ""CE_ID_TICKET"",
                            COALESCE(tct.""REPEM10_Valor"", 0::numeric) AS ""CE_VALOR_TICKET_LEGACY"",
                            COALESCE(tct.""REPEM10_Valor""::numeric / 1000::numeric, 0::numeric) AS ""CE_VALOR_TON_TICKET_LEGACY"",
                            COALESCE(r14.""C11_TONRECHAPR"", 0::numeric) AS ""CE_TONELADAS_RECHAZADAS"",
                            true AS ""CE_EXISTE_EN_F14"",
                            pb.""REPEM03_Nro_Identificacion_Tributaria"" AS ""CE_NIT_EMPRESA"",
                            r14.""C1_NUAP"" AS ""CE_NUAP""
                           FROM ""REPEM_Reporte_SUI_Recolecciones_F14"" r14
                             JOIN ""Reporting-Emvarias_03-Pesaje_de_Balanza"" pb ON pb.""REPEM03_Id"" = r14.""CE_ID_TICKET"" AND pb.""REPEM03_Fecha_de_cancelacion"" IS NULL
                             LEFT JOIN ""Reporting-Emvarias_08-Clientes"" cli ON pb.""REPEM03_Nro_Identificacion_Tributaria"" = cli.""REPEM08_NIT""
                             LEFT JOIN ""Reporting-Emvarias_01-Municipios"" mun ON pb.""REPEM03_Municipio""::text = mun.""REPEM01_Codigo""::text
                             LEFT JOIN ""Reporting-Emvarias_10-Tickets_de_Servicio_Generados"" tct ON pb.""REPEM03_Id"" = tct.""REPEM10_Id"" AND pb.""REPEM03_Fecha_de_cancelacion"" IS NULL
                        ), base AS (
                         SELECT f14dep.""C1_NUSD"",
                            f14dep.""C2_TIPO_ORIGEN"",
                            f14dep.""C3_NUSITIO_ORI"",
                            f14dep.""C4_NOMBRE_EMPRESA"",
                            f14dep.""C5_NIT_EMPRESA"",
                            f14dep.""C6_COD_DANE_ORI"",
                            f14dep.""C7_PLACA"",
                            f14dep.""C8_FECHA_INGRESO"",
                            f14dep.""C9_FECHA_SALIDA"",
                            f14dep.""C10_HORA_INGRESO"",
                            f14dep.""C11_HORA_SALIDA"",
                            f14dep.""C12_TONELADAS"",
                            f14dep.""CE_ID_TICKET"",
                            f14dep.""CE_VALOR_TICKET_LEGACY"",
                            f14dep.""CE_VALOR_TON_TICKET_LEGACY"",
                            f14dep.""CE_TONELADAS_RECHAZADAS"",
                            f14dep.""CE_EXISTE_EN_F14"",
                            f14dep.""CE_NIT_EMPRESA"",
                            f14dep.""CE_NUAP""
                           FROM f14dep
                        UNION ALL
                         SELECT 720105237 AS ""C1_NUSD"",
                                CASE
                                    WHEN pb.""REPEM03_Tipo_de_origen"" = 'Others'::text THEN '4'::bigint
                                    WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUAP'::text THEN '1'::bigint
                                    WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUET'::text THEN '2'::bigint
                                    WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUECA'::text THEN '3'::bigint
                                    ELSE NULL::bigint
                                END AS ""C2_TIPO_ORIGEN"",
                                CASE
                                    WHEN pb.""REPEM03_Tipo_de_origen"" = 'Others'::text THEN NULL::bigint
                                    ELSE pb.""REPEM03_Nro_Unico_Area_Prestacion""
                                END AS ""C3_NUSITIO_ORI"",
                                CASE
                                    WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                                    ELSE cli.""REPEM08_Nombre_Completo""
                                END AS ""C4_NOMBRE_EMPRESA"",
                                CASE
                                    WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::bigint
                                    ELSE pb.""REPEM03_Nro_Identificacion_Tributaria""
                                END AS ""C5_NIT_EMPRESA"",
                                CASE
                                    WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                                    ELSE mun.""REPEM01_Codigo""
                                END AS ""C6_COD_DANE_ORI"",
                            pb.""REPEM03_Patente"" AS ""C7_PLACA"",
                            pb.""REPEM03_Fecha_de_entrada""::date AS ""C8_FECHA_INGRESO"",
                            pb.""REPEM03_Fecha_de_egreso""::date AS ""C9_FECHA_SALIDA"",
                            pb.""REPEM03_Fecha_de_entrada""::time without time zone AS ""C10_HORA_INGRESO"",
                            pb.""REPEM03_Fecha_de_egreso""::time without time zone AS ""C11_HORA_SALIDA"",
                            tct.""REPEM10_Valor""::numeric / 1000::numeric - COALESCE(tre.""Toneladas_Rechazadas"", 0::numeric) AS ""C12_TONELADAS"",
                            pb.""REPEM03_Id"" AS ""CE_ID_TICKET"",
                            COALESCE(tct.""REPEM10_Valor"", 0::numeric) AS ""CE_VALOR_TICKET_LEGACY"",
                            COALESCE(tct.""REPEM10_Valor""::numeric / 1000::numeric, 0::numeric) AS ""CE_VALOR_TON_TICKET_LEGACY"",
                            COALESCE(tre.""Toneladas_Rechazadas"", 0::numeric) AS ""CE_TONELADAS_RECHAZADAS"",
                            false AS ""CE_EXISTE_EN_F14"",
                            pb.""REPEM03_Nro_Identificacion_Tributaria"" AS ""CE_NIT_EMPRESA"",
                            ap.""REPEM02_Codigo"" AS ""CE_NUAP""
                           FROM ""Reporting-Emvarias_10-Tickets_de_Servicio_Generados"" tct
                             JOIN ""Reporting-Emvarias_03-Pesaje_de_Balanza"" pb ON pb.""REPEM03_Id"" = tct.""REPEM10_Id"" AND pb.""REPEM03_Fecha_de_cancelacion"" IS NULL
                             LEFT JOIN ""REPEM_Toneladas_Rechazos_Agrupadas_Por_Ticket"" tre ON (pb.""REPEM03_Id"" = tre.""Ticket_Asignable"" OR tre.""Ticket_Asignable"" = tct.""REPEM10_Id"") AND pb.""REPEM03_Nro_Unico_Area_Prestacion"" = 440405001
                             LEFT JOIN ""Reporting-Emvarias_08-Clientes"" cli ON pb.""REPEM03_Nro_Identificacion_Tributaria"" = cli.""REPEM08_NIT""
                             LEFT JOIN ""Reporting-Emvarias_01-Municipios"" mun ON pb.""REPEM03_Municipio""::text = mun.""REPEM01_Codigo""::text
                             LEFT JOIN ""Reporting-Emvarias_02-Areas_de_Aprovechamiento"" ap ON ap.""REPEM02_Nombre""::text = mun.""REPEM01_Nombre""::text
                             LEFT JOIN LATERAL ( SELECT r14_1.""CE_EXISTE_EN_F14""
                                   FROM f14dep r14_1
                                  WHERE pb.""REPEM03_Id"" = r14_1.""CE_ID_TICKET""
                                 LIMIT 1) r14 ON true
                          WHERE r14.* IS NULL
                        ), rch AS (
                         SELECT bs.""C1_NUSD"",
                            3 AS ""C2_TIPO_ORIGEN"",
                            trept.""Num_ECA"" AS ""C3_NUSITIO_ORI"",
                            bs.""C4_NOMBRE_EMPRESA"",
                            bs.""C5_NIT_EMPRESA"",
                            bs.""C6_COD_DANE_ORI"",
                            bs.""C7_PLACA"",
                            bs.""C8_FECHA_INGRESO"",
                            bs.""C9_FECHA_SALIDA"",
                            bs.""C10_HORA_INGRESO"",
                            bs.""C11_HORA_SALIDA"",
                            trept.""Toneladas_Rechazadas"" AS ""C12_TONELADAS"",
                            bs.""CE_ID_TICKET"",
                            bs.""CE_VALOR_TICKET_LEGACY"",
                            bs.""CE_VALOR_TON_TICKET_LEGACY"",
                            0 AS ""CE_TONELADAS_RECHAZADAS"",
                            bs.""CE_EXISTE_EN_F14"",
                            bs.""CE_NIT_EMPRESA"",
                            trept.""NUAP"" AS ""CE_NUAP""
                           FROM base bs
                             JOIN ""REPEM_Toneladas_Rechazos_Por_Ticket"" trept ON bs.""CE_ID_TICKET"" = trept.""Id_Ticket"" AND trept.""NUAP"" = bs.""CE_NUAP""
                             LEFT JOIN ""Reporting-Emvarias_08-Clientes"" cli ON bs.""CE_NIT_EMPRESA"" = cli.""REPEM08_NIT""
                        )
                 SELECT base.""C1_NUSD"",
                    base.""C2_TIPO_ORIGEN"",
                    base.""C3_NUSITIO_ORI"",
                    base.""C4_NOMBRE_EMPRESA"",
                    base.""C5_NIT_EMPRESA"",
                    base.""C6_COD_DANE_ORI"",
                    base.""C7_PLACA"",
                    base.""C8_FECHA_INGRESO"",
                    base.""C9_FECHA_SALIDA"",
                    base.""C10_HORA_INGRESO"",
                    base.""C11_HORA_SALIDA"",
                    base.""C12_TONELADAS"",
                    base.""CE_ID_TICKET"",
                    base.""CE_VALOR_TICKET_LEGACY"",
                    base.""CE_VALOR_TON_TICKET_LEGACY"",
                    base.""CE_TONELADAS_RECHAZADAS"",
                    base.""CE_EXISTE_EN_F14"",
                    base.""CE_NIT_EMPRESA"",
                    base.""CE_NUAP""
                   FROM base
                UNION ALL
                 SELECT rch.""C1_NUSD"",
                    rch.""C2_TIPO_ORIGEN"",
                    rch.""C3_NUSITIO_ORI"",
                    rch.""C4_NOMBRE_EMPRESA"",
                    rch.""C5_NIT_EMPRESA"",
                    rch.""C6_COD_DANE_ORI"",
                    rch.""C7_PLACA"",
                    rch.""C8_FECHA_INGRESO"",
                    rch.""C9_FECHA_SALIDA"",
                    rch.""C10_HORA_INGRESO"",
                    rch.""C11_HORA_SALIDA"",
                    rch.""C12_TONELADAS"",
                    rch.""CE_ID_TICKET"",
                    rch.""CE_VALOR_TICKET_LEGACY"",
                    rch.""CE_VALOR_TON_TICKET_LEGACY"",
                    rch.""CE_TONELADAS_RECHAZADAS"",
                    rch.""CE_EXISTE_EN_F14"",
                    rch.""CE_NIT_EMPRESA"",
                    440405001 AS ""CE_NUAP""
                   FROM rch;
            ");
        }
    }
}
