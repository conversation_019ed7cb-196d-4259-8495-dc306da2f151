using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Reports.Infrastructure.Data.EFCore.Migrations
{
    public partial class Initial : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Reporting-Emvarias_01-Municipios",
                columns: table => new
                {
                    REPEM01_Codigo = table.Column<string>(type: "text", nullable: false),
                    REPEM01_Nombre = table.Column<string>(type: "text", nullable: false),
                    REPEM01_Departamento = table.Column<string>(type: "text", nullable: false),
                    REPEM01_Provincia = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("Reporting-Emvarias_01-Municipios_key", x => x.REPEM01_Codigo);
                });

            migrationBuilder.CreateTable(
                name: "Reporting-Emvarias_02-Areas_de_Aprovechamiento",
                columns: table => new
                {
                    REPEM02_Codigo = table.Column<string>(type: "text", nullable: false),
                    REPEM02_Nombre = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("Reporting-Emvarias_02-Areas_de_Aprovechamiento_key", x => x.REPEM02_Codigo);
                });

            migrationBuilder.CreateTable(
                name: "Reporting-Emvarias_04-Recoleccion_por_Microruta",
                columns: table => new
                {
                    REPEM04_Id = table.Column<string>(type: "text", nullable: false),
                    REPEM04_NUAP = table.Column<long>(type: "bigint", nullable: false),
                    REPEM04_Patente = table.Column<string>(type: "text", nullable: false),
                    REPEM04_FechaHora_Recoleccion = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    REPEM04_Microruta = table.Column<string>(type: "text", nullable: false),
                    REPEM04_TonLimUrb = table.Column<double>(type: "double precision", precision: 18, scale: 2, nullable: false),
                    REPEM04_TonBarrido = table.Column<double>(type: "double precision", precision: 18, scale: 2, nullable: false),
                    REPEM04_TonRechazo = table.Column<double>(type: "double precision", precision: 18, scale: 2, nullable: false),
                    REPEM04_TonResAprob = table.Column<double>(type: "double precision", precision: 18, scale: 2, nullable: false),
                    REPEM04_Valor_peaje = table.Column<double>(type: "double precision", precision: 18, scale: 2, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("Reporting-Emvarias_04-Recoleccion_por_Microruta_key", x => x.REPEM04_Id);
                });

            migrationBuilder.CreateTable(
                name: "Reporting-Emvarias_05-Recoleccion_Vehicular",
                columns: table => new
                {
                    REPEM05_Id = table.Column<string>(type: "text", nullable: false),
                    REPEM05_NUAP = table.Column<long>(type: "bigint", nullable: false),
                    REPEM05_Patente = table.Column<string>(type: "text", nullable: false),
                    REPEM05_FechaHora_Recoleccion = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    REPEM05_Empresa = table.Column<string>(type: "text", nullable: false),
                    REPEM05_NIT = table.Column<long>(type: "bigint", nullable: false),
                    REPEM05_Codigo_DANE = table.Column<string>(type: "text", nullable: false),
                    REPEM05_Toneladas = table.Column<decimal>(type: "numeric", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("Reporting-Emvarias_05-Recoleccion_Vehicular_key", x => x.REPEM05_Id);
                });

            migrationBuilder.CreateTable(
                name: "Reporting-Emvarias_03-Pesaje_de_Balanza",
                columns: table => new
                {
                    REPEM03_Id = table.Column<string>(type: "text", nullable: false),
                    REPEM03_Patente = table.Column<string>(type: "text", nullable: false),
                    REPEM03_Nro_Identificacion_Tributaria = table.Column<long>(type: "bigint", nullable: false),
                    REPEM03_Peso_de_entrada = table.Column<double>(type: "double precision", precision: 18, scale: 2, nullable: false),
                    REPEM03_Peso_de_salida = table.Column<double>(type: "double precision", precision: 18, scale: 2, nullable: false),
                    REPEM03_Fecha_de_entrada = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    REPEM03_Fecha_de_egreso = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    REPEM03_Lugar_de_deposito = table.Column<long>(type: "bigint", nullable: false),
                    REPEM03_Tipo_de_origen = table.Column<string>(type: "text", nullable: false),
                    REPEM03_Nro_Unico_Area_Prestacion = table.Column<long>(type: "bigint", nullable: false),
                    REPEM03_Fecha_de_carga = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    REPEM03_Tipo_de_carga = table.Column<string>(type: "text", nullable: false),
                    REPEM03_Municipio = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("Reporting-Emvarias_03-Pesaje_de_Balanza_key", x => x.REPEM03_Id);
                    table.ForeignKey(
                        name: "Reporting-Emvarias_REPEM03-REPEM01_fkey",
                        column: x => x.REPEM03_Municipio,
                        principalTable: "Reporting-Emvarias_01-Municipios",
                        principalColumn: "REPEM01_Codigo",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Reporting-Emvarias_03-Pesaje_de_Balanza_REPEM03_Municipio",
                table: "Reporting-Emvarias_03-Pesaje_de_Balanza",
                column: "REPEM03_Municipio");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Reporting-Emvarias_02-Areas_de_Aprovechamiento");

            migrationBuilder.DropTable(
                name: "Reporting-Emvarias_03-Pesaje_de_Balanza");

            migrationBuilder.DropTable(
                name: "Reporting-Emvarias_04-Recoleccion_por_Microruta");

            migrationBuilder.DropTable(
                name: "Reporting-Emvarias_05-Recoleccion_Vehicular");

            migrationBuilder.DropTable(
                name: "Reporting-Emvarias_01-Municipios");
        }
    }
}
