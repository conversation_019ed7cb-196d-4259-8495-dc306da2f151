using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Reports.Infrastructure.Data.EFCore.Migrations
{
    public partial class Add_Sync_And_Scheduled_Task_Tables : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Reporting-Emvarias_14-Sincronizacion_Urbetrack",
                columns: table => new
                {
                    REPEM14_Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    REPEM14_Id_Pesaje_Balanza = table.Column<long>(type: "bigint", nullable: false),
                    REPEM14_Estado = table.Column<string>(type: "text", nullable: false),
                    REPEM14_Nro_Reintentos = table.Column<int>(type: "integer", nullable: false),
                    REPEM14_Ultima_Sincronizacion = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    REPEM14_Id_Interno_Urbetrack = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("Reporting-Emvarias_14-Sincronizacion_Urbetrack_key", x => x.REPEM14_Id);
                    table.ForeignKey(
                        name: "FK_REPEM14_REPEM03",
                        column: x => x.REPEM14_Id_Pesaje_Balanza,
                        principalTable: "Reporting-Emvarias_03-Pesaje_de_Balanza",
                        principalColumn: "REPEM03_Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Reporting-Emvarias_15-Parametros_Tareas_Programadas",
                columns: table => new
                {
                    REPEM15_Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    REPEM15_Nombre = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    REPEM15_Descripcion = table.Column<string>(type: "character varying(250)", maxLength: 250, nullable: true),
                    REPEM15_Instancia_Ejecutora = table.Column<string>(type: "text", nullable: false),
                    REPEM15_Habilitado = table.Column<bool>(type: "boolean", nullable: false),
                    REPEM15_Frecuencia_en_Minutos = table.Column<int>(type: "integer", nullable: false),
                    REPEM15_Ultima_Ejecucion = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    REPEM15_Proxima_Ejecucion = table.Column<DateTime>(type: "timestamp without time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("Reporting-Emvarias_15-Parametros_Tareas_Programadas_key", x => x.REPEM15_Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Reporting-Emvarias_14-Sincronizacion_Urbetrack_REPEM14_Id_P~",
                table: "Reporting-Emvarias_14-Sincronizacion_Urbetrack",
                column: "REPEM14_Id_Pesaje_Balanza");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Reporting-Emvarias_14-Sincronizacion_Urbetrack");

            migrationBuilder.DropTable(
                name: "Reporting-Emvarias_15-Parametros_Tareas_Programadas");
        }
    }
}
