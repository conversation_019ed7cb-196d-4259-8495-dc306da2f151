using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Reports.Infrastructure.Data.EFCore.Migrations
{
    public partial class Agregar_Toneladas_Rutas_Compartidas_En_Distribuciones : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
             migrationBuilder.Sql(@"
                CREATE OR REPLACE VIEW ""REPEM_Distribuciones_de_Microrutas""
                AS SELECT rv.""REPEM05_Año"" AS ""Año"",
                    rv.""REPEM05_Mes"" AS ""Mes"",
                    ap.""REPEM02_Codigo"" AS ""NUAP"",
                    ap.""REPEM02_Nombre"" AS ""Area_Aprovechamiento"",
                    count(*) AS ""Cantidad_de_Viajes"",
                    rv.""REPEM05_Toneladas""::numeric AS ""Toneladas_Reportadas"",
                    trunc((rv.""REPEM05_Toneladas"" / count(*)::numeric)::numeric(18, 4),
                        3)::numeric(18, 3) AS ""Toneladas_Distribuidas"",
                    round(((rv.""REPEM05_Toneladas"" / count(*)::numeric) - trunc((rv.""REPEM05_Toneladas"" / count(*)::numeric)::numeric(18, 4),
                        3)::numeric(18, 3)) * count(*), 3)::numeric(18, 3) AS ""Toneladas_Desviacion"",
                    (rv.""REPEM05_Toneladas"" / src.""Suma_Pesos_Toneladas"" * 100::numeric)::numeric(18, 2) AS ""Porcentaje_Distribucion_Peaje"",
                    src.""Suma_Pesos_Toneladas"" as ""Toneladas_Rutas_Compartidas""
                   FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm2
                     JOIN ""Reporting-Emvarias_07-Microrutas"" mic2 ON rpm2.""REPEM04_RutaCodigo""::text = mic2.""REPEM07_Numero_de_Microruta""::character varying(16)::text
                     JOIN ""Reporting-Emvarias_05-Recoleccion_Vehicular"" rv ON rv.""REPEM05_Año""::numeric = EXTRACT(year FROM rpm2.""REPEM04_FechaHora_Pesaje"") AND rv.""REPEM05_Mes""::numeric = EXTRACT(month FROM rpm2.""REPEM04_FechaHora_Pesaje"") AND rv.""REPEM05_NUAP"" = mic2.""REPEM07_NUAP""
                     JOIN ""Reporting-Emvarias_02-Areas_de_Aprovechamiento"" ap ON ap.""REPEM02_Codigo"" = mic2.""REPEM07_NUAP""
                     JOIN ""REPEM_Sumatoria_Pesos_Rutas_Compartidas"" src ON src.""Año"" = EXTRACT(year FROM rpm2.""REPEM04_FechaHora_Pesaje"") AND src.""Mes"" = EXTRACT(month FROM rpm2.""REPEM04_FechaHora_Pesaje"")
                  WHERE mic2.""REPEM07_Porcentaje_No_Aforado""::numeric = 0::numeric AND ap.""REPEM02_Codigo"" <> 440405001 AND NOT (mic2.""REPEM07_NUAP"" = 441005380 AND mic2.""REPEM07_Ruta_Larga"" = '0911504'::bpchar)
                  GROUP BY rv.""REPEM05_Toneladas"", src.""Suma_Pesos_Toneladas"", ap.""REPEM02_Nombre"", rv.""REPEM05_Año"", rv.""REPEM05_Mes"", ap.""REPEM02_Codigo""
                  ORDER BY rv.""REPEM05_Año"", rv.""REPEM05_Mes"", ap.""REPEM02_Nombre"";
            ");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"
                CREATE OR REPLACE VIEW ""REPEM_Distribuciones_de_Microrutas""
                AS SELECT rv.""REPEM05_Año"" AS ""Año"",
                    rv.""REPEM05_Mes"" AS ""Mes"",
                    ap.""REPEM02_Codigo"" AS ""NUAP"",
                    ap.""REPEM02_Nombre"" AS ""Area_Aprovechamiento"",
                    count(*) AS ""Cantidad_de_Viajes"",
                    rv.""REPEM05_Toneladas""::numeric AS ""Toneladas_Reportadas"",
                    trunc((rv.""REPEM05_Toneladas"" / count(*)::numeric)::numeric(18, 4),
                        3)::numeric(18, 3) AS ""Toneladas_Distribuidas"",
                    round(((rv.""REPEM05_Toneladas"" / count(*)::numeric) - trunc((rv.""REPEM05_Toneladas"" / count(*)::numeric)::numeric(18, 4),
                        3)::numeric(18, 3)) * count(*), 3)::numeric(18, 3) AS ""Toneladas_Desviacion"",
                    (rv.""REPEM05_Toneladas"" / src.""Suma_Pesos_Toneladas"" * 100::numeric)::numeric(18, 2) AS ""Porcentaje_Distribucion_Peaje""
                   FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm2
                     JOIN ""Reporting-Emvarias_07-Microrutas"" mic2 ON rpm2.""REPEM04_RutaCodigo""::text = mic2.""REPEM07_Numero_de_Microruta""::character varying(16)::text
                     JOIN ""Reporting-Emvarias_05-Recoleccion_Vehicular"" rv ON rv.""REPEM05_Año""::numeric = EXTRACT(year FROM rpm2.""REPEM04_FechaHora_Pesaje"") AND rv.""REPEM05_Mes""::numeric = EXTRACT(month FROM rpm2.""REPEM04_FechaHora_Pesaje"") AND rv.""REPEM05_NUAP"" = mic2.""REPEM07_NUAP""
                     JOIN ""Reporting-Emvarias_02-Areas_de_Aprovechamiento"" ap ON ap.""REPEM02_Codigo"" = mic2.""REPEM07_NUAP""
                     JOIN ""REPEM_Sumatoria_Pesos_Rutas_Compartidas"" src ON src.""Año"" = EXTRACT(year FROM rpm2.""REPEM04_FechaHora_Pesaje"") AND src.""Mes"" = EXTRACT(month FROM rpm2.""REPEM04_FechaHora_Pesaje"")
                  WHERE mic2.""REPEM07_Porcentaje_No_Aforado""::numeric = 0::numeric AND ap.""REPEM02_Codigo"" <> 440405001 AND NOT (mic2.""REPEM07_NUAP"" = 441005380 AND mic2.""REPEM07_Ruta_Larga"" = '0911504'::bpchar)
                  GROUP BY rv.""REPEM05_Toneladas"", src.""Suma_Pesos_Toneladas"", ap.""REPEM02_Nombre"", rv.""REPEM05_Año"", rv.""REPEM05_Mes"", ap.""REPEM02_Codigo""
                  ORDER BY rv.""REPEM05_Año"", rv.""REPEM05_Mes"", ap.""REPEM02_Nombre"";
            ");
        }
    }
}
