using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Reports.Domain.Constants;
using Reports.Domain.Entities;

namespace Reports.Infrastructure.Data.EFCore.Configurations.PostgreSQL;

public class CollectionByMicrorouteConfiguration : IEntityTypeConfiguration<CollectionByMicroroute>
{
    private const string DateTimeWithoutTimeZone = "timestamp without time zone";
    
    public void Configure(EntityTypeBuilder<CollectionByMicroroute> builder)
    {
        builder.ToTable(ReportsTableNames.CollectionByMicroroute);

        builder
            .Property(p => p.Id)
            .HasColumnName(CollectionByMicrorouteColumns.Id);

        builder
            .Property(p => p.LicensePlate)
            .HasColumnName(CollectionByMicrorouteColumns.LicensePlate)
            .HasMaxLength(6)
            .IsRequired();
        
        builder
            .Property(p => p.ServiceStatus)
            .HasColumnName(CollectionByMicrorouteColumns.ServiceStatus)
            .HasMaxLength(10)
            .IsRequired();
        
        builder
            .Property(p => p.GroupTurn)
            .HasColumnName(CollectionByMicrorouteColumns.GroupTurn)
            .HasMaxLength(10)
            .IsRequired();
        
        builder
            .Property(p => p.ServiceType)
            .HasColumnName(CollectionByMicrorouteColumns.ServiceType)
            .HasMaxLength(50)
            .IsRequired();
        
        builder
            .Property(p => p.RouteCode)
            .HasColumnName(CollectionByMicrorouteColumns.RouteCode)
            .HasMaxLength(20)
            .IsRequired();
        
        builder
            .Property(p => p.IsReinforcement)
            .HasColumnName(CollectionByMicrorouteColumns.IsReinforcement)
            .IsRequired();
        
        builder
            .Property(p => p.Intern)
            .HasColumnName(CollectionByMicrorouteColumns.Intern)
            .HasMaxLength(20)
            .IsRequired();
        
        builder
            .Property(p => p.TotalWeight)
            .HasColumnName(CollectionByMicrorouteColumns.TotalWeight)
            .HasPrecision(18, 2)
            .IsRequired();
        
        builder
            .Property(p => p.ServiceId)
            .HasColumnName(CollectionByMicrorouteColumns.ServiceId)
            .IsRequired();
        
        builder
            .Property(p => p.RouteArrivalDate)
            .HasColumnType(DateTimeWithoutTimeZone)
            .HasColumnName(CollectionByMicrorouteColumns.RouteArrivalDate);
        
        builder
            .Property(p => p.RouteDepartureDate)
            .HasColumnType(DateTimeWithoutTimeZone)
            .HasColumnName(CollectionByMicrorouteColumns.RouteDepartureDate);
        
        builder
            .Property(p => p.Observations)
            .HasColumnName(CollectionByMicrorouteColumns.Observations);
        
        builder
            .Property(p => p.TotalTonnage)
            .HasColumnName(CollectionByMicrorouteColumns.TotalTonnage)
            .HasPrecision(18, 3)
            .IsRequired();
        
        builder
            .Property(p => p.WeighingDate)
            .HasColumnType(DateTimeWithoutTimeZone)
            .HasColumnName(CollectionByMicrorouteColumns.WeighingDate);
        
        builder
            .Property(p => p.ServicingDate)
            .HasColumnType(DateTimeWithoutTimeZone)
            .HasColumnName(CollectionByMicrorouteColumns.ServicingDate);
        
        builder
            .Property(p => p.ServiceStartDate)
            .HasColumnType(DateTimeWithoutTimeZone)
            .HasColumnName(CollectionByMicrorouteColumns.ServiceStartDate);
        
        builder
            .Property(p => p.BaseArrivalDate)
            .HasColumnType(DateTimeWithoutTimeZone)
            .HasColumnName(CollectionByMicrorouteColumns.BaseArrivalDate);
        
        builder
            .Property(p => p.BaseDepartureDate)
            .HasColumnType(DateTimeWithoutTimeZone)
            .HasColumnName(CollectionByMicrorouteColumns.BaseDepartureDate);
        
        builder
            .HasKey(p => p.Id)
            .HasName(CollectionByMicrorouteColumns.PrimaryKeyConstraintName);
    }
}