using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Reports.Domain.Constants;
using Reports.Domain.Entities;

namespace Reports.Infrastructure.Data.EFCore.Configurations.PostgreSQL;

public class ScheduledTaskParameterConfiguration : IEntityTypeConfiguration<ScheduledTaskParameter>
{
    public void Configure(EntityTypeBuilder<ScheduledTaskParameter> builder)
    {
        builder.ToTable(ReportsTableNames.ScheduledTaskParameter);

        builder.
            HasKey(e => e.Id)
            .HasName(ScheduledTaskParameterColumns.PrimaryKeyConstraintName);
        
        builder
            .Property(e => e.Id)
            .HasColumnName(ScheduledTaskParameterColumns.Id);
        
        builder
            .Property(e => e.Name)
            .HasColumnName(ScheduledTaskParameterColumns.Name)
            .HasMaxLength(100)
            .IsRequired();
        
        builder
            .Property(e => e.Description)
            .HasColumnName(ScheduledTaskParameterColumns.Description)
            .HasMaxLength(250);
        
        builder
            .Property(e => e.ScheduledTaskExecutors)
            .HasColumnName(ScheduledTaskParameterColumns.JobExecutor)
            .HasConversion(new EnumToStringConverter<ScheduledTaskExecutors>())
            .IsRequired();
        
        builder
            .Property(e => e.IsEnabled)
            .HasColumnName(ScheduledTaskParameterColumns.IsEnabled)
            .IsRequired();
        
        builder
            .Property(e => e.FrequencyInMinutes)
            .HasColumnName(ScheduledTaskParameterColumns.FrequencyInMinutes)
            .IsRequired();
        
        builder
            .Property(e => e.LastRunTime)
            .HasColumnName(ScheduledTaskParameterColumns.LastRunTime)
            .HasColumnType("timestamp without time zone");
        
        builder
            .Property(e => e.NextRunTime)
            .HasColumnName(ScheduledTaskParameterColumns.NextRunTime)
            .HasColumnType("timestamp without time zone");
    }
}
