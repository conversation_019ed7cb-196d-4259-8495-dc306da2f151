using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Reports.Domain.Constants;
using Reports.Domain.Entities;

namespace Reports.Infrastructure.Data.EFCore.Configurations.PostgreSQL;

public class MicroRouteConfiguration : IEntityTypeConfiguration<MicroRoute>
{
    private const string DateTimeWithoutTimeZone = "timestamp without time zone";
    public void Configure(EntityTypeBuilder<MicroRoute> builder)
    {
        builder.ToTable(ReportsTableNames.MicroRoute);
        
        builder
            .Property(p => p.Id)
            .HasColumnName(MicroRouteColumns.MicrorouteCode)
            .IsRequired();
        
        builder
            .Property(p => p.NUAP)
            .HasColumnName(MicroRouteColumns.NUAP)
            .IsRequired();

        builder
            .Property(p => p.ExternalRouteCode)
            .HasColumnName(MicroRouteColumns.ExternalRouteCode)
            .IsRequired();

        builder
            .Property(p => p.ExtendedRouteCode)
            .HasColumnName(MicroRouteColumns.ExtendedRouteCode)
            .HasColumnType("char(7)");

        builder
            .Property(p => p.UrbanCleaningPercentage)
            .HasColumnName(MicroRouteColumns.UrbanCleaningPercentage)
            .IsRequired();
        
        builder
            .Property(p => p.SweepingPercentage)
            .HasColumnName(MicroRouteColumns.SweepingPercentage)
            .IsRequired();
        
        builder
            .Property(p => p.NonAforatedPercentage)
            .HasColumnName(MicroRouteColumns.NonAforatedPercentage)
            .IsRequired();
        
        builder
            .Property(p => p.RecyclableTonsPercentage)
            .HasColumnName(MicroRouteColumns.RecyclableTonsPercentage)
            .IsRequired();
        
        builder
            .Property(p => p.StartDateValidity)
            .HasColumnType(DateTimeWithoutTimeZone)
            .HasColumnName(MicroRouteColumns.StartDateValidity)
            .IsRequired();

        builder
            .Property(p => p.EndDateValidity)
            .HasColumnType(DateTimeWithoutTimeZone)
            .HasColumnName(MicroRouteColumns.EndDateValidity);
        
        builder
            .HasKey(p => new { p.Id, p.NUAP, p.StartDateValidity})
            .HasName(MicroRouteColumns.PrimaryKeyConstraintName);
    }
}