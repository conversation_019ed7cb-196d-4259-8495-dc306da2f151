using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Reports.Domain.Constants;
using Reports.Domain.Entities;

namespace Reports.Infrastructure.Data.EFCore.Configurations.PostgreSQL;

public class ReportFormat14AditionRecyclablesConfiguration : IEntityTypeConfiguration<ReportsFormat14AditionRecyclables>
{
    public void Configure(EntityTypeBuilder<ReportsFormat14AditionRecyclables> builder)
    {
        builder.ToTable(ReportsTableNames.Report14AditionalDataRecyclableWaste);

        builder.HasNoKey();
        builder.Ignore(r => r.Id);
        
        builder
            .Property(p => p.NUAP)
            .HasColumnName(ReportFormat14ViewColumns.NUAP);
        
        builder
            .Property(p => p.DestinationType)
            .HasColumnName(ReportFormat14ViewColumns.DestinationType);
        
        builder
            .Property(p => p.DestinationCode)
            .HasColumnName(ReportFormat14ViewColumns.DestinationCode);
        
        builder
            .Property(p => p.LicensePlate)
            .HasColumnName(ReportFormat14ViewColumns.LicensePlate);
        
        builder
            .Property(p => p.VehicleArrival)
            .HasColumnName(ReportFormat14ViewColumns.VehicleArrival);
        
        builder
            .Property(p => p.VehicleArrivalTime)
            .HasColumnName(ReportFormat14ViewColumns.VehicleArrivalTime);
        
        builder
            .Property(p => p.MicrorouteId)
            .HasColumnName(ReportFormat14ViewColumns.MicrorouteId);
        
        builder
            .Property(p => p.UrbanCleaningTons)
            .HasColumnName(ReportFormat14ViewColumns.UrbanCleaningTons);
        
        builder
            .Property(p => p.SweepingTons)
            .HasColumnName(ReportFormat14ViewColumns.SweepingTons);
        
        builder
            .Property(p => p.NonRecyclableTons)
            .HasColumnName(ReportFormat14ViewColumns.NonRecyclableTons);
        
        builder
            .Property(p => p.RejectedTons)
            .HasColumnName(ReportFormat14ViewColumns.RejectedTons);
        
        builder
            .Property(p => p.RecyclableTons)
            .HasColumnName(ReportFormat14ViewColumns.RecyclableTons);
        
        builder
            .Property(p => p.MeasuringUnit)
            .HasColumnName(ReportFormat14ViewColumns.MeasuringUnit);
        
        builder
            .Property(p => p.Toll)
            .HasColumnName(ReportFormat14ViewColumns.Toll);
        
        builder
            .Property(p => p.ExtendedRouteCode)
            .HasColumnName(ReportFormat14ViewColumns.ExtendedRouteCode);
        
        builder
            .Property(p => p.TotalTons)
            .HasColumnName(ReportFormat14ViewColumns.TotalTons);
    }
}