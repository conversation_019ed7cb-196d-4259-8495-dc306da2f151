using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Reports.Domain.Constants;
using Reports.Domain.Entities;

namespace Reports.Infrastructure.Data.EFCore.Configurations.PostgreSQL;

public class WeighingScaleConfiguration : IEntityTypeConfiguration<WeighingScale>
{
    private const string DateTimeWithoutTimeZone = "timestamp without time zone";

    public void Configure(EntityTypeBuilder<WeighingScale> builder)
    {
        builder.ToTable(ReportsTableNames.WeighingScale);

        builder
            .Property(p => p.Id)
            .HasColumnName(WeighingScaleColumns.Id)
            .IsRequired();

        builder
            .Property(p => p.LicensePlate)
            .HasColumnName(WeighingScaleColumns.LicensePlate)
            .HasMaxLength(6)
            .IsRequired();

        builder
            .Property(p => p.NIT)
            .HasColumnName(WeighingScaleColumns.NIT);

        builder
            .Property(p => p.ArrivingWeight)
            .HasColumnName(WeighingScaleColumns.ArrivingWeight)
            .HasPrecision(18, 2);

        builder
            .Property(p => p.DepositWeight)
            .HasColumnName(WeighingScaleColumns.LeavingWeight)
            .HasPrecision(18, 2);

        builder
            .Property(p => p.EntryDate)
            .HasColumnType(DateTimeWithoutTimeZone)
            .HasColumnName(WeighingScaleColumns.EntryDate);

        builder
            .Property(p => p.EgressDate)
            .HasColumnType(DateTimeWithoutTimeZone)
            .HasColumnName(WeighingScaleColumns.EgressDate);

        builder
            .Property(p => p.DepositPlace)
            .HasColumnName(WeighingScaleColumns.DepositPlace);

        builder
            .Property(p => p.MaterialType)
            .HasColumnName(WeighingScaleColumns.MaterialType)
            .IsRequired();

        builder
            .Property(p => p.LoadingDate)
            .HasColumnType(DateTimeWithoutTimeZone)
            .HasColumnName(WeighingScaleColumns.LoadingDate);

        builder
            .Property(p => p.CancelDate)
            .HasColumnType(DateTimeWithoutTimeZone)
            .HasColumnName(WeighingScaleColumns.CancelDate);

        builder
            .Property(p => p.LoadingType)
            .HasColumnType("character")
            .HasColumnName(WeighingScaleColumns.LoadingType)
            .HasConversion(new EnumToStringConverter<LoadingType>());

        builder
            .Property(p => p.NUAP)
            .HasColumnName(WeighingScaleColumns.NUAP);

        builder
            .Property(p => p.OriginType)
            .HasColumnType("text")
            .HasColumnName(WeighingScaleColumns.OriginType)
            .HasConversion(new EnumToStringConverter<OriginType>());

        builder
            .HasKey(p => p.Id)
            .HasName(WeighingScaleColumns.PrimaryKeyConstraintName);

        builder
            .Property(p => p.TownCode)
            .HasMaxLength(5)
            .HasColumnName(WeighingScaleColumns.Town);

        builder
            .HasOne(p => p.Town)
            .WithMany()
            .HasForeignKey(p => p.TownCode)
            .HasPrincipalKey(p => p.Code)
            .HasConstraintName(WeighingScaleColumns.WeighingScaleTownConstraintName);

        builder
            .Ignore(p => p.CompanyName)
            .Ignore(p => p.LeavingWeight);
    }
}