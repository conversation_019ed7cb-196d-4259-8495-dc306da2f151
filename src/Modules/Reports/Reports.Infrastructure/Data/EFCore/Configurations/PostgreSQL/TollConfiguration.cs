using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Reports.Domain.Constants;
using Reports.Domain.Entities;

namespace Reports.Infrastructure.Data.EFCore.Configurations.PostgreSQL;

public class TollConfiguration : IEntityTypeConfiguration<Toll>
{
    private const string DateTimeWithoutTimeZone = "timestamp without time zone";

    public void Configure(EntityTypeBuilder<Toll> builder)
    {
        builder.ToTable(ReportsTableNames.Toll);
        
        builder
            .Property(p => p.Id)
            .HasColumnName(TollColumns.Id)
            .ValueGeneratedOnAdd();

        builder
            .Property(p => p.VehicleType)
            .HasColumnName(TollColumns.VehicleType)
            .HasMaxLength(50)
            .IsRequired();
        
        builder
            .Property(p => p.LicensePlate)
            .HasColumnName(TollColumns.LicensePlate)
            .HasMaxLength(6)
            .IsRequired();
        
        builder
            .Property(p => p.ValidFromDate)
            .HasColumnType(DateTimeWithoutTimeZone)
            .HasColumnName(TollColumns.ValidFromDate)
            .IsRequired();

        builder
            .Property(p => p.Value)
            .HasPrecision(18, 3)
            .HasColumnName(TollColumns.Value)
            .IsRequired();

        builder
            .HasKey(p => p.Id)
            .HasName(TollColumns.PrimaryKeyConstraintName);
    }
}