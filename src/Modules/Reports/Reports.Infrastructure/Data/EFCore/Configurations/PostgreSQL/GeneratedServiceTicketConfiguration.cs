using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Reports.Domain.Constants;
using Reports.Domain.Entities;

namespace Reports.Infrastructure.Data.EFCore.Configurations.PostgreSQL;

public class GeneratedServiceTicketConfiguration : IEntityTypeConfiguration<GeneratedServiceTicket>
{
    public void Configure(EntityTypeBuilder<GeneratedServiceTicket> builder)
    {
        builder.ToTable(ReportsTableNames.GeneratedTickets);
        
        builder
            .Property(p => p.Id)
            .HasColumnName(GeneratedServiceTicketColumns.Id)
            .IsRequired();
        
        builder
            .Property(p => p.Weight)
            .HasColumnName(GeneratedServiceTicketColumns.Weight)
            .HasPrecision(18, 2)
            .IsRequired();
        
        builder
            .HasKey(p => p.Id)
            .HasName(GeneratedServiceTicketColumns.PrimaryKeyConstraintName);
    }
}