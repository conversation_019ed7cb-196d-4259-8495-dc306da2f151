using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Reports.Domain.Constants;
using Reports.Domain.Entities;

namespace Reports.Infrastructure.Data.EFCore.Configurations.PostgreSQL;

public class ReportFormat34Configuration : IEntityTypeConfiguration<ReportFormat34>
{
    public void Configure(EntityTypeBuilder<ReportFormat34> builder)
    {
        builder.ToView(ReportsViewNames.ReportFormat34);
        
        builder.HasNoKey();
        builder.Ignore(p => p.Id);
        
        builder
            .Property(p => p.NUSD)
            .HasColumnName(ReportFormat34ViewColumns.NUSD);
        
        builder
            .Property(p => p.OriginType)
            .HasColumnName(ReportFormat34ViewColumns.OriginType);
        
        builder
            .Property(p => p.PlaceOriginNumber)
            .HasColumnName(ReportFormat34ViewColumns.PlaceOriginNumber);
        
        builder
            .Property(p => p.CompanyName)
            .HasColumnName(ReportFormat34ViewColumns.CompanyName);
        
        builder
            .Property(p => p.NIT)
            .HasColumnName(ReportFormat34ViewColumns.NIT);
        
        builder
            .Property(p => p.DaneCode)
            .HasColumnName(ReportFormat34ViewColumns.DaneCode);
        
        builder
            .Property(p => p.LicensePlate)
            .HasColumnName(ReportFormat34ViewColumns.LicensePlate);
        
        builder
            .Property(p => p.ArrivalDate)
            .HasColumnName(ReportFormat34ViewColumns.ArrivalDate);
        
        builder
            .Property(p => p.DepartureDate)
            .HasColumnName(ReportFormat34ViewColumns.DepartureDate);
        
        builder
            .Property(p => p.ArrivalTime)
            .HasColumnName(ReportFormat34ViewColumns.ArrivalTime);
        
        builder
            .Property(p => p.DepartureTime)
            .HasColumnName(ReportFormat34ViewColumns.DepartureTime);
        
        builder
            .Property(p => p.Tons)
            .HasColumnName(ReportFormat34ViewColumns.Tons);
        
        builder
            .Property(p => p.ServiceTicketId)
            .HasColumnName(ReportFormat34ViewColumns.ServiceTicketId);
        
        builder
            .Property(p => p.ServiceTicketWeight)
            .HasColumnName(ReportFormat34ViewColumns.ServiceTicketWeight);
        
        builder
            .Property(p => p.ServiceTicketTonnage)
            .HasColumnName(ReportFormat34ViewColumns.ServiceTicketTonnage);
        
        builder
            .Property(p => p.RejectedTonnage)
            .HasColumnName(ReportFormat34ViewColumns.RejectedTonnage);
        
        builder
            .Property(p => p.ExistsInReport14)
            .HasColumnName(ReportFormat34ViewColumns.ExistsInReport14);
        
        builder
            .Property(p => p.CompanyNIT)
            .HasColumnName(ReportFormat34ViewColumns.CompanyNIT);
        
        builder
            .Property(p => p.NUAP)
            .HasColumnName(ReportFormat34ViewColumns.NUAP);
        
        builder
            .Property(p => p.FilteredDate)
            .HasColumnName(ReportFormat34ViewColumns.FilteredDate);
    }
}