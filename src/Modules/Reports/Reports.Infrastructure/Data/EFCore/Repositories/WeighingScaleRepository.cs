using System.Collections.Immutable;
using System.Linq.Expressions;
using EFCore.BulkExtensions;
using Microsoft.EntityFrameworkCore;
using Orion.SharedKernel.Application.Common.Services;
using Orion.SharedKernel.Infrastructure.Data.EFCore.ContextProvider;
using Orion.SharedKernel.Infrastructure.Data.EFCore.Repositories.Entities;
using Orion.SharedKernel.Domain.Entities;
using Reports.Domain.Entities;
using Reports.Domain.Repositories;
using Reports.Domain.Common;
using Reports.Domain.ValueObjects;
using Reports.Infrastructure.Extensions;

namespace Reports.Infrastructure.Data.EFCore.Repositories;

public class WeighingScaleRepository : Repository<WeighingScale, long, ReportsDbContext>, IWeighingScaleRepository
{
    public WeighingScaleRepository(IDbContextProvider<ReportsDbContext> dbContextProvider, ICacheService cacheService)
        : base(dbContextProvider, cacheService) { }

    public async Task BulkInsertAsync(List<WeighingScale> weighins, CancellationToken cancellationToken)
    {
        await _context.BulkInsertAsync(weighins,
            new BulkConfig
            {
                PreserveInsertOrder = false,
                SetOutputIdentity = false
            },
            cancellationToken: cancellationToken);
    }

    public async Task BulkUpdateAsync(List<WeighingScale> weighins, CancellationToken cancellationToken)
    {
        await _context.BulkUpdateAsync(weighins,
            new BulkConfig
            {
                PreserveInsertOrder = false,
                SetOutputIdentity = false,
                PropertiesToIncludeOnUpdate = new List<string>
                {
                    nameof(WeighingScale.LicensePlate),
                    nameof(WeighingScale.NIT),
                    nameof(WeighingScale.TownCode),
                    nameof(WeighingScale.NUAP),
                    nameof(WeighingScale.OriginType),
                    nameof(WeighingScale.CancelDate),
                }
            },
            cancellationToken: cancellationToken);
    }

    public async Task<PaginatedResult<WeighingScale>> GetFilteredReportAsync(Expression<Func<WeighingScale, bool>> predicate,
        bool isPaginated,
        SortingOptions sortingOptions,
        CancellationToken cancellationToken,
        int pageNumber = 1,
        int pageSize = 10)
    {
        var query = _context.WeighingScales
            .AsQueryable()
            .AsNoTracking()
            .Include(w => w.Town)
            .Where(predicate)
            .ApplySortOptions<WeighingScale, long>(sortingOptions ?? SortingOptions.Default);
        
        if (!isPaginated)
            return new PaginatedResult<WeighingScale> { Results = await query.ToListAsync(cancellationToken) };

        var totalCount = await query.CountAsync(cancellationToken);
        
        var result = await query
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken: cancellationToken);

        return new PaginatedResult<WeighingScale>
        {
            Results = result,
            TotalRecords = totalCount,
            PageSize = pageSize,
            PageNumber = pageNumber
        };
    }
}