using Microsoft.EntityFrameworkCore;
using Orion.SharedKernel.Application.Common.Services;
using Orion.SharedKernel.Infrastructure.Data.EFCore.ContextProvider;
using Orion.SharedKernel.Infrastructure.Data.EFCore.Repositories.Entities;
using Reports.Domain.Constants;
using Reports.Domain.Entities;
using Reports.Domain.Repositories;

namespace Reports.Infrastructure.Data.EFCore.Repositories;

public class UrbetrackSynchronizationRepository : Repository<UrbetrackSynchronization, int, ReportsDbContext>, IUrbetrackSynchronizationRepository
{
    public UrbetrackSynchronizationRepository(IDbContextProvider<ReportsDbContext> dbContextProvider, ICacheService cacheService) : base(dbContextProvider, cacheService) { }

    public async Task<Dictionary<long, UrbetrackSynchronization>> GetByWeighingScaleIdsAsync(IEnumerable<long> weighingScaleIds, CancellationToken cancellationToken = default)
    {
        return await _context.UrbetrackSynchronizations
            .Where(us => weighingScaleIds.Contains(us.WeighingScaleId))
            .ToDictionaryAsync(us => us.WeighingScaleId, us => us, cancellationToken);
    }

    public async Task<List<UrbetrackSynchronization>> GetByStatusAsync(SynchronizationStatus status, CancellationToken cancellationToken = default)
    {
        return await _context.UrbetrackSynchronizations
            .Where(us => us.Status == status)
            .Include(us => us.WeighingScale)
            .ToListAsync(cancellationToken);
    }

    public async Task BulkInsertAsync(List<UrbetrackSynchronization> synchronizations, CancellationToken cancellationToken = default)
    {
        await _context.UrbetrackSynchronizations.AddRangeAsync(synchronizations, cancellationToken);
    }

    public async Task BulkUpdateAsync(List<UrbetrackSynchronization> synchronizations, CancellationToken cancellationToken = default)
    {
        _context.UrbetrackSynchronizations.UpdateRange(synchronizations);
    }

    public async Task<List<UrbetrackSynchronization>> AcquireLockBatchAsync(
        SynchronizationStatus status,
        int batchSize,
        string lockIdentifier,
        CancellationToken cancellationToken = default)
    {
        var availableEntries = await _context.UrbetrackSynchronizations
            .Where(us => us.Status == status)
            .OrderBy(us => us.Id)
            .Take(batchSize)
            .ToListAsync(cancellationToken);

        if (!availableEntries.Any())
            return new List<UrbetrackSynchronization>();

        foreach (var entry in availableEntries)
        {
            entry.AcquireLock(lockIdentifier);
        }

        _context.UrbetrackSynchronizations.UpdateRange(availableEntries);
        await _context.SaveChangesAsync(cancellationToken);

        var entryIds = availableEntries.Select(e => e.Id).ToList();
        var lockedEntries = await _context.UrbetrackSynchronizations
            .Where(us => entryIds.Contains(us.Id))
            .Include(us => us.WeighingScale)
            .ToListAsync(cancellationToken);

        return lockedEntries;
    }

    public async Task ReleaseLockBatchAsync(IEnumerable<UrbetrackSynchronization> entries, CancellationToken cancellationToken = default)
    {
        var entriesList = entries.ToList();
        
        if (!entriesList.Any())
            return;

        foreach (var entry in entriesList)
        {
            entry.ReleaseLock();
        }

        _context.UrbetrackSynchronizations.UpdateRange(entriesList);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task<int> CleanupStaleLocks(TimeSpan lockTimeout, CancellationToken cancellationToken = default)
    {
        var cutoffTime = DateTime.SpecifyKind(DateTime.UtcNow, DateTimeKind.Unspecified) - lockTimeout;

        var staleEntries = await _context.UrbetrackSynchronizations
            .Where(us => (us.Status == SynchronizationStatus.LockedForCreation ||
                         us.Status == SynchronizationStatus.LockedForModification ||
                         us.Status == SynchronizationStatus.LockedForCancellation ||
                         us.Status == SynchronizationStatus.ProcessingCreation ||
                         us.Status == SynchronizationStatus.ProcessingModification ||
                         us.Status == SynchronizationStatus.ProcessingCancellation)
                      && us.LockedAt.HasValue
                      && us.LockedAt.Value < cutoffTime)
            .ToListAsync(cancellationToken);

        if (!staleEntries.Any())
            return 0;

        foreach (var entry in staleEntries)
        {
            entry.ReleaseLock();
        }

        _context.UrbetrackSynchronizations.UpdateRange(staleEntries);
        await _context.SaveChangesAsync(cancellationToken);

        return staleEntries.Count;
    }
}
