using EFCore.BulkExtensions;
using Orion.SharedKernel.Application.Common.Services;
using Orion.SharedKernel.Infrastructure.Data.EFCore.ContextProvider;
using Orion.SharedKernel.Infrastructure.Data.EFCore.Repositories.Entities;
using Reports.Domain.Entities;
using Reports.Domain.Repositories;

namespace Reports.Infrastructure.Data.EFCore.Repositories;

public class HistoricalAuditRepository : Repository<HistoricalAudit, int, ReportsDbContext>, IHistoricalAuditRepository
{
    public HistoricalAuditRepository(IDbContextProvider<ReportsDbContext> dbContextProvider, ICacheService cacheService)
        : base(dbContextProvider, cacheService) { }

    public async Task BulkInsertAsync(List<HistoricalAudit> audits, CancellationToken cancellationToken)
    {
        await _context.BulkInsertAsync(audits,
            new BulkConfig
            {
                PreserveInsertOrder = false,
                SetOutputIdentity = false
            },
            cancellationToken: cancellationToken);
    }
}
