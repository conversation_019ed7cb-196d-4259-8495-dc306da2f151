using Orion.SharedKernel.Domain.Services;
using Orion.SharedKernel.Infrastructure.Data;
using Orion.SharedKernel.Infrastructure.Data.EFCore.ContextProvider;
using Reports.Domain;
using Reports.Domain.Repositories;

namespace Reports.Infrastructure.Data.EFCore;

public class ReportsUnitOfWork : UnitOfWork<ReportsDbContext>, IReportsUnitOfWork
{
    public IWeighingScaleRepository WeighingScales { get; }
    public IVehicleRetrievalRepository VehicleRetrievals { get; }
    public ITownRepository Towns { get; }
    public ITollRepository Tolls { get; }
    public IClientRepository Clients { get; }
    public IMicrorouteRepository Microroutes { get; }
    public IReportFormat14Repository ReportsFormat14 { get; }
    public IReportFormat34Repository ReportsFormat34 { get; }
    public IRecyclingAreaRepository RecyclingAreaRepository { get; }
    public IDistributionRepository DistributionRepository { get; }
    public ICollectionByMicrorouteRepository CollectionByMicrorouteRepository { get; }
    public IHistoricalAuditRepository HistoricalAudits { get; }
    public IUrbetrackSynchronizationRepository UrbetrackSynchronizations { get; }
    public IScheduledTaskParameterRepository ScheduledTaskParameters { get; }
    public IErrorService ErrorService { get; }
    public ILogEventMessage LogEventMessage { get; }

    public ReportsUnitOfWork(IWeighingScaleRepository weighingScaleRepository,
        IVehicleRetrievalRepository vehicleRetrievalRepository,
        ITownRepository towns,
        ITollRepository tolls,
        IClientRepository clients,
        IMicrorouteRepository microroutes,
        IReportFormat14Repository reportFormat14Repository,
        IReportFormat34Repository reportFormat34Repository,
        IRecyclingAreaRepository recyclingAreaRepository,
        IDistributionRepository distributionRepository,
        ICollectionByMicrorouteRepository collectionByMicrorouteRepository,
        IHistoricalAuditRepository historicalAuditRepository,
        IUrbetrackSynchronizationRepository urbetrackSynchronizationRepository,
        IScheduledTaskParameterRepository scheduledTaskParameterRepository,
        IErrorService errorService,
        ILogEventMessage logEventMessage,
        IDbContextProvider<ReportsDbContext> dbContextProvider) : base(dbContextProvider)
    {
        WeighingScales = weighingScaleRepository;
        VehicleRetrievals = vehicleRetrievalRepository;
        Towns = towns;
        Tolls = tolls;
        Clients = clients;
        Microroutes = microroutes;
        ReportsFormat14 = reportFormat14Repository;
        ReportsFormat34 = reportFormat34Repository;
        RecyclingAreaRepository = recyclingAreaRepository;
        DistributionRepository = distributionRepository;
        CollectionByMicrorouteRepository = collectionByMicrorouteRepository;
        HistoricalAudits = historicalAuditRepository;
        UrbetrackSynchronizations = urbetrackSynchronizationRepository;
        ScheduledTaskParameters = scheduledTaskParameterRepository;
        ErrorService = errorService;
        LogEventMessage = logEventMessage;
    }
}