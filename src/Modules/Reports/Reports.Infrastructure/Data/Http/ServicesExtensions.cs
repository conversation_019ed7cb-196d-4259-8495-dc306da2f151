using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Reports.Infrastructure.Data.Http.Legacy;

namespace Reports.Infrastructure.Data.Http;

public static class ServicesExtensions
{
    public static IServiceCollection AddHttpClients(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddLegacyHttpClient(configuration);

        return services;
    }
}