using System.Text.Json;
using Microsoft.Extensions.Options;
using Orion.SharedKernel.Application.Common.Services;
using Orion.SharedKernel.Infrastructure.Data.Http;
using Reports.Application.Services.Http.Legacy;
using Reports.Application.Services.Http.Legacy.Request;
using Reports.Application.Services.Http.Legacy.Response;
using Reports.Domain.Configurations;

namespace Reports.Infrastructure.Data.Http.Legacy;

public class LegacyApiService: ApiService, ILegacyApiService
{
    private readonly LegacyApiConfiguration _legacyApiConfiguration;
    private readonly SemaphoreSlim _semaphore;

    public LegacyApiService(IOptions<LegacyApiConfiguration> legacyApiConfiguration, ICacheService cacheService, HttpClient httpClient, SemaphoreSlim semaphore) : base(cacheService, httpClient)
    {
        _legacyApiConfiguration = legacyApiConfiguration.Value;
        _semaphore = semaphore;
    }
    
    public async Task SendUnloadingTicketAsync(PostUnloadingTicketRequest request)
    {
        await _semaphore.WaitAsync();

        try
        {
            var endpoint = request.ToRoute(_legacyApiConfiguration.UnloadingTicketEndpoint);

            var headersParams = request.ToHeader();
            
            await Post(endpoint: endpoint, headersParams: headersParams, body: request);
        }
        finally
        {
            _semaphore.Release();
        }
    }

    public Task DeleteUnloadingTicketAsync(DeleteUnloadingTicketRequest request)
    {
        var endpoint = request.ToRoute(_legacyApiConfiguration.DeleteUnloadingTicketEndpoint);

        var query = request.ToQuery(false);
        
        var headersParams = request.ToHeader();

        return Delete(endpoint: endpoint, queryParams: query, headersParams: headersParams);
    }

    public Task<IList<GetUnloadingTicketsResponseItem>> GetUnloadingTicketsAsync(GetUnloadingTicketsRequest request)
    {
        var endpoint = request.ToRoute(_legacyApiConfiguration.GetUnloadingTicketsEndpoint);

        var query = request.ToQuery(false);

        var headersParams = request.ToHeader();

        return GetAll<GetUnloadingTicketsResponseItem>(endpoint: endpoint, queryParams: query, headersParams: headersParams, useCache: false);
    }
}