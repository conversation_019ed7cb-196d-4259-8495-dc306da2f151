using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Reports.Application.Services.Http.Legacy;
using Reports.Domain.Configurations;
using System.Threading;
using System.Threading.Tasks;

namespace Reports.Infrastructure.Data.Http.Legacy;

public static class ServicesExtensions
{
    public static IServiceCollection AddLegacyHttpClient(this IServiceCollection services, IConfiguration configuration)
    {
        var legacyApiConfiguration = configuration.GetSection(nameof(LegacyApiConfiguration)).Get<LegacyApiConfiguration>();

        services.AddHttpClient<ILegacyApiService, LegacyApiService>(httpClient =>
        {
            httpClient.BaseAddress = new Uri(legacyApiConfiguration!.UrlBase);
            httpClient.Timeout = TimeSpan.FromMinutes(1);
        });
        
        services.AddSemaphoreSlim(10);

        return services;
    }
    
    private static IServiceCollection AddSemaphoreSlim(this IServiceCollection services, int maxConcurrentTasks)
    {
        services.AddSingleton(_ => new SemaphoreSlim(maxConcurrentTasks));
        return services;
    }
}