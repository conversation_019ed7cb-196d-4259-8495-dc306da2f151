using System.Linq.Expressions;
using System.Reflection;
using Orion.SharedKernel.Domain.Entities;
using Reports.Domain.Common;
using Reports.Domain.Entities;

namespace Reports.Infrastructure.Extensions;

public static class IQueryableExtensions
{
    public static IOrderedQueryable<T> ApplySortOptions<T, TKey>(this IQueryable<T> source,
        SortingOptions sortingOptions) where T : Entity<TKey>
    {
        var sortFields = sortingOptions.SortFields?.Where(field => !string.IsNullOrWhiteSpace(field)).ToList() ??
                         [];
        var isDescending = sortingOptions.SortDirection == SortDirection.Descending;

        if (sortFields.Count == 0)
            return isDescending
                ? source.OrderByDescending(x => x.Id)
                : source.OrderBy(x => x.Id);

        var initialOrder = isDescending
            ? source.OrderByDescending(GetOrderByExpression<T>(sortFields.First()))
            : source.OrderBy(GetOrderByExpression<T>(sortFields.First()));

        return sortFields.Skip(1).Aggregate(initialOrder, (current, field) =>
            isDescending
                ? current.ThenByDescending(GetOrderByExpression<T>(field))
                : current.ThenBy(GetOrderByExpression<T>(field)));
    }

    private static Expression<Func<T, object>> GetOrderByExpression<T>(string propertyName)
    {
        var propertyInfo = typeof(T).GetProperty(propertyName,
            BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);
        if (propertyInfo == null)
            throw new ArgumentException($"Property '{propertyName}' does not exist on type '{typeof(T).Name}'.");

        var parameter = Expression.Parameter(typeof(T), "x");
        var property = Expression.Property(parameter, propertyInfo);

        if (!propertyInfo.PropertyType.IsValueType) return Expression.Lambda<Func<T, object>>(property, parameter);

        var convertedProperty = Expression.Convert(property, typeof(object));
        return Expression.Lambda<Func<T, object>>(convertedProperty, parameter);
    }
}