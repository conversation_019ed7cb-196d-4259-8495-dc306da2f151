using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Orion.SharedKernel.Infrastructure.Data.EFCore;
using Orion.SharedKernel.Infrastructure.Data.EFCore.Settings;
using Quartz;
using Reports.Domain.Configurations;
using Reports.Domain.Services;
using Reports.Infrastructure.Data.EFCore;
using Reports.Infrastructure.Data.EFCore.Repositories;
using Reports.Infrastructure.Data.Http;
using Reports.Infrastructure.Jobs;
using Reports.Infrastructure.Services;

namespace Reports.Infrastructure;

public static class ServicesExtensions
{
    public static IServiceCollection AddReportsInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddEFCorePostgre<ReportsDbContext>(configuration.GetConnectionString(Providers.PostgreSql)!);

        services.AddReportsRepositories();
        
        services.AddHttpClients(configuration);

        services.AddSynchronizationStrategies();

        services.AddQuartzServices(configuration);

        return services;
    }

    private static IServiceCollection AddQuartzServices(this IServiceCollection services, IConfiguration configuration)
    {
        var quartzConfig = new QuartzConfiguration();
        
        configuration
            .GetSection("Quartz")
            .Bind(quartzConfig);
        
        services.AddSingleton(quartzConfig);

        services.AddQuartz(q =>
        {
            var mainJobKey = new JobKey("MainSchedulerJob", "SchedulerGroup");
            q.AddJob<MainSchedulerJob>(opts => opts.WithIdentity(mainJobKey));

            q.AddTrigger(opts => opts
                .ForJob(mainJobKey)
                .WithIdentity("MainSchedulerTrigger", "SchedulerGroup")
                .WithCronSchedule($"0 */{quartzConfig.MainSchedulerIntervalMinutes} * ? * *")
                .StartNow());
        });

        services.AddQuartzHostedService(q => q.WaitForJobsToComplete = true);


        return services;
    }

    private static IServiceCollection AddSynchronizationStrategies(this IServiceCollection services)
    {
        services.AddScoped<IExternalTicketingSystem, LegacyApiClient>();

        return services;
    }
}