using AutoMapper;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Reports.Application.Services.Authentication;
using Reports.Application.Services.Http.Legacy.Request;
using Reports.Domain.Configurations;
using Reports.Domain.Entities;
using Reports.Domain.Services;

namespace Reports.Infrastructure.Services;

public class LegacyApiClient : IExternalTicketingSystem
{
    private readonly IAuthenticatedLegacyApiService _authenticatedApiService;
    private readonly IMapper _mapper;
    private readonly ILogger<LegacyApiClient> _logger;
    private readonly LegacyApiConfiguration _configuration;

    public LegacyApiClient(
        IAuthenticatedLegacyApiService authenticatedApiService,
        IMapper mapper,
        ILogger<LegacyApiClient> logger,
        IOptions<LegacyApiConfiguration> configuration)
    {
        _authenticatedApiService = authenticatedApiService ?? throw new ArgumentNullException(nameof(authenticatedApiService));
        _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _configuration = configuration?.Value ?? throw new ArgumentNullException(nameof(configuration));
    }

    public async Task<string?> GetExistingTicketInternalIdAsync(long weighingScaleId, CancellationToken cancellationToken = default)
    {
        try
        {
            var request = new GetUnloadingTicketsRequest()
                .Create(weighingScaleId.ToString(), _configuration.CompanyId);

            var tickets = await _authenticatedApiService.GetUnloadingTicketsAsync(request, cancellationToken);
            return tickets.FirstOrDefault()?.InternalUrbetrackId.ToString();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving existing ticket for WeighingScale {WeighingScaleId}", weighingScaleId);
            throw;
        }
    }

    public async Task CreateTicketAsync(WeighingScale weighingScale, CancellationToken cancellationToken = default)
    {
        try
        {
            var request = _mapper.Map<PostUnloadingTicketRequest>(weighingScale);
            await _authenticatedApiService.SendUnloadingTicketAsync(request, cancellationToken);
            
            _logger.LogDebug("Successfully sent ticket creation/update request for WeighingScale {WeighingScaleId}", weighingScale.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating or updating ticket for WeighingScale {WeighingScaleId}", weighingScale.Id);
            throw;
        }
    }

    public async Task DeleteTicketAsync(string internalId, CancellationToken cancellationToken = default)
    {
        try
        {
            var request = new DeleteUnloadingTicketRequest()
                .Create(_configuration.CompanyId, int.Parse(internalId));

            await _authenticatedApiService.DeleteUnloadingTicketAsync(request, cancellationToken);
            
            _logger.LogInformation("Successfully cancelled ticket with InternalId {InternalId}", internalId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting ticket with InternalId {InternalId}", internalId);
            throw;
        }
    }
}