using Common.Domain.Entities.Http.Orion.Request;
using Common.Domain.Entities.Http.Orion.Response;

namespace Common.Application.Services.Http.Orion
{
    public interface IOrionApiService
    {
        Task<IEnumerable<DomainValue>> GetPresentationTypes(CancellationToken cancellationToken);
        
        Task<IEnumerable<DomainValue>> GetMaterialGroupTypes(CancellationToken cancellationToken);
    }
}
