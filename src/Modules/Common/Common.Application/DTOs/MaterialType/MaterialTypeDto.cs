using System.Text.Json.Serialization;

namespace Common.Application.DTOs.MaterialType;

public class MaterialTypeDto
{
    /// <summary>
    /// Id del tipo de material.
    /// </summary>
    /// <example>Cartón corrugado</example>
    [JsonPropertyName("id")]
    public int Id { get; set; }
    
    /// <summary>
    /// Nombre del tipo de material.
    /// </summary>
    /// <example>Cartón corrugado</example>
    [JsonPropertyName("name")]
    public string Name { get; set; }
    
    /// <summary>
    /// Breve descripción del tipo de material.
    /// </summary>
    /// <example>Cartón simple de textura corrugada</example>
    [JsonPropertyName("description")]
    public string Description { get; set; }
    
    /// <summary>
    /// Id de Grupo del tipo de material.
    /// </summary>
    /// <example>23</example>
    [JsonPropertyName("groupId")]
    public int GroupId { get; set; }
    
    /// <summary>
    /// Grupo del tipo de material.
    /// </summary>
    /// <example>Cartón</example>
    [JsonPropertyName("group")]
    public string Group { get; set; }
}