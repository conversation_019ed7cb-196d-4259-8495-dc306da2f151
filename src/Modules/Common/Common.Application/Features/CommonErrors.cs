using Orion.SharedKernel.Domain.Entities.Error;
using Orion.SharedKernel.Domain.Entities.Loggers;
using System.Net;

namespace Common.Application.Features;

public class MaterialTypeAlreadyExists : Error
{
    public MaterialTypeAlreadyExists(string code)
    {
        Title = "Registro ya existente.";
        Messages = new List<string> { $"Ya existe un tipo de material con el nombre '{code}'." };
        Status = HttpStatusCode.Conflict;
        Platform = PlatformApp.Web;
        ErrorCode = LevelErrorCode.Information;
    }
}

public class MaterialTypeNotFound : Error
{
	public MaterialTypeNotFound(int id)
	{
		Title = "No se encontró el tipo de material.";
		Messages = new List<string>() { $"No se encontró el tipo de material con Id: '{id}'." };
		Status = HttpStatusCode.NotFound;
		Platform = PlatformApp.Web;
		ErrorCode = LevelErrorCode.Information;
	}
}

public class MaterialTypeGroupDoesNotExist : Error
{
    public MaterialTypeGroupDoesNotExist(int groupId)
    {
        Title = "Grupo de tipo de material no existe.";
        Messages = new List<string> { $"No existe un grupo de tipo de material con el id '{groupId}'." };
        Status = HttpStatusCode.Conflict;
        Platform = PlatformApp.Web;
        ErrorCode = LevelErrorCode.Information;
    }
}

