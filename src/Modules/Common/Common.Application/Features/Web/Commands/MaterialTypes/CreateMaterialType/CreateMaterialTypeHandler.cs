using AutoMapper;
using Common.Application.DTOs.MaterialType;
using Common.Application.Services.Http.Orion;
using Common.Domain.Entities.Http.Orion.Request;
using Orion.SharedKernel.Application.Common.Mapping;
using Orion.SharedKernel.Application.Common.Services;
using Orion.SharedKernel.Application.Exceptions;

namespace Common.Application.Features.Web.Commands.MaterialTypes.CreateMaterialType;

public class CreateMaterialTypeHandler : MappingService, IRequestHandler<CreateMaterialTypeRequest, CreateMaterialTypeResponse>
{
    private readonly ICommonUnitOfWork _commonUnitOfWork;
    private readonly IOrionApiService _orionApiService;

    public CreateMaterialTypeHandler(
        IMapper mapper,
        ICommonUnitOfWork commonUnitOfWork,
        IOrionApiService orionApiService
        ): base(mapper)
    {
        _commonUnitOfWork = commonUnitOfWork;
        _orionApiService = orionApiService;
    }

    public CreateMaterialTypeHandler(IMapper mapper, ICommonUnitOfWork commonUnitOfWork) : base(mapper)
    {
        _commonUnitOfWork = commonUnitOfWork;
    }

    public async Task<CreateMaterialTypeResponse> Handle(CreateMaterialTypeRequest request, CancellationToken cancellationToken)
    {
        var materialType = Mapper.Map<MaterialType>(request);
        
        await ValidateMaterialType(cancellationToken, materialType);

        var entity = await InsertAndRetrieveMaterialType(materialType, cancellationToken);
        
        var materialTypeDto = Mapper.Map<MaterialTypeDto>(entity);

        return new CreateMaterialTypeResponse(materialTypeDto);
    }

    private async Task ValidateMaterialType(CancellationToken cancellationToken, MaterialType materialType)
    {
        var existsMaterialType = await _commonUnitOfWork.MaterialTypeRepository
            .GetSingleAsync(mt => mt.Name.Equals(materialType.Name),
                cancellationToken);
        
        if (existsMaterialType is not null)
            throw new OrionException(_commonUnitOfWork
                .ErrorService
                .GenerateError(
                    new MaterialTypeAlreadyExists(materialType.Name))
            );

        var existentMaterialGroups = await _orionApiService.GetMaterialGroupTypes(cancellationToken);
        
        if (!existentMaterialGroups.Any(x => x.Id == materialType.GroupId))
            throw new OrionException(_commonUnitOfWork
                .ErrorService
                .GenerateError(
                    new MaterialTypeGroupDoesNotExist(materialType.GroupId))
            );
    }
    
    private async Task<MaterialType> InsertAndRetrieveMaterialType(MaterialType materialType, CancellationToken cancellationToken)
    {
        await _commonUnitOfWork.MaterialTypeRepository.AddAsync(materialType, cancellationToken);
        await _commonUnitOfWork.SaveChangesAsync(cancellationToken);

        return materialType;
    }
}