using Common.Application.Services.Http.Orion;
using Common.Domain.Entities.Http.Orion.Request;
using Orion.SharedKernel.Application.Common.Services;
using Orion.SharedKernel.Application.Exceptions;

namespace Common.Application.Features.Web.Commands.MaterialTypes.UpdateMaterialType;

internal sealed class UpdateMaterialTypeHandler : IRequestHandler<UpdateMaterialTypeRequest>
{
    private readonly ICommonUnitOfWork _unitOfWork;
    private readonly IOrionApiService _orionApiService;

    public UpdateMaterialTypeHandler(ICommonUnitOfWork unitOfWork,
        IOrionApiService orionApiService)
    {
        _unitOfWork = unitOfWork;
        _orionApiService = orionApiService;
    }
    public UpdateMaterialTypeHandler(ICommonUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    public async Task<Unit> Handle(UpdateMaterialTypeRequest request, CancellationToken cancellationToken)
    {
        MaterialType materialType = await GetMaterialType(request);

        await CheckExistsDomainValue(request, cancellationToken);

        await UpdateMaterialType(request, materialType, cancellationToken);

        return Unit.Value;
    }

    private async Task UpdateMaterialType(UpdateMaterialTypeRequest request, MaterialType materialType, CancellationToken cancellationToken)
    {
        materialType.Update(request.Name, request.Description, request.GroupId);

        _unitOfWork.MaterialTypeRepository.Update(materialType);

        await _unitOfWork.SaveChangesAsync(cancellationToken);
    }

    private async Task<MaterialType> GetMaterialType(UpdateMaterialTypeRequest request)
    {
        return await _unitOfWork
            .MaterialTypeRepository
            .GetByIdAsync(request.Id)
        ?? throw new OrionException(new MaterialTypeNotFound(request.Id));
    }

    private async Task CheckExistsDomainValue(UpdateMaterialTypeRequest request, CancellationToken cancellationToken)
    {
        var existentMaterialGroups = await _orionApiService.GetMaterialGroupTypes(cancellationToken);
        
        if (!existentMaterialGroups.Any(x => x.Id == request.GroupId))
            throw new OrionException(new MaterialTypeGroupDoesNotExist(request.GroupId));
    }
}