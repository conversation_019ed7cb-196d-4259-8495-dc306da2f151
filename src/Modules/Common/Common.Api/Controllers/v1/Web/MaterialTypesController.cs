using Common.Application.DTOs.MaterialType;
using Common.Application.Features.Web.Commands.MaterialTypes.CreateMaterialType;
using Common.Application.Features.Web.Commands.MaterialTypes.DeleteMaterialType;
using Common.Application.Features.Web.Commands.MaterialTypes.UpdateMaterialType;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Orion.SharedKernel.Api.Controllers;
using Orion.SharedKernel.Domain.Entities.Error;
using System.Net;

namespace Common.Api.Controllers.v1.Web;

[ApiVersion("1.0")]
[Authorize]
public class MaterialTypesController : OrionController
{
    /// <summary>
    /// Crea un tipo de material.
    /// </summary>
    /// <param name="request"></param>
    [ProducesResponseType((int)HttpStatusCode.Created, Type = typeof(MaterialTypeDto))]
    [ProducesResponseType((int)HttpStatusCode.Conflict, Type = typeof(Error))]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError, Type = typeof(Error))]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [HttpPost]
    public async Task<IActionResult> CreateMaterialType(CreateMaterialTypeRequest request)
    {
        var response = await Mediator.Send<CreateMaterialTypeResponse>(request);

        return Created(string.Empty, response.MaterialType);
    }

	/// <summary>
	/// Actualiza un tipo de material.
	/// </summary>
	[ProducesResponseType((int)HttpStatusCode.NoContent)]
	[ProducesResponseType((int)HttpStatusCode.BadRequest, Type = typeof(Error))]
	[ProducesResponseType((int)HttpStatusCode.Conflict, Type = typeof(Error))]
	[ProducesResponseType((int)HttpStatusCode.InternalServerError, Type = typeof(Error))]
	[ProducesResponseType((int)HttpStatusCode.Unauthorized)]
	[HttpPatch("{id}")]
	public async Task<IActionResult> UpdateMaterialType([FromRoute] int id, [FromBody] UpdateMaterialTypeRequest request)
	{
        request.Id = id;

		_ = await Mediator.Send(request);

		return NoContent();
	}

	/// <summary>
	/// Elimina un tipo de material.
	/// </summary>
	[ProducesResponseType((int)HttpStatusCode.NoContent)]
	[ProducesResponseType((int)HttpStatusCode.BadRequest, Type = typeof(Error))]
	[ProducesResponseType((int)HttpStatusCode.Conflict, Type = typeof(Error))]
	[ProducesResponseType((int)HttpStatusCode.InternalServerError, Type = typeof(Error))]
	[ProducesResponseType((int)HttpStatusCode.Unauthorized)]
	[HttpDelete("{id}")]
	public async Task<IActionResult> UpdateMaterialType([FromRoute] int id)
	{
		_ = await Mediator.Send(new DeleteMaterialTypeRequest() { Id = id});

		return NoContent();
	}
}