using Common.Domain;
using Common.Domain.Repositories;
using Microsoft.Extensions.DependencyInjection;

namespace Common.Infrastructure.Data.EFCore.Repositories;

public static class ServicesExtensions
{
    public static IServiceCollection AddCommonRepositories(this IServiceCollection services)
    {
        services.AddScoped<ICommonUnitOfWork, CommonUnitOfWork>();

        #region Repositories

        services.AddScoped<IMaterialTypeRepository, MaterialTypeRepository>();

        #endregion Repositories

        return services;
    }
}