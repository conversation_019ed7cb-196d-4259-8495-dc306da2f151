using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Common.Infrastructure.Data.EFCore.Migrations
{
    public partial class Adds_Audit_For_MaterialType : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "RECYECA01_CreadoPor",
                table: "Recycling-ECA-01_Materiales",
                type: "character varying(60)",
                maxLength: 60,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<bool>(
                name: "RECYECA01_Eliminado",
                table: "Recycling-ECA-01_Materiales",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "RECYECA01_EliminadoPor",
                table: "Recycling-ECA-01_Materiales",
                type: "character varying(60)",
                maxLength: 60,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "RECYECA01_FechaDeCreacion",
                table: "Recycling-ECA-01_Materiales",
                type: "timestamp without time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<DateTime>(
                name: "RECYECA01_FechaDeEliminacion",
                table: "Recycling-ECA-01_Materiales",
                type: "timestamp without time zone",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "RECYECA01_FechaDeModificacion",
                table: "Recycling-ECA-01_Materiales",
                type: "timestamp without time zone",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "RECYECA01_ModificadoPor",
                table: "Recycling-ECA-01_Materiales",
                type: "character varying(60)",
                maxLength: 60,
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "RECYECA01_CreadoPor",
                table: "Recycling-ECA-01_Materiales");

            migrationBuilder.DropColumn(
                name: "RECYECA01_Eliminado",
                table: "Recycling-ECA-01_Materiales");

            migrationBuilder.DropColumn(
                name: "RECYECA01_EliminadoPor",
                table: "Recycling-ECA-01_Materiales");

            migrationBuilder.DropColumn(
                name: "RECYECA01_FechaDeCreacion",
                table: "Recycling-ECA-01_Materiales");

            migrationBuilder.DropColumn(
                name: "RECYECA01_FechaDeEliminacion",
                table: "Recycling-ECA-01_Materiales");

            migrationBuilder.DropColumn(
                name: "RECYECA01_FechaDeModificacion",
                table: "Recycling-ECA-01_Materiales");

            migrationBuilder.DropColumn(
                name: "RECYECA01_ModificadoPor",
                table: "Recycling-ECA-01_Materiales");
        }
    }
}
