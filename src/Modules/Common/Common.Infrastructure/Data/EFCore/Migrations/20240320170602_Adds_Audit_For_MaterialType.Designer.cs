// <auto-generated />
using System;
using Common.Infrastructure.Data.EFCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Common.Infrastructure.Data.EFCore.Migrations
{
    [DbContext(typeof(CommonDbContext))]
    [Migration("20240320170602_Adds_Audit_For_MaterialType")]
    partial class Adds_Audit_For_MaterialType
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "6.0.22")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("Common.Domain.Entities.MaterialType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("RECYECA01_Id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(60)
                        .HasColumnType("character varying(60)")
                        .HasColumnName("RECYECA01_CreadoPor");

                    b.Property<DateTime>("CreationDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("RECYECA01_FechaDeCreacion");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(60)
                        .HasColumnType("character varying(60)")
                        .HasColumnName("RECYECA01_EliminadoPor");

                    b.Property<DateTime?>("DeletionDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("RECYECA01_FechaDeEliminacion");

                    b.Property<string>("Description")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("RECYECA01_Descripcion");

                    b.Property<int>("GroupId")
                        .HasColumnType("integer")
                        .HasColumnName("RECYECA01_Grupo");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("RECYECA01_Eliminado");

                    b.Property<DateTime?>("LastModificationDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("RECYECA01_FechaDeModificacion");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(60)
                        .HasColumnType("character varying(60)")
                        .HasColumnName("RECYECA01_ModificadoPor");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("RECYECA01_Nombre");

                    b.HasKey("Id")
                        .HasName("Recycling-ECA-01_Materiales_key");

                    b.HasIndex("GroupId");

                    b.ToTable("Recycling-ECA-01_Materiales", (string)null);
                });

            modelBuilder.Entity("Orion.Core.DDV.Domain.Entities.Domain", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("Description")
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int?>("ParentDomainId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("Orion-Domain", (string)null);
                });

            modelBuilder.Entity("Orion.Core.DDV.Domain.Entities.DomainValue", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Description")
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)");

                    b.Property<int>("DomainId")
                        .HasColumnType("integer");

                    b.Property<int?>("Order")
                        .HasColumnType("integer");

                    b.Property<int?>("ParentDomainValueId")
                        .HasColumnType("integer");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.HasKey("Id");

                    b.HasIndex("DomainId");

                    b.ToTable("Orion-DomainValue", (string)null);
                });

            modelBuilder.Entity("Common.Domain.Entities.MaterialType", b =>
                {
                    b.HasOne("Orion.Core.DDV.Domain.Entities.DomainValue", "Group")
                        .WithMany()
                        .HasForeignKey("GroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("Recycling-ECA_RECYECA01-OrionDomainValue_fkey");

                    b.Navigation("Group");
                });

            modelBuilder.Entity("Orion.Core.DDV.Domain.Entities.DomainValue", b =>
                {
                    b.HasOne("Orion.Core.DDV.Domain.Entities.Domain", "Domain")
                        .WithMany("Values")
                        .HasForeignKey("DomainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Domain");
                });

            modelBuilder.Entity("Orion.Core.DDV.Domain.Entities.Domain", b =>
                {
                    b.Navigation("Values");
                });
#pragma warning restore 612, 618
        }
    }
}
