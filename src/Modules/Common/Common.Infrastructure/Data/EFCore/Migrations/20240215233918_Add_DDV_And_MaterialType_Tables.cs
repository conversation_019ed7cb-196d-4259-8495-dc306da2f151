using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Common.Infrastructure.Data.EFCore.Migrations
{
    public partial class Add_DDV_And_MaterialType_Tables : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Orion-Domain",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Code = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    Description = table.Column<string>(type: "character varying(250)", maxLength: 250, nullable: true),
                    ParentDomainId = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Orion-Domain", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Orion-DomainValue",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Value = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Code = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    Description = table.Column<string>(type: "character varying(250)", maxLength: 250, nullable: true),
                    Order = table.Column<int>(type: "integer", nullable: true),
                    ParentDomainValueId = table.Column<int>(type: "integer", nullable: true),
                    DomainId = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Orion-DomainValue", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Orion-DomainValue_Orion-Domain_DomainId",
                        column: x => x.DomainId,
                        principalTable: "Orion-Domain",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Recycling-ECA-01_Materiales",
                columns: table => new
                {
                    RECYECA01_Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    RECYECA01_Nombre = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    RECYECA01_Descripcion = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    RECYECA01_Grupo = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("Recycling-ECA-01_Materiales_key", x => x.RECYECA01_Id);
                    table.ForeignKey(
                        name: "Recycling-ECA_RECYECA01-OrionDomainValue_fkey",
                        column: x => x.RECYECA01_Grupo,
                        principalTable: "Orion-DomainValue",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Orion-DomainValue_DomainId",
                table: "Orion-DomainValue",
                column: "DomainId");

            migrationBuilder.CreateIndex(
                name: "IX_Recycling-ECA-01_Materiales_RECYECA01_Grupo",
                table: "Recycling-ECA-01_Materiales",
                column: "RECYECA01_Grupo");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Recycling-ECA-01_Materiales");

            migrationBuilder.DropTable(
                name: "Orion-DomainValue");

            migrationBuilder.DropTable(
                name: "Orion-Domain");
        }
    }
}
