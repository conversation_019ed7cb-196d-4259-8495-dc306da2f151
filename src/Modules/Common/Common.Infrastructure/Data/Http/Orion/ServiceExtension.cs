using Common.Application.Services;
using Common.Application.Services.Http.Orion;
using Common.Domain.Configuration;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Common.Infrastructure.Data.Http.Orion
{
    public static class ServiceExtension
    {
        public static IServiceCollection AddOrionHttpClient(this IServiceCollection services, IConfiguration configuration)
        {
            var apiConfiguration = configuration.GetSection(nameof(OrionApiConfiguration)).Get<OrionApiConfiguration>();

            services.AddHttpClient<IOrionApiService, OrionApiService>(httpClient =>
            {
                httpClient.BaseAddress = new Uri(apiConfiguration!.UrlBase);
                httpClient.Timeout = TimeSpan.FromMinutes(1);
                httpClient.DefaultRequestHeaders.Add("X-Tenant", configuration.GetSection("AppName").Value);
            });

            services.AddHttpClient<IJobAuthenticationService, JobAuthenticationService>(httpClient =>
            {
                httpClient.BaseAddress = new Uri(apiConfiguration!.UrlBase);
                httpClient.Timeout = TimeSpan.FromMinutes(1);
                httpClient.DefaultRequestHeaders.Add("X-Tenant", configuration.GetSection("AppName").Value);
            });

            return services;
        }
    }
}
