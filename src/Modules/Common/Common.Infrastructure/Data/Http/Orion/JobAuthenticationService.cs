using Common.Application.Services;
using Common.Domain.Configuration;
using Common.Domain.Entities.Http.Orion.Request;
using Common.Domain.Entities.Http.Orion.Response;
using Microsoft.Extensions.Options;
using Orion.SharedKernel.Application.Common.Services;
using Orion.SharedKernel.Infrastructure.Data.Http;
using System.Text;
using System.Text.Json;

namespace Common.Infrastructure.Data.Http.Orion
{
    public class JobAuthenticationService : ApiService, IJobAuthenticationService
    {
        private readonly OrionApiConfiguration _configuration;
        public JobAuthenticationService(IOptions<OrionApiConfiguration> options, ICacheService cacheService, HttpClient httpClient) : base(cacheService, httpClient)
        {
            _configuration = options.Value;
        }

        public async Task<AuthenticationResponse> AuthenticateAsync()
        {
            var request = new AuthenticationRequest(_configuration.SyncronizationLoginUser,
                _configuration.SyncronizationLoginPassword);

            var endpoint = request.ToRoute(_configuration.AuthenticationEndpoint);

            var headers = request.ToHeader();

            var response = await Post<AuthenticationResponse>(endpoint: endpoint, body: request, headersParams: headers);

            return response;
        }
    }
}