using Common.Infrastructure.Data.EFCore;
using Common.Infrastructure.Data.EFCore.Repositories;
using Common.Infrastructure.Data.Http.Orion;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Orion.SharedKernel.Infrastructure.Data.EFCore;
using Orion.SharedKernel.Infrastructure.Data.EFCore.Settings;

namespace Common.Infrastructure;

public static class ServicesExtensions
{
    public static IServiceCollection AddCommonInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddOrionHttpClient(configuration);

        services.AddEFCorePostgre<CommonDbContext>(configuration.GetConnectionString(Providers.PostgreSql)!);

        services.AddCommonRepositories();

        return services;
    }
}