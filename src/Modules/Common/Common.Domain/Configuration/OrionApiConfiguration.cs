using Orion.SharedKernel.Domain.Configurations;

namespace Common.Domain.Configuration
{
    public class OrionApiConfiguration : ApiConfiguration
    {
        public string GetDomainValuesByCode { get; set; } = null!;
        public string MaterialGroupTypeDDVCode { get; set; } = null!;
        public string MaterialPresentationDDVCode { get; set; } = null!;
        public string SyncronizationLoginUser { get; set; } = null!;
        public string SyncronizationLoginPassword { get; set; } = null!;
        public string AuthenticationEndpoint { get; set; } = null!;
    }
}
