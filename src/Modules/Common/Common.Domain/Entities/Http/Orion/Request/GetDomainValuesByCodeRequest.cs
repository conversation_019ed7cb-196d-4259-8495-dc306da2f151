using Common.Domain.Entities.Http.Legacy.Request;
using Orion.SharedKernel.Domain.Entities.Http;

namespace Common.Domain.Entities.Http.Orion.Request
{
    public class GetDomainValuesByCodeRequest : CommonRequest
    {
        [Route(position: 0)]
        public string Code { get; set; }

        public GetDomainValuesByCodeRequest(string code, string authorization)
        {
            Code = code;
            Authorization = authorization;
        }
    }
}
