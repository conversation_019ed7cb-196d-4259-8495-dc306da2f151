using Common.Domain.Entities.Http.Legacy.Request;
using Orion.SharedKernel.Domain.Entities.Http;

namespace Common.Domain.Entities.Http.Orion.Request
{
    public class GetDomainValuesByIdsRequest : CommonRequest
    {
        [Query]
        public string Ids { get; set; } = null!;

        public GetDomainValuesByIdsRequest(string ids, string authorization)
        {
            Ids = ids;
            Authorization = authorization;
        }
    }
}
