using Orion.SharedKernel.Domain.Entities.Http;

namespace Common.Domain.Entities.Http.Orion.Request
{
    public class AuthenticationRequest : ApiRequestModelBase
    {
        public string User { get; set; } = null!;
        public string Password { get; set; } = null!;
        
        public AuthenticationRequest(string user, string password)
        {
            User = user;
            Password = password;
        }
    }
}