using Newtonsoft.Json;
using Orion.SharedKernel.Domain.Entities.Http;

namespace Common.Domain.Entities.Http.Orion.Response
{
    public class AuthenticationResponse : SerializableModel
    {
        [JsonProperty("token")]
        public string Token { get; set; } = null!;
        
        [JsonProperty("legacyToken")]
        public string LegacyToken { get; set; } = null!;
        
        [JsonProperty("refreshToken")]
        public string RefreshToken { get; set; } = null!;
    }
}