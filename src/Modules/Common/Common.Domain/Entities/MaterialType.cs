using Common.Domain.Entities.Http.Orion.Response;
using Orion.SharedKernel.Domain.Entities.Audit;
using Orion.SharedKernel.Domain.Entities.Audit.DeletionAudit;
using Orion.SharedKernel.Domain.Utils;

namespace Common.Domain.Entities;

public class MaterialType : AuditableEntity<int>, ISoftDeletedEntity
{
    public string Name { get; set; }
    public string Description { get; set; }
	public bool IsDeleted { get; set; }

    public int GroupId { get; set; }
    public DomainValue Group { get; set; }

    public void Update(string name, string description, int groupId)
    {
		Name = name is { Length: > 0 } ? name : Name;
		Description = description is { Length: > 0 } ? description : Description;
        GroupId = DomainUtils.UpdatePropertyIfChanged(groupId, GroupId, newValue => groupId > 0, false, out _);
    }

    public void Delete()
    {
        IsDeleted = true;
    }
}