using Classification.Domain.Repositories;
using Orion.SharedKernel.Domain.Repositories.Entities;
using Orion.SharedKernel.Domain.Services;

namespace Classification.Domain;

public interface IClassificationUnitOfWork : IUnitOfWork
{
    ISackRepository SackRepository { get; }
    
    IMaterialEntryRepository MaterialEntryRepository { get; }
    
    IMaterialClassificationRepository MaterialClassificationRepository { get; }

    IMaterialClassificationDetailRepository MaterialClassificationDetailRepository { get; }
    
    IErrorService ErrorService { get; }
    
    ILogEventMessage LogEventMessage { get; }
}