using Classification.Domain.Constants;
using Orion.SharedKernel.Domain.Entities.Audit;
using Orion.SharedKernel.Domain.Entities.Audit.DeletionAudit;

namespace Classification.Domain.Entities;

public class MaterialEntry : AuditableEntity<int>, ISoftDeletedEntity
{
    public DateTime Date { get; set; }
    public string Responsible { get; set; }
    public string LicensePlate { get; set; }
    public string Route { get; set; }
    public OriginType Origin { get; set; }
	public bool IsDeleted { get; set; }

    public IEnumerable<MaterialEntryDetail> Details { get; set; }
	
    public void UpdateDetail(int materialEntryDetailId, string client)
    {
        MaterialEntryDetail? detailToUpdate = Details
            .FirstOrDefault(d => d.Id == materialEntryDetailId);

        if(detailToUpdate is not null)
        {
            detailToUpdate.Client = client;
        }
    }

	public void Delete()
		=> IsDeleted = true;
}