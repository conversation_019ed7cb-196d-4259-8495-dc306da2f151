using Orion.SharedKernel.Domain.Entities;
using Orion.SharedKernel.Domain.Entities.Audit.DeletionAudit;

namespace Classification.Domain.Entities;

public class MaterialEntryDetail : Entity<int>, ISoftDeletedEntity
{
    public string Client { get; set; }
    public int MaterialEntryId { get; set; }
    public MaterialEntry MaterialEntry { get; set; }
	public bool IsDeleted { get; set; }

    public IEnumerable<Sack> Sacks { get; set; }
    
    public int? MaterialClassificationId {  get; set; }
    public MaterialClassification MaterialClassification {  get; set; }
}