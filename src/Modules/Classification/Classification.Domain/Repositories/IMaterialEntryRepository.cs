using System.Linq.Expressions;
using Classification.Domain.Entities;
using Orion.SharedKernel.Domain.Repositories.Entities;

namespace Classification.Domain.Repositories;

public interface IMaterialEntryRepository : IRepository<MaterialEntry, int>
{
    Task<MaterialEntry?> GetSingleWithIncludesAsync(Expression<Func<MaterialEntry, bool>> predicate, CancellationToken cancellationToken);
    
    Task<List<MaterialEntry>> GetAllWithIncludesAsync(Expression<Func<MaterialEntry, bool>> predicate,
        CancellationToken cancellationToken);
}