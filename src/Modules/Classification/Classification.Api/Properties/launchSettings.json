{"$schema": "https://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:43932", "sslPort": 44308}}, "profiles": {"Classification.Api": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": false, "launchUrl": "scalar", "applicationUrl": "https://localhost:7086;http://localhost:5077", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": false, "launchUrl": "scalar", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}}}