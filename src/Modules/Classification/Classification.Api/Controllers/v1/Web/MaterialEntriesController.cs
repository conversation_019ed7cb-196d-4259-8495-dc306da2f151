using System.Net;
using Classification.Application.DTOs.MaterialEntry;
using Classification.Application.Features.Web.Commands.MaterialEntries.CreateMaterialEntry;
using Classification.Application.Features.Web.Commands.MaterialEntries.DeleteMaterialEntry;
using Classification.Application.Features.Web.Commands.MaterialEntries.UpdateMaterialEntry;
using Classification.Application.Features.Web.Queries.GetMaterialEntry.MaterialEntries;
using Classification.Application.Features.Web.Queries.MaterialEntries.GetMaterialEntries;
using Classification.Application.Features.Web.Queries.MaterialEntries.GetWeeklyMaterialEntries;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Orion.SharedKernel.Api.Controllers;
using Orion.SharedKernel.Domain.Entities.Error;

namespace Classification.Api.Controllers.v1.Web;

[ApiVersion("1.0")]
[Authorize]
public class MaterialEntriesController : OrionController
{
    /// <summary>
    /// Crea un ingreso de material.
    /// </summary>
    [ProducesResponseType((int)HttpStatusCode.Created, Type = typeof(MaterialEntryResponseDto))]
    [ProducesResponseType((int)HttpStatusCode.Conflict, Type = typeof(Error))]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError, Type = typeof(Error))]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [HttpPost]
    public async Task<IActionResult> Create(MaterialEntryRequestDto requestDto)
    {
        var request = new CreateMaterialEntryRequest(requestDto);
        
        var response = await Mediator.Send<CreateMaterialEntryResponse>(request);

        return Created(string.Empty, response.MaterialEntryResponse);
    }
    
    /// <summary>
    /// Obtiene un ingreso de material por Id.
    /// </summary>
    /// <param name="id"></param>
    [ProducesResponseType((int)HttpStatusCode.OK, Type = typeof(MaterialEntryResponseDto))]
    [ProducesResponseType((int)HttpStatusCode.NoContent)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError, Type = typeof(Error))]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [HttpGet("{id:int}")]
    public async Task<IActionResult> GetById(int id)
    {
        var request = new GetMaterialEntryRequest(id);
        
        var response = await Mediator.Send<GetMaterialEntryResponse>(request);

        return Ok(response.MaterialEntryResponse);
    }
    
    /// <summary>
    /// Obtiene los ingresos segun los filtros ingresados de forma paginada.
    /// </summary>
    /// <param name="request"></param>
    [ProducesResponseType((int)HttpStatusCode.OK, Type = typeof(IEnumerable<MaterialEntryResponseDto>))]
    [ProducesResponseType((int)HttpStatusCode.NoContent)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError, Type = typeof(Error))]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [HttpGet]
    public async Task<IActionResult> Get([FromQuery] GetMaterialEntriesRequest request)
    {
        var response = await Mediator.Send<GetMaterialEntriesResponse>(request);

        if (!response.MaterialEntries.Any()) return NoContent();

        return Ok(response.MaterialEntries);
    }
    
    /// <summary>
    /// Obtienen los ingresos de la ultima semana laboral.
    /// </summary>
    /// <param name="request"></param>
    [ProducesResponseType((int)HttpStatusCode.OK, Type = typeof(List<MaterialEntryResponseDto>))]
    [ProducesResponseType((int)HttpStatusCode.NoContent)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError, Type = typeof(Error))]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [HttpGet("weekly")]
    public async Task<IActionResult> GetWeekly([FromQuery] GetWeeklyMaterialEntriesRequest request)
    {
        var response = await Mediator.Send<GetWeeklyMaterialEntriesResponse>(request);
        
        if (!response.MaterialEntries.Any()) return NoContent();

        return Ok(response.MaterialEntries);
    }

	/// <summary>
	/// Actualiza un ingreso de material.
	/// </summary>
	[ProducesResponseType((int)HttpStatusCode.NoContent)]
	[ProducesResponseType((int)HttpStatusCode.BadRequest, Type = typeof(Error))]
	[ProducesResponseType((int)HttpStatusCode.Unauthorized)]
	[ProducesResponseType((int)HttpStatusCode.InternalServerError, Type = typeof(Error))]
	[HttpPatch("{id}")]
	public async Task<IActionResult> UpdateMaterialEntry([FromRoute] int id, [FromBody] UpdateMaterialEntryRequest request)
	{
        request.Id = id;

		_ = await Mediator.Send(request);

        return NoContent();
	}

    ///<summary>
    /// Baja de ingreso de material
    /// </summary>
    [ProducesResponseType((int)HttpStatusCode.NoContent)]

    [ProducesResponseType((int)HttpStatusCode.NotFound, Type = typeof(Error))]

    [ProducesResponseType((int)HttpStatusCode.BadRequest, Type = typeof(Error))]

    [ProducesResponseType((int)HttpStatusCode.Conflict, Type = typeof(Error))]

    [ProducesResponseType((int)HttpStatusCode.Unauthorized, Type = typeof(Error))]

    [ProducesResponseType((int)HttpStatusCode.InternalServerError, Type = typeof(Error))]
    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete([FromRoute] DeleteMaterialEntryRequest request) { 
        
        await Mediator.Send(request);

        return NoContent();
    }
}