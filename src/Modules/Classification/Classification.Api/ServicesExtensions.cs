using Classification.Application;
using Classification.Infrastructure;

namespace Classification.Api;

public static class ServicesExtensions
{
    public static IServiceCollection AddClassificationModule(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddClassificationInfrastructure(configuration);

        services.AddClassificationApplication();

        return services;
    }
}