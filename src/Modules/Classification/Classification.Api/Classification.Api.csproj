<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
      <DocumentationFile>obj\Debug\net6.0\Classification.Api.xml</DocumentationFile>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
      <DocumentationFile>obj\Release\net6.0\Classification.Api.xml</DocumentationFile>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\Common\Common.Api\Common.Api.csproj" />
      <ProjectReference Include="..\Classification.Application\Classification.Application.csproj" />
      <ProjectReference Include="..\Classification.Infrastructure\Classification.Infrastructure.csproj" />
    </ItemGroup>

</Project>
