using System.Text.Json.Serialization;

namespace Classification.Application.DTOs.MaterialEntry;

public class MaterialEntryDetailResponseDto
{
    /// <summary>
    /// Identificador del detalle registrado en el ingreso de material.
    /// </summary>
    /// <example>1</example>
    [JsonPropertyName("id")]
    public int Id { get; set; }
    
    /// <summary>
    /// Cliente al que corresponde el ingreso.
    /// </summary>
    /// <example><PERSON></example>
    [JsonPropertyName("client")]
    public string? Client { get; set; }
    
    /// <summary>
    /// Listado de bolsones del detalle a ingresar divididos por peso y presentación.
    /// </summary>
    /// <example cref="SackResponseDto">SackResponseDto</example>
    [JsonPropertyName("sacks")]
    public IEnumerable<SackResponseDto> Sacks { get; set; }
}