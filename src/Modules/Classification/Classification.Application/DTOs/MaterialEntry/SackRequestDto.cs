using System.Text.Json.Serialization;

namespace Classification.Application.DTOs.MaterialEntry;

public class SackRequestDto
{
    /// <summary>
    /// Cantidad representada.
    /// </summary>
    /// <remarks>Valor de la presentación del bolson.</remarks>
    /// <example>18.21</example>
    [JsonPropertyName("quantity")]
    public double? Quantity { get; set; }
    
    /// <summary>
    /// Identificador del tipo de presentación.
    /// </summary>
    /// <remarks>Debe ser uno ya existente.</remarks>
    /// <example>44</example>
    [JsonPropertyName("presentationId")]
    public int? PresentationId { get; set; }
}