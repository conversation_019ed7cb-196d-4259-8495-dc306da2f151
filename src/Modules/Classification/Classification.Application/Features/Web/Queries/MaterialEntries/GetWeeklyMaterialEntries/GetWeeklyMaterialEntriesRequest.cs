using MediatR;

namespace Classification.Application.Features.Web.Queries.MaterialEntries.GetWeeklyMaterialEntries;

public record GetWeeklyMaterialEntriesRequest : IRequest<GetWeeklyMaterialEntriesResponse>
{
    /// <summary>
    /// Placa del vehículo
    /// </summary>
    /// <example>ABC123</example>
    public string? LicensePlate { get; set; }
    
    /// <summary>
    /// Nombre del cliente
    /// </summary>
    /// <example><PERSON></example>
    public string? ClientName { get; set; }
    
    /// <summary>
    /// Ruta
    /// </summary>
    /// <example>1</example>
    public string? Route { get; set; }
}