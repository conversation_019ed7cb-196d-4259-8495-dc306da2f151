using FluentValidation;

namespace Classification.Application.Features.Web.Queries.GetMaterialEntry.MaterialEntries;

public class GetMaterialEntryValidator : AbstractValidator<GetMaterialEntryRequest>
{
    public GetMaterialEntryValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty()
            .WithMessage("El identificador de la entrada de material no puede estar vacío.")
            .GreaterThanOrEqualTo(0)
            .WithMessage("El identificador de la entrada de material debe ser mayor o igual a 0.");
    }
}