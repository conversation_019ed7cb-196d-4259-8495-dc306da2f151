using Classification.Application.Common.Extensions;
using Classification.Application.DTOs.MaterialEntry;
using Classification.Domain.Entities;
using Orion.SharedKernel.Application.Common.Mapping;
using System.Linq.Expressions;

namespace Classification.Application.Features.Web.Queries.MaterialEntries.GetWeeklyMaterialEntries;

internal sealed class GetWeeklyMaterialEntriesHandler : MappingService, IRequestHandler<GetWeeklyMaterialEntriesRequest, GetWeeklyMaterialEntriesResponse>
{
    private readonly IClassificationUnitOfWork _classificationUnitOfWork;

    public GetWeeklyMaterialEntriesHandler(IMapper mapper, IClassificationUnitOfWork classificationUnitOfWork) : base(mapper)
    {
        _classificationUnitOfWork = classificationUnitOfWork;
    }

    public async Task<GetWeeklyMaterialEntriesResponse> Handle(GetWeeklyMaterialEntriesRequest request, CancellationToken cancellationToken)
    {
        var filters = GetPredicate(request);

        var entities = await _classificationUnitOfWork.MaterialEntryRepository
                .GetAllWithIncludesAsync(
                    predicate: filters,
                    cancellationToken: cancellationToken
                );

        var materialEntryDtos = Mapper.Map<IEnumerable<MaterialEntryResponseDto>>(entities);

        return new GetWeeklyMaterialEntriesResponse(materialEntryDtos);
    }

    private static Expression<Func<MaterialEntry, bool>> GetPredicate(GetWeeklyMaterialEntriesRequest request)
    {
        var startOfWeek = DateTime.Now.StartOfWeek();
        var endOfWeek = DateTime.Now.EndOfWeek();

        return me =>
        me.Date >= startOfWeek
        && me.Date <= endOfWeek
        && (string.IsNullOrEmpty(request.LicensePlate) || me.LicensePlate.Equals(request.LicensePlate))
        && (string.IsNullOrEmpty(request.Route) || request.Route.Equals(request.Route))
        && (string.IsNullOrEmpty(request.ClientName) || me.Details.Any(d => d.Client.ToLowerInvariant().Equals(request.ClientName.ToLowerInvariant())));
    }
}