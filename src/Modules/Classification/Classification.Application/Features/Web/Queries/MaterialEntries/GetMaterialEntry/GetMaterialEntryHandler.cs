using AutoMapper;
using Classification.Application.DTOs.MaterialEntry;
using Classification.Domain;
using MediatR;
using Orion.SharedKernel.Application.Common.Mapping;

namespace Classification.Application.Features.Web.Queries.GetMaterialEntry.MaterialEntries;

internal sealed class GetMaterialEntryHandler : MappingService, IRequestHandler<GetMaterialEntryRequest, GetMaterialEntryResponse>
{
    private readonly IClassificationUnitOfWork _classificationUnitOfWork;
    
    public GetMaterialEntryHandler(IMapper mapper, IClassificationUnitOfWork classificationUnitOfWork) : base(mapper)
    {
        _classificationUnitOfWork = classificationUnitOfWork;
    }
    
    public async Task<GetMaterialEntryResponse> Handle(GetMaterialEntryRequest request, CancellationToken cancellationToken)
    {
        var entity = await _classificationUnitOfWork.MaterialEntryRepository
            .GetSingleWithIncludesAsync(
            predicate: me => me.Id == request.Id,
            cancellationToken: cancellationToken
        );
        
        var materialEntryDto = Mapper.Map<MaterialEntryResponseDto>(entity);

        return new GetMaterialEntryResponse(materialEntryDto);
    }
}