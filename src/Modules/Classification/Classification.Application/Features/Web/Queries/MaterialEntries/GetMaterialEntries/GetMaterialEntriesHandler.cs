using Classification.Application.DTOs.MaterialEntry;
using Classification.Domain.Entities;
using Orion.SharedKernel.Application.Common.Mapping;
using System.Linq.Expressions;

namespace Classification.Application.Features.Web.Queries.MaterialEntries.GetMaterialEntries;

internal sealed class GetMaterialEntriesHandler : MappingService, IRequestHandler<GetMaterialEntriesRequest, GetMaterialEntriesResponse>
{
    private readonly IClassificationUnitOfWork _classificationUnitOfWork;

    public GetMaterialEntriesHandler(IMapper mapper, IClassificationUnitOfWork classificationUnitOfWork) : base(mapper)
    {
        _classificationUnitOfWork = classificationUnitOfWork;
    }

    public async Task<GetMaterialEntriesResponse> Handle(GetMaterialEntriesRequest request, CancellationToken cancellationToken)
    {
        var filters = GetPredicate(request);

        var entities = (await _classificationUnitOfWork.MaterialEntryRepository
            .GetAllWithIncludesAsync(
                predicate: filters,
                cancellationToken: cancellationToken
            ));

        var materialEntryDtos = Mapper.Map<IEnumerable<MaterialEntryResponseDto>>(entities);

        return new GetMaterialEntriesResponse(materialEntryDtos);
    }

    private static Expression<Func<MaterialEntry, bool>> GetPredicate(GetMaterialEntriesRequest request)
    {
        return me =>
            me.Date >= request.FromDate
            && me.Date <= request.ToDate
            && (string.IsNullOrEmpty(request.LicensePlate) || me.LicensePlate.Equals(request.LicensePlate))
            && (string.IsNullOrEmpty(request.Route) || me.Route.Equals(request.Route))
            && (string.IsNullOrEmpty(request.ClientName) || me.Details.Any(x => x.Client.ToLower().Equals(request.ClientName.ToLower())));
    }
}