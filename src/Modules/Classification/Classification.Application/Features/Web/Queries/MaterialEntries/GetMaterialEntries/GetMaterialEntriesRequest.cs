using MediatR;

namespace Classification.Application.Features.Web.Queries.MaterialEntries.GetMaterialEntries;

public record GetMaterialEntriesRequest : IRequest<GetMaterialEntriesResponse>
{
    /// <summary>
    /// Fecha desde
    /// </summary>
    /// <example>2024-01-04 12:30:51</example>
    public DateTime? FromDate { get; set; }
    
    /// <summary>
    /// Fecha hasta
    /// </summary>
    /// <example>2024-01-05 12:30:51</example>
    public DateTime? ToDate { get; set; }
    
    /// <summary>
    /// Placa del vehículo
    /// </summary>
    /// <example>ABC123</example>
    public string? LicensePlate { get; set; }
    
    /// <summary>
    /// Nombre del cliente
    /// </summary>
    /// <example>John <PERSON></example>
    public string? ClientName { get; set; }
    
    /// <summary>
    /// Ruta
    /// </summary>
    /// <example>1</example>
    public string? Route { get; set; }
}