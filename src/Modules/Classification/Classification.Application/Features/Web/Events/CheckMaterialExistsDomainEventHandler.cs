using Classification.Domain.Entities;
using Common.Domain.Events;
using Orion.SharedKernel.Application.Exceptions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Classification.Application.Features.Web.Events
{
    public class CheckMaterialExistsDomainEventHandler : INotificationHandler<CheckMaterialExistsDomainEvent>
    {

        private readonly IClassificationUnitOfWork _classificationUnitOfWork;

        public CheckMaterialExistsDomainEventHandler(IClassificationUnitOfWork classificationUnitOfWork)
        {
            _classificationUnitOfWork = classificationUnitOfWork;
        }

        public async Task Handle(CheckMaterialExistsDomainEvent notification, CancellationToken cancellationToken)
        {
            MaterialClassificationDetail materialClassificationDetail = await _classificationUnitOfWork
                .MaterialClassificationDetailRepository
                .GetSingleAsync(predicate: e => e.MaterialTypeId == notification.Id, cancellationToken: cancellationToken);

            if (materialClassificationDetail != null)
            {
                throw new OrionException(_classificationUnitOfWork.ErrorService.GenerateError(new MaterialTypeIsInClassificationDetail(notification.Id, materialClassificationDetail.Id)));
            }
        }
    }
}
