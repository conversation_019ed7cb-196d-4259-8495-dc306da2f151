using Classification.Domain.Constants;

namespace Classification.Application.Features.Web.Commands.MaterialEntries.DeleteMaterialEntry
{
    public class DeleteMaterialEntryValidator : AbstractValidator<DeleteMaterialEntryRequest>
    {
        public DeleteMaterialEntryValidator()
        {
            RuleFor(e => e.Id)
                .NotNull()
                .WithMessage(e => string.Format(ValidationErrorMessage.NotNull, nameof(e.Id)))
                .GreaterThan(0)
                .WithMessage(e => string.Format(ValidationErrorMessage.GreaterThan, nameof(e.Id), 0));
        }
    }
}
