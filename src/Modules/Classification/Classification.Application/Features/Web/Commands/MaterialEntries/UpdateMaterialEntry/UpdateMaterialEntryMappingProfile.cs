using Classification.Application.Features.Web.Commands.MaterialEntries.UpdateMaterialEntry.DTOs;
using Classification.Domain.Entities;

namespace Classification.Application.Features.Web.Commands.MaterialEntries.UpdateMaterialEntry;

public class UpdateMaterialEntryMappingProfile : Profile
{
    public UpdateMaterialEntryMappingProfile()
    {
        CreateMap<CreateSackDto, Sack>()
            .ForMember(dest => dest.MaterialEntryDetailId, opts => opts.MapFrom(src => src.MaterialEntryDetailId))
            .ForMember(dest => dest.PresentationId, opts => opts.MapFrom(src => src.PresentationId))
            .ForMember(dest => dest.Quantity, opts => opts.MapFrom(src => src.Quantity));
	}
}