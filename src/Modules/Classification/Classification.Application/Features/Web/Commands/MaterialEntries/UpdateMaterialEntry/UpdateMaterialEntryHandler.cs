using Classification.Application.Features.Errors;
using Classification.Application.Features.Web.Commands.MaterialEntries.UpdateMaterialEntry.DTOs;
using Classification.Domain.Constants;
using Classification.Domain.Entities;
using Common.Application.Services.Http.Orion;
using Common.Domain.Entities.Http.Orion.Request;
using Common.Domain.Entities.Http.Orion.Response;
using Orion.SharedKernel.Application.Common.Mapping;
using Orion.SharedKernel.Application.Common.Services;
using Orion.SharedKernel.Application.Exceptions;

namespace Classification.Application.Features.Web.Commands.MaterialEntries.UpdateMaterialEntry;

internal sealed class UpdateMaterialEntryHandler : MappingService,
    IRequestHandler<UpdateMaterialEntryRequest>
{
    private readonly IClassificationUnitOfWork _classificationUnitOfWork;
    private readonly IOrionApiService _orionApiService;
    private readonly DateTime _dateTimeNow;

    public UpdateMaterialEntryHandler(IClassificationUnitOfWork classificationUnitOfWork,
        IDateTimeProvider dateTimeProvider,
        IOrionApiService orionApiService,
        IMapper mapper) : base(mapper)
    {
        _classificationUnitOfWork = classificationUnitOfWork;
        _dateTimeNow = dateTimeProvider.UtcNow;
        _orionApiService = orionApiService;
    }

    public async Task<Unit> Handle(UpdateMaterialEntryRequest request, CancellationToken cancellationToken)
    {
        MaterialEntry materialEntry = await GetMaterialEntry(request, cancellationToken);

        await VerifyConstraintsAsync(request, materialEntry, cancellationToken);

        await _classificationUnitOfWork.BeginTransactionAsync(cancellationToken);
        try
        {
            await ProcessTransactionAsync(request, materialEntry, cancellationToken);

            await _classificationUnitOfWork.CommitAsync(cancellationToken);

            return Unit.Value;
        }
        catch (Exception)
        {
            throw;
        }
    }

    private async Task<MaterialEntry> GetMaterialEntry(UpdateMaterialEntryRequest request, CancellationToken cancellationToken)
    {
        return (await _classificationUnitOfWork
                    .MaterialEntryRepository
                    .GetAllWithIncludesAsync(predicate: me => me.Id == request.Id, cancellationToken))
                    .FirstOrDefault()
                ?? throw new OrionException(new MaterialEntryNotFound(request.Id));
    }

    private async Task VerifyConstraintsAsync(UpdateMaterialEntryRequest request, MaterialEntry materialEntry, CancellationToken cancellationToken)
    {
        VerifyUpdateFromMobile(request, materialEntry);

        VerifySacksToDelete(request, materialEntry);

        VerifyDetailsToUpdate(request, materialEntry);

        await VerifySacksToAdd(request, materialEntry, cancellationToken);
    }

    private async Task VerifySacksToAdd(UpdateMaterialEntryRequest request, MaterialEntry materialEntry, CancellationToken cancellationToken)
    {
        IEnumerable<int> presentationsIdsFromRequest = request?.SacksToAdd?.Select(e => e.PresentationId);

        if (presentationsIdsFromRequest?.Any() is true)
        {
            var existentPresentationTypes = await _orionApiService.GetPresentationTypes(cancellationToken);

            CheckPresentationExists(presentationsIdsFromRequest, existentPresentationTypes, cancellationToken);
        }

        IEnumerable<int> detailsIdsFromRequest = request?.SacksToAdd?.Select(e => e.MaterialEntryDetailId);
        if (detailsIdsFromRequest?.Any() is true)
        {
            CheckDetailExits(materialEntry, detailsIdsFromRequest);
        }
    }

    private static void CheckDetailExits(MaterialEntry materialEntry, IEnumerable<int> detailsIdsFromRequest)
    {
        IEnumerable<int> detailsIdsThatNoExistToAddSacks = detailsIdsFromRequest
                        .Except(materialEntry?.Details?.Select(e => e.Id));

        if (detailsIdsThatNoExistToAddSacks.Any())
        {
            throw new OrionException(new MaterialEntryDetailNotFound(detailsIdsThatNoExistToAddSacks));
        }
    }

    private static void CheckPresentationExists(IEnumerable<int> presentationsIdsFromRequest, IEnumerable<DomainValue> domainValues, CancellationToken cancellationToken)
    {
        var presentationsIdsThatNotExistInMaterialEntry = presentationsIdsFromRequest.Where(pid => !domainValues.Any(dv => dv.Id == pid));

        if (presentationsIdsThatNotExistInMaterialEntry?.Any() is true)
        {
            throw new OrionException(new NonExistentSacksPresentation(presentationsIdsThatNotExistInMaterialEntry));
        }
    }

    private static void VerifyDetailsToUpdate(UpdateMaterialEntryRequest request, MaterialEntry materialEntry)
    {
        CheckDetailsExistInMaterialEntry(request, materialEntry);

        CheckSackExistInMaterialEntry(request, materialEntry);
    }

    private static void CheckSackExistInMaterialEntry(UpdateMaterialEntryRequest request, MaterialEntry materialEntry)
    {
        IEnumerable<int> sacksIdsThatNoExistInMaterialEntry = request?.SacksToUpdate?
                    .Select(s => s.Id.Value)
                    .Except(materialEntry.Details?.SelectMany(d => d.Sacks.Select(e => e.Id)));

        if (sacksIdsThatNoExistInMaterialEntry?.Any() is true)
        {
            throw new OrionException(new SackNotFoundError(sacksIdsThatNoExistInMaterialEntry));
        }
    }

    private static void CheckDetailsExistInMaterialEntry(UpdateMaterialEntryRequest request, MaterialEntry materialEntry)
    {
        IEnumerable<int?> detailsIdsThatNoExistInMaterialEntry = request?.DetailsToUpdate?
                            .Select(d => d?.Id)?
                            .Except(materialEntry?.Details?.Select(d => d?.Id));

        if (detailsIdsThatNoExistInMaterialEntry?.Any() is true)
        {
            throw new OrionException(new MaterialEntryDetailNotFound(detailsIdsThatNoExistInMaterialEntry.Select(d => d.Value)));
        }
    }

    private static void VerifySacksToDelete(UpdateMaterialEntryRequest request, MaterialEntry materialEntry)
    {
        if (request?.SackIdsToBeRemoved?.Any() is true)
        {
            CheckSackExistsInMaterialEntry(request, materialEntry);
        }

        if (request?.SackIdsToBeRemoved is not null && request?.SacksToUpdate is not null)
        {
            CheckSacksConflictExist(request);
        }
    }

    private static void CheckSacksConflictExist(UpdateMaterialEntryRequest request)
    {
        IEnumerable<int> conflictSackIds = request.SacksToUpdate
            .Where(sack => request.SackIdsToBeRemoved.Any(idToRemove => idToRemove == sack.Id))
            .Select(sack => sack.Id.Value);

        if (conflictSackIds.Any())
        {
            throw new OrionException(new SackUpdateAndDeleteConflictError(conflictSackIds));
        }
    }

    private static void CheckSackExistsInMaterialEntry(UpdateMaterialEntryRequest request, MaterialEntry materialEntry)
    {
        IEnumerable<int> sackIdsThatNoExistInMaterialEntryDetail = request.SackIdsToBeRemoved
                        .Except(materialEntry.Details.SelectMany(e => e.Sacks.Select(e => e.Id)));

        if (sackIdsThatNoExistInMaterialEntryDetail.Any())
        {
            throw new OrionException(new SackNotFoundError(sackIdsThatNoExistInMaterialEntryDetail));
        }
    }

    private void VerifyUpdateFromMobile(UpdateMaterialEntryRequest request, MaterialEntry materialEntry)
    {
        if (VerifyMobileRules(request, materialEntry))
        {
            throw new OrionException(new MaterialEntryUpdateErrorFromMobile(request.Id));
        }
    }

    private bool VerifyMobileRules(UpdateMaterialEntryRequest request, MaterialEntry materialEntry)
    {
        OriginType originType = Enum.Parse<OriginType>(request.OriginType);

        return originType.Equals(OriginType.App)
                        && (_dateTimeNow - materialEntry.Date).TotalDays > 7;
    }

    private async Task ProcessTransactionAsync(UpdateMaterialEntryRequest request, MaterialEntry materialEntry, CancellationToken cancellationToken)
    {
        UpdateDetails(request, materialEntry);

        await UpdateSacks(request, materialEntry, cancellationToken);

        DeleteSacks(request, materialEntry);

        await AddNewSacks(request, cancellationToken);

        await _classificationUnitOfWork.SaveChangesAsync(cancellationToken);
    }

    private void UpdateDetails(UpdateMaterialEntryRequest request, MaterialEntry materialEntry)
    {
        if (request?.DetailsToUpdate?.Any() is not true) return;

        foreach (MaterialEntryDetailDto item in request?.DetailsToUpdate)
        {
            materialEntry.UpdateDetail(item.Id, item.Client);
        }

        _classificationUnitOfWork.MaterialEntryRepository.Update(materialEntry);
    }

    private async Task UpdateSacks(UpdateMaterialEntryRequest request, MaterialEntry materialEntry, CancellationToken cancellationToken)
    {
        if (request?.SacksToUpdate?.Any() is not true)
        {
            return;
        }

        IEnumerable<Sack> sacksToUpdate = materialEntry
            .Details
            .SelectMany(d => d.Sacks)
            .Where(s => request.SacksToUpdate.Select(s => s.Id).Contains(s.Id));

        if (sacksToUpdate?.Any() is not true)
        {
            return;
        }

        foreach (Sack sack in sacksToUpdate)
        {
            sack.Update(request.SacksToUpdate.FirstOrDefault(s => s.Id == sack.Id).Quantity.Value);
        }

        await _classificationUnitOfWork.SackRepository.UpsertRangeAsync(sacksToUpdate, cancellationToken);
    }

    private void DeleteSacks(UpdateMaterialEntryRequest request, MaterialEntry materialEntry)
    {
        if (request?.SackIdsToBeRemoved?.Any() is not true)
        {
            return;
        }

        IEnumerable<Sack> sacksToDelete = materialEntry.Details
            .SelectMany(d => d.Sacks)
            .Where(s => request.SackIdsToBeRemoved.Contains(s.Id));

        if (sacksToDelete?.Any() is true)
        {
            _classificationUnitOfWork.SackRepository.DeleteRange(sacksToDelete);
        }
    }

    private async Task AddNewSacks(UpdateMaterialEntryRequest request, CancellationToken cancellationToken)
    {
        if (request?.SacksToAdd?.Any() is not true)
        {
            return;
        }

        IEnumerable<Sack> sacksToAdd = Mapper.Map<IEnumerable<Sack>>(request.SacksToAdd);

        await _classificationUnitOfWork.SackRepository.AddRangeAsync(sacksToAdd, cancellationToken);
    }
}