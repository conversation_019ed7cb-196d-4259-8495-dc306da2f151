using Classification.Domain.Constants;
using Shared.Application.Common.ValidationExtensions;

namespace Classification.Application.Features.Web.Commands.MaterialEntries.UpdateMaterialEntry;

public class UpdateMaterialEntryValidator : AbstractValidator<UpdateMaterialEntryRequest>
{
	public UpdateMaterialEntryValidator()
	{
		RuleFor(r => r.Id)
			.GreaterThan(0)
				.WithMessage("El campo Id debe ser mayor a 0.");

		RuleFor(r => r.OriginType)
			.IsValidEnum(typeof(OriginType))
					.WithMessage("El tipo de origen del ingreso no es válido");

		When(r => r?.DetailsToUpdate?.Any() is true, () =>
		{
			RuleForEach(r => r.DetailsToUpdate)
				.ChildRules(request =>
				{
					request.RuleFor(s => s.Id)
						.NotNull()
							.WithMessage("El campo Id de Saco es requerido");

					request.RuleFor(s => s.Client)
						.NotNull()
							.WithMessage("El campo Cliente es requerido.");
				});
		});

		When(r => r?.SackIdsToBeRemoved?.Any() is true, () =>
		{
			RuleFor(r => r.SackIdsToBeRemoved)
				.Must(r => r.All(id => id > 0))
					.WithMessage(r => "Los ids de los sacos a eliminar deben ser mayores a 0.");
		});

		When(r => r?.SacksToUpdate?.Any() is true, () =>
		{
			RuleForEach(r => r.SacksToUpdate)
				.ChildRules(validator =>
				{
					validator.RuleFor(s => s.Id)
						.NotNull()
							.WithMessage("El campo 'Id de Saco' es requerido al actualizar.");

					validator.RuleFor(s => s.Quantity)
						.NotNull()
							.WithMessage("El campo 'Cantidad' del saco es requerido actualizar.")
						.GreaterThan(0)
							.WithMessage("El campo 'Cantidad' del saco debe ser mayor a 0 al actualizar.");
				});
		});

		When(r => r?.SacksToAdd?.Any() is true, () =>
		{
			RuleForEach(r => r.SacksToAdd)
				.ChildRules(validator =>
				{
					validator.RuleFor(r => r.Quantity)
						.NotNull()
							.WithMessage("El campo 'Cantidad' del saco es requerido al crear.")
						.GreaterThan(0)
							.WithMessage("El campo 'Cantidad' del saco debe ser mayor a 0 al crear.");

					validator.RuleFor(r => r.PresentationId)
						.NotNull()
							.WithMessage("El campo 'Id de presentación' del saco es requerido al crear.")
						.GreaterThan(0)
							.WithMessage("El campo 'Id de presentación' del saco debe ser mayor a 0 al crear.");

					validator.RuleFor(r => r.MaterialEntryDetailId)
						.NotNull()
							.WithMessage("El campo 'Id del detalle de material de entrada' del saco es requerido al crear.")
						.GreaterThan(0)
							.WithMessage("El campo 'Id del detalle de material de entrada' del saco debe ser mayor a 0 al crear.");
				});
		});
	}
}