using Classification.Domain.Constants;
using FluentValidation;
using Shared.Application.Common.ValidationExtensions;

namespace Classification.Application.Features.Web.Commands.MaterialEntries.CreateMaterialEntry;

public class CreateMaterialEntryValidator : AbstractValidator<CreateMaterialEntryRequest>
{
    public CreateMaterialEntryValidator()
    {
        RuleFor(m => m.MaterialEntryRequest)
            .ChildRules(materialEntryRequest =>
            {
                materialEntryRequest.RuleFor(m => m.EntryDate)
                    .NotEmpty()
                    .WithMessage("La fecha de ingreso es requerida.")
                    .IsValidDateWithTime()
                    .WithMessage("La fecha de ingreso no es válida. Debe tener el formato 'yyyy-MM-dd HH:mm:ss'.");

                materialEntryRequest.RuleFor(m => m.Responsible)
                    .NotEmpty()
                    .WithMessage("El responsable es requerido.");
                
                materialEntryRequest.RuleFor(m => m.LicensePlate)
                    .NotEmpty()
                    .WithMessage("La placa del vehiculo no puede estar vacía")
                    .MinimumLength(5)
                    .WithMessage("La placa del vehiculo no puede tener menos de 5 caracteres");
                
                materialEntryRequest.RuleFor(m => m.Route)
                    .NotEmpty()
                    .WithMessage("La ruta no puede estar vacía");
                
                materialEntryRequest.RuleFor(m => m.Details)
                    .NotEmpty()
                    .WithMessage("Debe ingresar al menos un detalle de ingreso.");
                
                materialEntryRequest.RuleFor(m => m.Origin)
                    .IsValidEnum(typeof(OriginType))
                    .WithMessage("El tipo de origen del ingreso no es válido");
                
                materialEntryRequest.RuleForEach(m => m.Details)
                    .ChildRules(materialEntryDetailRequest =>
                    {
                        materialEntryDetailRequest.RuleFor(m => m.Client)
                            .NotEmpty()
                            .WithMessage("El cliente es requerido.");
                        
                        materialEntryDetailRequest.RuleFor(m => m.Sacks)
                            .NotEmpty()
                            .WithMessage("Debe ingresar al menos un saco.");
                        
                        materialEntryDetailRequest.RuleForEach(m => m.Sacks)
                            .ChildRules(sack =>
                            {
                                sack.RuleFor(m => m.Quantity)
                                    .NotEmpty()
                                    .WithMessage("El valor del saco es requerido.")
                                    .GreaterThan(0)
                                    .WithMessage("El valor del saco debe ser mayor a 0.");
                                
                                sack.RuleFor(m => m.PresentationId)
                                    .NotEmpty()
                                    .WithMessage("La presentación del saco es requerida.");
                            });
                    });
            });
    }
}