using Orion.SharedKernel.Domain.Entities.Error;
using Orion.SharedKernel.Domain.Entities.Loggers;
using System.Net;

namespace Classification.Application.Features.Errors;

public class MaterialEntryUpdateErrorFromMobile : Error
{
	public MaterialEntryUpdateErrorFromMobile(int id)
	{
		Title = "Error al editar la entrada de material desde la app.";
		Messages = new List<string> { $"La entrada de material con id: {id} no puede ser editada porque han pasado más de 7 días desde su creación." };
		Status = HttpStatusCode.BadRequest;
		Platform = PlatformApp.Mobile;
		ErrorCode = LevelErrorCode.Error;
	}
}

public class MaterialEntryNotFound : Error
{
	public MaterialEntryNotFound(int id)
	{
		Title = "No se encontró el ingreso de material.";
		Messages = new List<string> { $"No se encontró el ingreso de material con id: {id}." };
		Status = HttpStatusCode.BadRequest;
		Platform = PlatformApp.Web;
		ErrorCode = LevelErrorCode.Error;
	}
}