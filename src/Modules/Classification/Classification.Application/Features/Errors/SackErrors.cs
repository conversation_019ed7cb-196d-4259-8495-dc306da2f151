using Orion.SharedKernel.Domain.Entities.Error;
using Orion.SharedKernel.Domain.Entities.Loggers;
using System.Net;

namespace Classification.Application.Features.Errors;

public class SackUpdateAndDeleteConflictError : Error
{
	public SackUpdateAndDeleteConflictError(IEnumerable<int> ids)
	{
		Title = "No se puede actualizar y eliminar bolsones al mismo tiempo.";
		Messages = new List<string>
		{
			$"No se puede realizar la operación solicitada en el saco al mismo tiempo para los siguientes ids: '{string.Join(", ", ids)}'."
		};
		Status = HttpStatusCode.Conflict;
		Platform = PlatformApp.Api;
		ErrorCode = LevelErrorCode.Error;
	}
}

public class SackNotFoundError : Error
{
	public SackNotFoundError(int id)
	{
		Title = "No se encontró el bolsón.";
		Messages = new List<string> { $"No se encontró el bolsón con id: '{id}'." };
		Status = HttpStatusCode.NotFound;
		Platform = PlatformApp.Api;
		ErrorCode = LevelErrorCode.Error;
	}

	public SackNotFoundError(IEnumerable<int> ids)
    {
		Title = "No se encontraron algunos bolsones.";
		Messages = new List<string> { $"No se encontraron los siguientes bolsones con ids: '{string.Join(", ", ids)}'." };
		Status = HttpStatusCode.NotFound;
		Platform = PlatformApp.Api;
		ErrorCode = LevelErrorCode.Error;
	}
}