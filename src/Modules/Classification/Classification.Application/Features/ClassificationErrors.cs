using System.Net;
using Orion.SharedKernel.Domain.Entities.Error;
using Orion.SharedKernel.Domain.Entities.Loggers;

namespace Classification.Application.Features;

public class MaterialEntryAlreadyExists : Error
{
    public MaterialEntryAlreadyExists(string route, DateTime dateTime)
    {
        Title = "Ingreso de material ya existente.";
        Messages = new List<string>
        {
            $"No se puede tener dos ingresos para la misma combinacion de Ruta y Fecha. Ruta: {route} - Fecha: {dateTime:dd/MM/yyyy HH:mm:ss}"
        };
        Status = HttpStatusCode.Conflict;
        Platform = PlatformApp.Web;
        ErrorCode = LevelErrorCode.Information;
    }
}

public class NonExistentSacksPresentation : Error
{
    public NonExistentSacksPresentation(IEnumerable<int> presentationIds)
    {
        Title = "No se encontraron todas las presentaciones ingresadas de los bolsones.";
		Messages = new List<string> { $"No se encontraron las presentaciones con Ids: '{string.Join(", ", presentationIds)}'" };
        Status = HttpStatusCode.NotFound;
        Platform = PlatformApp.Web;
        ErrorCode = LevelErrorCode.Information;
    }
}

public class MaterialTypeIsInClassificationDetail : Error
{
    public MaterialTypeIsInClassificationDetail(int id, int classificationDetailId)
    {
        Title = "El tipo de material está en una clasificación.";
        Messages = new List<string> { $"El tipo de material con id '{id}' se encuentra en el detalle de clasificación con id '{classificationDetailId}'." };
        Status = HttpStatusCode.Conflict;
        Platform = PlatformApp.Web;
        ErrorCode = LevelErrorCode.Information;
    }
}