using System.Globalization;

namespace Classification.Application.Common.Extensions;

internal static class DateTimeExtensions
{
    private const string DateFormat = "yyyy-MM-dd";
    private const string TimeFormat = "HH:mm:ss";
    private const string DateTimeFormat = $"{DateFormat} {TimeFormat}";
    
    public static string ToStandardDateTimeString(this DateTime dateTime) => 
        dateTime.ToString(DateTimeFormat);

    public static DateTime ToStandardDateTime(this string dateTimeString) => 
        DateTime.ParseExact(dateTimeString, DateTimeFormat, CultureInfo.InvariantCulture);
    
    public static DateTime StartOfWeek(this DateTime date) => date.AddDays(-(int)date.DayOfWeek + (int)DayOfWeek.Monday);
    
    public static DateTime EndOfWeek(this DateTime date) => date.StartOfWeek().AddDays(6);
}