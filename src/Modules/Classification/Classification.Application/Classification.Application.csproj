<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>disable</Nullable>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
      <DocumentationFile>obj\Debug\net6.0\Classification.Application.xml</DocumentationFile>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
      <DocumentationFile>obj\Release\net6.0\Classification.Application.xml</DocumentationFile>
    </PropertyGroup>

	<PropertyGroup>
		<NoWarn>$(NoWarn);CS1591</NoWarn>
	</PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\Common\Common.Application\Common.Application.csproj" />
      <ProjectReference Include="..\Classification.Domain\Classification.Domain.csproj" />
    </ItemGroup>

</Project>
