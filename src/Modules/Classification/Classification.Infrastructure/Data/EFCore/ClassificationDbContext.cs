using Classification.Domain.Entities;
using Classification.Infrastructure.Data.EFCore.Configurations.PostgreSQL;
using Common.Domain.Constants;
using Common.Domain.Entities;
using Common.Infrastructure.Data.EFCore.Configurations.PostgreSQL;
using Grpc.Core;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Orion.SharedKernel.Infrastructure.Data.EFCore;
using Orion.SharedKernel.Infrastructure.Data.EFCore.Audit;

namespace Classification.Infrastructure.Data.EFCore;

public sealed class ClassificationDbContext : OrionDbContext
{
    public DbSet<Sack> Sacks { get; set; }
    public DbSet<MaterialEntry> MaterialEntries { get; set; }
    public DbSet<MaterialEntryDetail> MaterialEntryDetails { get; set; }
    public DbSet<MaterialClassification> MaterialClassifications { get; set; }
    public DbSet<MaterialClassificationDetail> MaterialClassificationDetails { get; set; }

    public ClassificationDbContext()
    {
        Sacks = Set<Sack>();
        MaterialEntries = Set<MaterialEntry>();
        MaterialEntryDetails = Set<MaterialEntryDetail>();
        MaterialClassifications = Set<MaterialClassification>();
        MaterialClassificationDetails = Set<MaterialClassificationDetail>();
    }
    
    public ClassificationDbContext(
        DbContextOptions<ClassificationDbContext> options,
        IConfiguration configuration,
        IMediator mediator,
        AuditableEntitySaveChangesInterceptor auditableEntitySaveChangesInterceptor)
        : base(options, configuration, mediator, auditableEntitySaveChangesInterceptor)
    {
        Sacks = Set<Sack>();
        MaterialEntries = Set<MaterialEntry>();
        MaterialEntryDetails = Set<MaterialEntryDetail>();
        MaterialClassifications = Set<MaterialClassification>();
        MaterialClassificationDetails = Set<MaterialClassificationDetail>();
    }
    
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        ModelConfiguration(modelBuilder);
    }
    
    private static void ModelConfiguration(ModelBuilder modelBuilder)
    {
        modelBuilder.ApplyConfiguration(new SackConfiguration());
        modelBuilder.ApplyConfiguration(new MaterialEntryConfiguration());
        modelBuilder.ApplyConfiguration(new MaterialEntryDetailConfiguration());
        modelBuilder.ApplyConfiguration(new MaterialClassificationConfiguration());
        modelBuilder.ApplyConfiguration(new MaterialClassificationDetailConfiguration());

        ExternalModulesConfiguration(modelBuilder);
    }

    private static void ExternalModulesConfiguration(ModelBuilder modelBuilder)
    {
        modelBuilder.ApplyConfiguration(new MaterialTypeConfiguration());
        //modelBuilder.Entity<MaterialType>().ToTable(CommonTableNames.MaterialType, e => e.ExcludeFromMigrations());
    }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        optionsBuilder.UseNpgsql();

        base.OnConfiguring(optionsBuilder);
    }
}