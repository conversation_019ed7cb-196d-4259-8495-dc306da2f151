using Classification.Domain.Constants;
using Classification.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

namespace Classification.Infrastructure.Data.EFCore.Configurations.PostgreSQL;

public class MaterialEntryConfiguration : IEntityTypeConfiguration<MaterialEntry>
{
    private const string DateTimeWithoutTimeZone = "timestamp without time zone";
    
    public void Configure(EntityTypeBuilder<MaterialEntry> builder)
    {
        builder.ToTable(ClassificationTableNames.MaterialEntry);
        
        builder
            .HasKey(me => me.Id)
            .HasName(MaterialEntryColumns.PrimaryKeyConstraintName);
        
        builder
            .Property(me => me.Id)
            .HasColumnName(MaterialEntryColumns.Id)
            .IsRequired();

        builder
            .Property(me => me.Date)
            .HasColumnType(DateTimeWithoutTimeZone)
            .HasColumnName(MaterialEntryColumns.Date)
            .IsRequired();

        builder
            .Property(me => me.Responsible)
            .HasColumnName(MaterialEntryColumns.Responsible)
            .IsRequired();
        
        builder.Property(me => me.LicensePlate)
            .HasColumnName(MaterialEntryColumns.LicensePlate)
            .IsRequired();

        builder
            .Property(me => me.Route)
            .HasColumnName(MaterialEntryColumns.Route)
            .IsRequired();
        
        builder
            .Property(me => me.Origin)
            .HasColumnType("text")
            .HasColumnName(MaterialEntryColumns.Origin)
            .HasConversion(new EnumToStringConverter<OriginType>());

		builder.Property(s => s.IsDeleted)
	        .HasDefaultValue(false)
	        .HasColumnName(MaterialEntryColumns.IsDeleted)
	        .IsRequired();

		builder
			.Property(s => s.CreationDate)
			.HasColumnName(MaterialEntryColumns.CreationDate)
			.IsRequired();

		builder
			.Property(s => s.CreatedBy)
			.HasMaxLength(60).HasColumnName(MaterialEntryColumns.CreatedBy)
			.IsRequired();

		builder
			.Property(s => s.LastModificationDate)
			.HasColumnName(MaterialEntryColumns.LastModificationDate)
			.IsRequired(false);

		builder
			.Property(s => s.LastModifiedBy)
			.HasMaxLength(60).HasColumnName(MaterialEntryColumns.LastModifiedBy)
			.IsRequired(false);

		builder
			.Property(s => s.DeletionDate)
			.HasColumnName(MaterialEntryColumns.DeletionDate)
			.IsRequired(false);

		builder
			.Property(s => s.DeletedBy)
			.HasMaxLength(60)
			.HasColumnName(MaterialEntryColumns.DeletedBy)
			.IsRequired(false);

		builder
            .HasMany(me => me.Details)
            .WithOne(med => med.MaterialEntry)
            .HasForeignKey(med => med.MaterialEntryId)
            .HasConstraintName(MaterialEntryDetailColumns.MaterialEntryConstraintName)
            .OnDelete(DeleteBehavior.Cascade);
    }
}