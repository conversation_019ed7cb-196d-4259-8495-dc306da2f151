using Classification.Domain.Constants;
using Classification.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

namespace Classification.Infrastructure.Data.EFCore.Configurations.PostgreSQL;

public class MaterialClassificationConfiguration : IEntityTypeConfiguration<MaterialClassification>
{
	private const string DateTimeWithoutTimeZone = "timestamp without time zone";

	public void Configure(EntityTypeBuilder<MaterialClassification> entity)
	{
		entity.ToTable(ClassificationTableNames.MaterialClassification);

		entity.HasKey(m => m.Id)
			.HasName(MaterialClassificationColumns.PrimaryKeyConstraintName);

		entity.Property(m => m.Id)
			.HasColumnName(MaterialClassificationColumns.Id)
			.ValueGeneratedOnAdd();

		entity.Property(m => m.CompleteState)
			.HasColumnName(MaterialClassificationColumns.CompleteState)
			.HasColumnType("text")
			.HasConversion(new EnumToStringConverter<ClassificationStep>());

		entity.Property(m => m.IsDeleted)
			.HasColumnName(MaterialClassificationColumns.IsDeleted);

		entity.Property(m => m.IsDeleted)
			.HasDefaultValue(false)
			.HasColumnName(MaterialClassificationColumns.IsDeleted)
			.IsRequired();

		entity
			.Property(m => m.CreationDate)
			.HasColumnName(MaterialClassificationColumns.CreationDate)
			.HasColumnType(DateTimeWithoutTimeZone)
			.IsRequired();

		entity
			.Property(m => m.CreatedBy)
			.HasMaxLength(60).HasColumnName(MaterialClassificationColumns.CreatedBy)
			.IsRequired();

		entity
			.Property(m => m.LastModificationDate)
			.HasColumnName(MaterialClassificationColumns.LastModificationDate)
			.HasColumnType(DateTimeWithoutTimeZone)
			.IsRequired(false);

		entity
			.Property(m => m.LastModifiedBy)
			.HasMaxLength(60).HasColumnName(MaterialClassificationColumns.LastModifiedBy)
			.IsRequired(false);

		entity
			.Property(m => m.DeletionDate)
			.HasColumnName(MaterialClassificationColumns.DeletionDate)
			.HasColumnType(DateTimeWithoutTimeZone)
			.IsRequired(false);

		entity
			.Property(m => m.DeletedBy)
			.HasMaxLength(60)
			.HasColumnName(MaterialClassificationColumns.DeletedBy)
			.IsRequired(false);

		entity.HasMany(m => m.Details)
			.WithOne(d => d.MaterialClassification)
			.HasForeignKey(d => d.MaterialClassificationId)
			.HasConstraintName(MaterialClassificationColumns.MaterialClassificationDetailConstraintName);
			
	}
}