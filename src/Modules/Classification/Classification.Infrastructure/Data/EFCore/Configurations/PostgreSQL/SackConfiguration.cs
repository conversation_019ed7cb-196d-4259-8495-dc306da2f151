using Classification.Domain.Constants;
using Classification.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Classification.Infrastructure.Data.EFCore.Configurations.PostgreSQL;

public class SackConfiguration : IEntityTypeConfiguration<Sack>
{
    public void Configure(EntityTypeBuilder<Sack> builder)
    {
        builder.ToTable(ClassificationTableNames.Sack);
        
        builder
            .HasKey(s => s.Id)
            .HasName(SackColumns.PrimaryKeyConstraintName);
        
        builder
            .Property(s => s.Id)
            .HasColumnName(SackColumns.Id)
            .IsRequired();
        
        builder
            .Property(s => s.Quantity)
            .HasColumnName(SackColumns.Quantity)
            .IsRequired();

        builder
            .Property(s => s.PresentationId)
            .HasColumnName(SackColumns.PresentationId)
            .IsRequired();

        builder.Ignore(s => s.Presentation);
            
        builder
            .Property(s => s.MaterialEntryDetailId)
            .HasColumnName(SackColumns.MaterialEntryDetailId)
            .IsRequired();

		builder.Property(s => s.IsDeleted)
			.HasDefaultValue(false)
			.HasColumnName(SackColumns.IsDeleted)
			.IsRequired();

		builder
            .HasOne(s => s.MaterialEntryDetail)
            .WithMany()
            .HasForeignKey(s => s.MaterialEntryDetailId)
            .HasConstraintName(SackColumns.MaterialEntryDetailConstraintName);
	}
}