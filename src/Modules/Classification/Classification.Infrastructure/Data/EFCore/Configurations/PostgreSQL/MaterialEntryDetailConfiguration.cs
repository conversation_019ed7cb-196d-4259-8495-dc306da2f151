using Classification.Domain.Constants;
using Classification.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Classification.Infrastructure.Data.EFCore.Configurations.PostgreSQL;

public class MaterialEntryDetailConfiguration : IEntityTypeConfiguration<MaterialEntryDetail>
{
    public void Configure(EntityTypeBuilder<MaterialEntryDetail> builder)
    {
        builder.ToTable(ClassificationTableNames.MaterialEntryDetail);
        
        builder
            .HasKey(med => med.Id)
            .HasName(MaterialEntryDetailColumns.PrimaryKeyConstraintName);
        
        builder
            .Property(med => med.Id)
            .HasColumnName(MaterialEntryDetailColumns.Id)
            .IsRequired();

        builder
            .Property(med => med.Client)
            .HasColumnName(MaterialEntryDetailColumns.Client)
            .IsRequired();

        builder
            .Property(med => med.MaterialEntryId)
            .HasColumnName(MaterialEntryDetailColumns.MaterialEntryId)
            .IsRequired();

		builder.Property(s => s.IsDeleted)
			.HasDefaultValue(false)
			.HasColumnName(MaterialEntryDetailColumns.IsDeleted)
			.IsRequired();

        builder.Property(med => med.MaterialClassificationId)
            .HasColumnName(MaterialEntryDetailColumns.MaterialClassificationId)
            .IsRequired(false);

		builder.HasMany(med => med.Sacks)
            .WithOne(s => s.MaterialEntryDetail)
            .HasForeignKey(s => s.MaterialEntryDetailId)
            .HasConstraintName(MaterialEntryDetailColumns.SackConstraintName)
		.OnDelete(DeleteBehavior.Cascade);

		builder.HasOne(med => med.MaterialClassification)
			.WithMany(mc => mc.MaterialEntryDetails)
            .HasForeignKey(med => med.MaterialClassificationId)
			.HasConstraintName(MaterialEntryDetailColumns.MaterialClassificationConstraintName);
	}
}