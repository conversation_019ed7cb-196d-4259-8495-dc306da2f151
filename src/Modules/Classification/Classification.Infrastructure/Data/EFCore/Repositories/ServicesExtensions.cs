using Classification.Domain;
using Classification.Domain.Repositories;
using Microsoft.Extensions.DependencyInjection;

namespace Classification.Infrastructure.Data.EFCore.Repositories;

public static class ServicesExtensions
{
    public static IServiceCollection AddClassificationRepositories(this IServiceCollection services)
    {
        services.AddScoped<IClassificationUnitOfWork, ClassificationUnitOfWork>();
        
        #region Repositories

        services.AddScoped<ISackRepository, SackRepository>();
        services.AddScoped<IMaterialEntryRepository, MaterialEntryRepository>();
        services.AddScoped<IMaterialClassificationRepository, MaterialClassificationRepository>();
        services.AddScoped<IMaterialClassificationDetailRepository, MaterialClassificationDetailRepository>();
        #endregion
        
        return services;
    }
}