// <auto-generated />
using System;
using Classification.Infrastructure.Data.EFCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Classification.Infrastructure.Data.EFCore.Migrations
{
    [DbContext(typeof(ClassificationDbContext))]
    [Migration("20240422195703_Remove_FK_DDV")]
    partial class Remove_FK_DDV
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "6.0.22")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("Classification.Domain.Entities.MaterialClassification", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("RECYECA07_Id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("CompleteState")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("RECYECA07_Paso_de_la_Clasificacion");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(60)
                        .HasColumnType("character varying(60)")
                        .HasColumnName("RECYECA07_CreadoPor");

                    b.Property<DateTime>("CreationDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("RECYECA07_FechaDeCreacion");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(60)
                        .HasColumnType("character varying(60)")
                        .HasColumnName("RECYECA07_EliminadoPor");

                    b.Property<DateTime?>("DeletionDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("RECYECA07_FechaDeEliminacion");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("RECYECA07_Eliminado");

                    b.Property<DateTime?>("LastModificationDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("RECYECA07_FechaDeModificacion");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(60)
                        .HasColumnType("character varying(60)")
                        .HasColumnName("RECYECA07_ModificadoPor");

                    b.HasKey("Id")
                        .HasName("Recycling-ECA_07-Clasificacion_de_Material_key");

                    b.ToTable("Recycling-ECA_07-Clasificacion_de_Material", (string)null);
                });

            modelBuilder.Entity("Classification.Domain.Entities.MaterialClassificationDetail", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("RECYECA08_Id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("RECYECA08_Eliminado");

                    b.Property<int>("MaterialClassificationId")
                        .HasColumnType("integer")
                        .HasColumnName("RECYECA08_Id_de_Clasificacion_de_Material");

                    b.Property<int>("MaterialTypeId")
                        .HasColumnType("integer")
                        .HasColumnName("RECYECA08_Id_de_Tipo_de_Material");

                    b.Property<int>("UnitId")
                        .HasColumnType("integer")
                        .HasColumnName("RECYECA08_Id_de_Unidad");

                    b.Property<double>("Value")
                        .HasColumnType("double precision")
                        .HasColumnName("RECYECA08_Valor");

                    b.HasKey("Id")
                        .HasName("Recycling-ECA_08-Detalle_Clasificacion_de_Material_key");

                    b.HasIndex("MaterialClassificationId");

                    b.HasIndex("MaterialTypeId")
                        .IsUnique()
                        .HasDatabaseName("IX_Recycling-ECA_08-Detalle_Clasificacion_de_Material_RECYECA~1");

                    b.ToTable("Recycling-ECA_08-Detalle_Clasificacion_de_Material", (string)null);
                });

            modelBuilder.Entity("Classification.Domain.Entities.MaterialEntry", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("RECYECA04_Id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(60)
                        .HasColumnType("character varying(60)")
                        .HasColumnName("RECYECA04_CreadoPor");

                    b.Property<DateTime>("CreationDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("RECYECA04_FechaDeCreacion");

                    b.Property<DateTime>("Date")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("RECYECA04_Fecha");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(60)
                        .HasColumnType("character varying(60)")
                        .HasColumnName("RECYECA04_EliminadoPor");

                    b.Property<DateTime?>("DeletionDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("RECYECA04_FechaDeEliminacion");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("RECYECA04_Eliminado");

                    b.Property<DateTime?>("LastModificationDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("RECYECA04_FechaDeModificacion");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(60)
                        .HasColumnType("character varying(60)")
                        .HasColumnName("RECYECA04_ModificadoPor");

                    b.Property<string>("LicensePlate")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("RECYECA04_Placa");

                    b.Property<string>("Origin")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("RECYECA04_Origen");

                    b.Property<string>("Responsible")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("RECYECA04_Responsable");

                    b.Property<string>("Route")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("RECYECA04_Ruta");

                    b.HasKey("Id")
                        .HasName("Recycling-ECA_04-Ingreso_de_Material_key");

                    b.ToTable("Recycling-ECA_04-Ingreso_de_Material", (string)null);
                });

            modelBuilder.Entity("Classification.Domain.Entities.MaterialEntryDetail", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("RECYECA05_Id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Client")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("RECYECA05_Cliente");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("RECYECA05_Eliminado");

                    b.Property<int?>("MaterialClassificationId")
                        .HasColumnType("integer")
                        .HasColumnName("RECYECA05_Id_de_Clasificacion_de_Material");

                    b.Property<int>("MaterialEntryId")
                        .HasColumnType("integer")
                        .HasColumnName("RECYECA05_Ingreso_de_Material");

                    b.HasKey("Id")
                        .HasName("Recycling-ECA_05-Detalle_Ingreso_de_Material_key");

                    b.HasIndex("MaterialClassificationId");

                    b.HasIndex("MaterialEntryId");

                    b.ToTable("Recycling-ECA_05-Detalle_Ingreso_de_Material", (string)null);
                });

            modelBuilder.Entity("Classification.Domain.Entities.Sack", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("RECYECA06_Id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("RECYECA06_Eliminado");

                    b.Property<int>("MaterialEntryDetailId")
                        .HasColumnType("integer")
                        .HasColumnName("RECYECA06_Detalle_Ingreso_de_Material");

                    b.Property<int>("PresentationId")
                        .HasColumnType("integer")
                        .HasColumnName("RECYECA06_Presentacion");

                    b.Property<int>("Quantity")
                        .HasColumnType("integer")
                        .HasColumnName("RECYECA06_Cantidad");

                    b.HasKey("Id")
                        .HasName("Recycling-ECA_06-Bolson_key");

                    b.HasIndex("MaterialEntryDetailId");

                    b.ToTable("Recycling-ECA_06-Bolson", (string)null);
                });

            modelBuilder.Entity("Common.Domain.Entities.MaterialType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("RECYECA01_Id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(60)
                        .HasColumnType("character varying(60)")
                        .HasColumnName("RECYECA01_CreadoPor");

                    b.Property<DateTime>("CreationDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("RECYECA01_FechaDeCreacion");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(60)
                        .HasColumnType("character varying(60)")
                        .HasColumnName("RECYECA01_EliminadoPor");

                    b.Property<DateTime?>("DeletionDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("RECYECA01_FechaDeEliminacion");

                    b.Property<string>("Description")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("RECYECA01_Descripcion");

                    b.Property<int>("GroupId")
                        .HasColumnType("integer")
                        .HasColumnName("RECYECA01_Grupo");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("RECYECA01_Eliminado");

                    b.Property<DateTime?>("LastModificationDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("RECYECA01_FechaDeModificacion");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(60)
                        .HasColumnType("character varying(60)")
                        .HasColumnName("RECYECA01_ModificadoPor");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("RECYECA01_Nombre");

                    b.HasKey("Id")
                        .HasName("Recycling-ECA-01_Materiales_key");

                    b.ToTable("Recycling-ECA-01_Materiales", (string)null);
                });

            modelBuilder.Entity("Classification.Domain.Entities.MaterialClassificationDetail", b =>
                {
                    b.HasOne("Classification.Domain.Entities.MaterialClassification", "MaterialClassification")
                        .WithMany("Details")
                        .HasForeignKey("MaterialClassificationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("Recycling-ECA_RECYECA08-ECA_RECYECA07_fkey");

                    b.HasOne("Common.Domain.Entities.MaterialType", "MaterialType")
                        .WithOne()
                        .HasForeignKey("Classification.Domain.Entities.MaterialClassificationDetail", "MaterialTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("Recycling-ECA_RECYECA08-ECA_RECYECA01_fkey");

                    b.Navigation("MaterialClassification");

                    b.Navigation("MaterialType");
                });

            modelBuilder.Entity("Classification.Domain.Entities.MaterialEntryDetail", b =>
                {
                    b.HasOne("Classification.Domain.Entities.MaterialClassification", "MaterialClassification")
                        .WithMany("MaterialEntryDetails")
                        .HasForeignKey("MaterialClassificationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("Recycling-ECA_RECYECA05-RECYECA07_fkey");

                    b.HasOne("Classification.Domain.Entities.MaterialEntry", "MaterialEntry")
                        .WithMany("Details")
                        .HasForeignKey("MaterialEntryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("Recycling-ECA_RECYECA05-RECYECA04_fkey");

                    b.Navigation("MaterialClassification");

                    b.Navigation("MaterialEntry");
                });

            modelBuilder.Entity("Classification.Domain.Entities.Sack", b =>
                {
                    b.HasOne("Classification.Domain.Entities.MaterialEntryDetail", "MaterialEntryDetail")
                        .WithMany("Sacks")
                        .HasForeignKey("MaterialEntryDetailId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("Recycling-ECA_RECYECA05-RECYECA06_fkey");

                    b.Navigation("MaterialEntryDetail");
                });

            modelBuilder.Entity("Classification.Domain.Entities.MaterialClassification", b =>
                {
                    b.Navigation("Details");

                    b.Navigation("MaterialEntryDetails");
                });

            modelBuilder.Entity("Classification.Domain.Entities.MaterialEntry", b =>
                {
                    b.Navigation("Details");
                });

            modelBuilder.Entity("Classification.Domain.Entities.MaterialEntryDetail", b =>
                {
                    b.Navigation("Sacks");
                });
#pragma warning restore 612, 618
        }
    }
}
