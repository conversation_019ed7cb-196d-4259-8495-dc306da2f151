using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Classification.Infrastructure.Data.EFCore.Migrations
{
    public partial class Adds_Audit_For_Sack : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "RECYECA04_CreadoPor",
                table: "Recycling-ECA_06-<PERSON><PERSON>",
                type: "character varying(60)",
                maxLength: 60,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<bool>(
                name: "RECYECA04_Eliminado",
                table: "Recycling-ECA_06-<PERSON><PERSON>",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "RECYECA04_EliminadoPor",
                table: "Recycling-ECA_06-<PERSON><PERSON>",
                type: "character varying(60)",
                maxLength: 60,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "RECYECA04_FechaDeCreacion",
                table: "Recycling-ECA_06-<PERSON><PERSON>",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<DateTime>(
                name: "RECYECA04_FechaDeEliminacion",
                table: "Recycling-ECA_06-Bolson",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "RECYECA04_FechaDeModificacion",
                table: "Recycling-ECA_06-Bolson",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "RECYECA04_ModificadoPor",
                table: "Recycling-ECA_06-Bolson",
                type: "character varying(60)",
                maxLength: 60,
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "RECYECA04_CreadoPor",
                table: "Recycling-ECA_06-Bolson");

            migrationBuilder.DropColumn(
                name: "RECYECA04_Eliminado",
                table: "Recycling-ECA_06-Bolson");

            migrationBuilder.DropColumn(
                name: "RECYECA04_EliminadoPor",
                table: "Recycling-ECA_06-Bolson");

            migrationBuilder.DropColumn(
                name: "RECYECA04_FechaDeCreacion",
                table: "Recycling-ECA_06-Bolson");

            migrationBuilder.DropColumn(
                name: "RECYECA04_FechaDeEliminacion",
                table: "Recycling-ECA_06-Bolson");

            migrationBuilder.DropColumn(
                name: "RECYECA04_FechaDeModificacion",
                table: "Recycling-ECA_06-Bolson");

            migrationBuilder.DropColumn(
                name: "RECYECA04_ModificadoPor",
                table: "Recycling-ECA_06-Bolson");
        }
    }
}
