using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Classification.Infrastructure.Data.EFCore.Migrations
{
    public partial class Add_MaterialEntry_Related_Tables : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Recycling-ECA_04-Ingreso_de_Material",
                columns: table => new
                {
                    RECYECA04_Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    RECYECA04_Fecha = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    RECYECA04_Responsable = table.Column<string>(type: "text", nullable: false),
                    RECYECA04_Placa = table.Column<string>(type: "text", nullable: false),
                    RECYECA04_Ruta = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("Recycling-ECA_04-Ingreso_de_Material_key", x => x.RECYECA04_Id);
                });

            migrationBuilder.CreateTable(
                name: "Recycling-ECA_05-Detalle_Ingreso_de_Material",
                columns: table => new
                {
                    RECYECA05_Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    RECYECA05_Cliente = table.Column<string>(type: "text", nullable: false),
                    RECYECA05_Ingreso_de_Material = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("Recycling-ECA_05-Detalle_Ingreso_de_Material_key", x => x.RECYECA05_Id);
                    table.ForeignKey(
                        name: "Recycling-ECA_RECYECA05-RECYECA04_fkey",
                        column: x => x.RECYECA05_Ingreso_de_Material,
                        principalTable: "Recycling-ECA_04-Ingreso_de_Material",
                        principalColumn: "RECYECA04_Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Recycling-ECA_06-Bolson",
                columns: table => new
                {
                    RECYECA06_Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    RECYECA06_Cantidad = table.Column<int>(type: "integer", nullable: false),
                    RECYECA06_Presentacion = table.Column<int>(type: "integer", nullable: false),
                    RECYECA06_Detalle_Ingreso_de_Material = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("Recycling-ECA_06-Bolson_key", x => x.RECYECA06_Id);
                    table.ForeignKey(
                        name: "Recycling-ECA_RECYECA05-RECYECA06_fkey",
                        column: x => x.RECYECA06_Detalle_Ingreso_de_Material,
                        principalTable: "Recycling-ECA_05-Detalle_Ingreso_de_Material",
                        principalColumn: "RECYECA05_Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "Recycling-ECA_RECYECA06-OrionDomainValue_fkey",
                        column: x => x.RECYECA06_Presentacion,
                        principalTable: "Orion-DomainValue",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Recycling-ECA_05-Detalle_Ingreso_de_Material_RECYECA05_Ingr~",
                table: "Recycling-ECA_05-Detalle_Ingreso_de_Material",
                column: "RECYECA05_Ingreso_de_Material");

            migrationBuilder.CreateIndex(
                name: "IX_Recycling-ECA_06-Bolson_RECYECA06_Detalle_Ingreso_de_Materi~",
                table: "Recycling-ECA_06-Bolson",
                column: "RECYECA06_Detalle_Ingreso_de_Material");

            migrationBuilder.CreateIndex(
                name: "IX_Recycling-ECA_06-Bolson_RECYECA06_Presentacion",
                table: "Recycling-ECA_06-Bolson",
                column: "RECYECA06_Presentacion");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Recycling-ECA_06-Bolson");

            migrationBuilder.DropTable(
                name: "Recycling-ECA_05-Detalle_Ingreso_de_Material");

            migrationBuilder.DropTable(
                name: "Recycling-ECA_04-Ingreso_de_Material");
        }
    }
}
