using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Classification.Infrastructure.Data.EFCore.Migrations
{
    public partial class Change_Name_Property_FromIsDeletedToDeleted_In_The_Table_MaterialClassificationDetail : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "IsDele<PERSON>",
                table: "Recycling-ECA_08-Detalle_Clasificacion_de_Material",
                newName: "RECYECA08_Eliminado");

            migrationBuilder.AlterColumn<bool>(
                name: "RECYECA08_Eliminado",
                table: "Recycling-ECA_08-Detalle_Clasificacion_de_Material",
                type: "boolean",
                nullable: false,
                defaultValue: false,
                oldClrType: typeof(bool),
                oldType: "boolean");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "RECYECA08_Eliminado",
                table: "Recycling-ECA_08-Detalle_Clasificacion_de_Material",
                newName: "IsDeleted");

            migrationBuilder.AlterColumn<bool>(
                name: "IsDele<PERSON>",
                table: "Recycling-ECA_08-Detalle_Clasificacion_de_Material",
                type: "boolean",
                nullable: false,
                oldClrType: typeof(bool),
                oldType: "boolean",
                oldDefaultValue: false);
        }
    }
}
