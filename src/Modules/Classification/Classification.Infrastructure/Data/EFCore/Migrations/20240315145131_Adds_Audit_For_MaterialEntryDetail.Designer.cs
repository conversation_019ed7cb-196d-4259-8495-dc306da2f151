// <auto-generated />
using System;
using Classification.Infrastructure.Data.EFCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Classification.Infrastructure.Data.EFCore.Migrations
{
    [DbContext(typeof(ClassificationDbContext))]
    [Migration("20240315145131_Adds_Audit_For_MaterialEntryDetail")]
    partial class Adds_Audit_For_MaterialEntryDetail
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "6.0.22")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("Classification.Domain.Entities.MaterialEntry", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("RECYECA04_Id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(60)
                        .HasColumnType("character varying(60)")
                        .HasColumnName("RECYECA04_CreadoPor");

                    b.Property<DateTime>("CreationDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("RECYECA04_FechaDeCreacion");

                    b.Property<DateTime>("Date")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("RECYECA04_Fecha");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(60)
                        .HasColumnType("character varying(60)")
                        .HasColumnName("RECYECA04_EliminadoPor");

                    b.Property<DateTime?>("DeletionDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("RECYECA04_FechaDeEliminacion");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("RECYECA04_Eliminado");

                    b.Property<DateTime?>("LastModificationDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("RECYECA04_FechaDeModificacion");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(60)
                        .HasColumnType("character varying(60)")
                        .HasColumnName("RECYECA04_ModificadoPor");

                    b.Property<string>("LicensePlate")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("RECYECA04_Placa");

                    b.Property<string>("Origin")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("RECYECA04_Origen");

                    b.Property<string>("Responsible")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("RECYECA04_Responsable");

                    b.Property<string>("Route")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("RECYECA04_Ruta");

                    b.HasKey("Id")
                        .HasName("Recycling-ECA_04-Ingreso_de_Material_key");

                    b.ToTable("Recycling-ECA_04-Ingreso_de_Material", (string)null);
                });

            modelBuilder.Entity("Classification.Domain.Entities.MaterialEntryDetail", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("RECYECA05_Id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Client")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("RECYECA05_Cliente");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(60)
                        .HasColumnType("character varying(60)")
                        .HasColumnName("RECYECA05_CreadoPor");

                    b.Property<DateTime>("CreationDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("RECYECA05_FechaDeCreacion");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(60)
                        .HasColumnType("character varying(60)")
                        .HasColumnName("RECYECA05_EliminadoPor");

                    b.Property<DateTime?>("DeletionDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("RECYECA05_FechaDeEliminacion");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("RECYECA05_Eliminado");

                    b.Property<DateTime?>("LastModificationDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("RECYECA05_FechaDeModificacion");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(60)
                        .HasColumnType("character varying(60)")
                        .HasColumnName("RECYECA05_ModificadoPor");

                    b.Property<int>("MaterialEntryId")
                        .HasColumnType("integer")
                        .HasColumnName("RECYECA05_Ingreso_de_Material");

                    b.HasKey("Id")
                        .HasName("Recycling-ECA_05-Detalle_Ingreso_de_Material_key");

                    b.HasIndex("MaterialEntryId");

                    b.ToTable("Recycling-ECA_05-Detalle_Ingreso_de_Material", (string)null);
                });

            modelBuilder.Entity("Classification.Domain.Entities.Sack", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("RECYECA06_Id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("MaterialEntryDetailId")
                        .HasColumnType("integer")
                        .HasColumnName("RECYECA06_Detalle_Ingreso_de_Material");

                    b.Property<int>("PresentationId")
                        .HasColumnType("integer")
                        .HasColumnName("RECYECA06_Presentacion");

                    b.Property<int>("Quantity")
                        .HasColumnType("integer")
                        .HasColumnName("RECYECA06_Cantidad");

                    b.HasKey("Id")
                        .HasName("Recycling-ECA_06-Bolson_key");

                    b.HasIndex("MaterialEntryDetailId");

                    b.HasIndex("PresentationId");

                    b.ToTable("Recycling-ECA_06-Bolson", (string)null);
                });

            modelBuilder.Entity("Orion.Core.DDV.Domain.Entities.Domain", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("Description")
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int?>("ParentDomainId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("Orion-Domain", (string)null);
                });

            modelBuilder.Entity("Orion.Core.DDV.Domain.Entities.DomainValue", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Description")
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)");

                    b.Property<int>("DomainId")
                        .HasColumnType("integer");

                    b.Property<int?>("Order")
                        .HasColumnType("integer");

                    b.Property<int?>("ParentDomainValueId")
                        .HasColumnType("integer");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.HasKey("Id");

                    b.HasIndex("DomainId");

                    b.ToTable("Orion-DomainValue", (string)null);
                });

            modelBuilder.Entity("Classification.Domain.Entities.MaterialEntryDetail", b =>
                {
                    b.HasOne("Classification.Domain.Entities.MaterialEntry", "MaterialEntry")
                        .WithMany("Details")
                        .HasForeignKey("MaterialEntryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("Recycling-ECA_RECYECA05-RECYECA04_fkey");

                    b.Navigation("MaterialEntry");
                });

            modelBuilder.Entity("Classification.Domain.Entities.Sack", b =>
                {
                    b.HasOne("Classification.Domain.Entities.MaterialEntryDetail", "MaterialEntryDetail")
                        .WithMany("Sacks")
                        .HasForeignKey("MaterialEntryDetailId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("Recycling-ECA_RECYECA05-RECYECA06_fkey");

                    b.HasOne("Orion.Core.DDV.Domain.Entities.DomainValue", "Presentation")
                        .WithMany()
                        .HasForeignKey("PresentationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("Recycling-ECA_RECYECA06-OrionDomainValue_fkey");

                    b.Navigation("MaterialEntryDetail");

                    b.Navigation("Presentation");
                });

            modelBuilder.Entity("Orion.Core.DDV.Domain.Entities.DomainValue", b =>
                {
                    b.HasOne("Orion.Core.DDV.Domain.Entities.Domain", "Domain")
                        .WithMany("Values")
                        .HasForeignKey("DomainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Domain");
                });

            modelBuilder.Entity("Classification.Domain.Entities.MaterialEntry", b =>
                {
                    b.Navigation("Details");
                });

            modelBuilder.Entity("Classification.Domain.Entities.MaterialEntryDetail", b =>
                {
                    b.Navigation("Sacks");
                });

            modelBuilder.Entity("Orion.Core.DDV.Domain.Entities.Domain", b =>
                {
                    b.Navigation("Values");
                });
#pragma warning restore 612, 618
        }
    }
}
