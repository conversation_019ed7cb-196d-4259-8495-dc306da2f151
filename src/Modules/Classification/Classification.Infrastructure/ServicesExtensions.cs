using Classification.Infrastructure.Data.EFCore;
using Classification.Infrastructure.Data.EFCore.Repositories;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Orion.SharedKernel.Infrastructure.Data.EFCore;
using Orion.SharedKernel.Infrastructure.Data.EFCore.Settings;


namespace Classification.Infrastructure;

public static class ServicesExtensions
{
    public static IServiceCollection AddClassificationInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddEFCorePostgre<ClassificationDbContext>(configuration.GetConnectionString(Providers.PostgreSql)!);
        
        services.AddClassificationRepositories();

        return services;
    }
}